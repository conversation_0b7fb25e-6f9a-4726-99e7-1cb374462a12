import os
import asyncio
import traceback
from typing import Optional
from fastapi import HTTPException, Body, Depends, APIRouter, Path
from pydantic import BaseModel
from sqlalchemy.orm import Session

from backend.db import get_db
from backend.auth.credentials import get_auth_user_email
from backend.auth.JWT_helpers import J<PERSON><PERSON><PERSON><PERSON><PERSON>, decode_JWT
from backend.manager import get_agent_with_token, get_user_id_from_token
from backend.utilities.logger import logger

# Create a router for table operations
table_router = APIRouter(prefix="/api/table", tags=["table"])

# Define request models
class TableOperationRequest(BaseModel):
    action: str  # "delete" or "edit" 
    table_name: str

@table_router.delete("/{table_name}", dependencies=[Depends(JWTBearer())])
async def delete_table(
    table_name: str = Path(..., description="The name of the table to delete"),
    token: str = Depends(JWTBearer())
):
    """
    Delete a table by name using a REST-style DELETE endpoint.
    
    Args:
        table_name: The name of the table to delete (from URL path)
        token: The JWT token (injected by FastAPI)
        
    Returns:
        JSON response with the deletion result
    """
    try:
        # Get the data analyst agent using the token
        data_analyst = get_agent_with_token(token)
        user_id = get_user_id_from_token(token)
        
        # Call the delete_table method we implemented
        success, message = data_analyst.delete_data(table_name)
      
        if not success:
          asyncio.create_task(logger.error(
              component="table_service",
              event_name="delete_table_failed",
              user_id=str(user_id) if user_id else "unknown",
              error={
                  "error_message": message,
              }
          ))
          raise HTTPException(status_code=400, detail=message)
            
        return {"status": "success", "message": message}
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        # Log the error
        asyncio.create_task(logger.error(
            component="table_service",
            event_name="delete_table_exception",
            user_id=str(user_id) if 'user_id' in locals() else "unknown",
            error={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "stack_trace": traceback.format_exc()
            }
        ))
        
        # Return error response
        raise HTTPException(status_code=500, detail=str(e))
