"""Make sure tables.py and real database are in sync

Revision ID: 8774287659de
Revises: ca61d31053fe
Create Date: 2025-03-18 14:54:34.410736

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8774287659de'
down_revision = 'ca61d31053fe'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('utterances')
    op.drop_table('conversations')
    op.drop_index('ix_credentials_access_token', table_name='credentials')
    op.drop_index('ix_credentials_id', table_name='credentials')
    op.drop_table('credentials')
    op.drop_table('users')
    op.drop_table('dialogue_acts')
    op.drop_table('intents')
    op.drop_table('states')
    op.drop_table('agents')
    op.drop_constraint('score_conversation_id_fkey', 'score', type_='foreignkey')
    op.create_foreign_key(None, 'score', 'conversation', ['conversation_id'], ['id'])
    op.drop_constraint('utterance_conversation_id_fkey', 'utterance', type_='foreignkey')
    op.create_foreign_key(None, 'utterance', 'conversation', ['conversation_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('utterance_conversation_id_fkey', 'utterance', type_='foreignkey')
    op.create_foreign_key('utterance_conversation_id_fkey', 'utterance', 'conversation', ['conversation_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint("score_conversation_id_fkey", 'score', type_='foreignkey')
    op.create_foreign_key('score_conversation_id_fkey', 'score', 'conversation', ['conversation_id'], ['id'], ondelete='CASCADE')
    op.create_table('agents',
    sa.Column('agent_id', sa.INTEGER(), server_default=sa.text("nextval('agents_agent_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('use_case', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('agent_id', name='agents_pkey'),
    sa.UniqueConstraint('name', name='agents_name_key'),
    sa.UniqueConstraint('use_case', name='agents_use_case_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('dialogue_acts',
    sa.Column('dact_id', sa.INTEGER(), server_default=sa.text("nextval('dialogue_acts_dact_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('dact', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    sa.Column('dax', sa.VARCHAR(length=4), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('intent_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('agent_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], name='dialogue_acts_agent_id_fkey'),
    sa.ForeignKeyConstraint(['intent_id'], ['intents.intent_id'], name='dialogue_acts_intent_id_fkey'),
    sa.PrimaryKeyConstraint('dact_id', name='dialogue_acts_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('credentials',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('access_token', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('refresh_token', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('token_expiry', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('vendor', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('vendor_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('scope', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('last_sync_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='credentials_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='credentials_pkey')
    )
    op.create_index('ix_credentials_id', 'credentials', ['id'], unique=False)
    op.create_index('ix_credentials_access_token', 'credentials', ['access_token'], unique=False)
    op.create_table('states',
    sa.Column('state_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('table_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('table_cols', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('campaigns', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('channels', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('metric', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('state_id', name='states_pkey')
    )
    op.create_table('intents',
    sa.Column('intent_id', sa.INTEGER(), server_default=sa.text("nextval('intents_intent_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('level', sa.VARCHAR(length=8), autoincrement=False, nullable=False),
    sa.Column('intent_name', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=128), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('intent_id', name='intents_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('utterances',
    sa.Column('utt_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('speaker', sa.VARCHAR(length=8), autoincrement=False, nullable=False),
    sa.Column('text', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('utt_type', sa.VARCHAR(length=32), autoincrement=False, nullable=True),
    sa.Column('convo_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['convo_id'], ['conversations.convo_id'], name='utterances_convo_id_fkey'),
    sa.PrimaryKeyConstraint('utt_id', name='utterances_pkey')
    )
    op.create_table('users',
    sa.Column('user_id', sa.INTEGER(), server_default=sa.text("nextval('users_user_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('first', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('middle', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('last', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('username', sa.VARCHAR(length=32), autoincrement=False, nullable=True),
    sa.Column('_password', sa.VARCHAR(length=60), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('user_id', name='users_pkey'),
    sa.UniqueConstraint('email', name='users_email_key'),
    sa.UniqueConstraint('username', name='users_username_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('conversations',
    sa.Column('convo_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('short_embed', postgresql.ARRAY(sa.Float), autoincrement=False, nullable=False),
    sa.Column('medium_embed', postgresql.ARRAY(sa.Float), autoincrement=False, nullable=True),
    sa.Column('long_embed', postgresql.ARRAY(sa.Float), autoincrement=False, nullable=True),
    sa.Column('start_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('end_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('agent_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('labels', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('dact_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('subject', sa.VARCHAR(length=128), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], name='conversations_agent_id_fkey'),
    sa.ForeignKeyConstraint(['dact_id'], ['dialogue_acts.dact_id'], name='conversations_dact_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='conversations_user_id_fkey'),
    sa.PrimaryKeyConstraint('convo_id', name='conversations_pkey')
    )
    # ### end Alembic commands ###
