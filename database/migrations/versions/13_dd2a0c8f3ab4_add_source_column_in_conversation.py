"""Add source column in Conversation

Revision ID: dd2a0c8f3ab4
Revises: 8774287659de
Create Date: 2025-03-20 15:51:56.082814

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'dd2a0c8f3ab4'
down_revision = '8774287659de'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create enum type first
    source_enum = postgresql.ENUM('synthetic', 'development', 'production', name='source_enum')
    source_enum.create(op.get_bind())
    
    # Then add the column using the enum
    op.add_column('conversation', sa.Column('source', sa.Enum('synthetic', 'development', 'production', name='source_enum'), nullable=True))


def downgrade() -> None:
    # Drop the column first
    op.drop_column('conversation', 'source')
    
    # Then drop the enum type
    source_enum = postgresql.ENUM('synthetic', 'development', 'production', name='source_enum')
    source_enum.drop(op.get_bind())
