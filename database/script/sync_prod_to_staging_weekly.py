#!/usr/bin/env python
"""
<PERSON>ript to sync the production database to staging by:
1. Creating a full backup of the production database
2. Dropping all tables in staging
3. Restoring production data to staging

Run with: python production_to_staging_sync.py
"""

import os
import sys
import subprocess
import datetime
import argparse
import urllib.parse
from dotenv import load_dotenv
from sqlalchemy import create_engine


def load_environment_variables():
    """Load environment variables from .env file"""
    # Load from .env file in parent directory, matching your project structure
    DIRNAME = os.path.dirname(__file__)
    ENV_PATH = os.path.abspath(os.path.join(DIRNAME, "..", ".env"))
    
    if not os.path.exists(ENV_PATH):
        print(f"Error: .env file not found at {ENV_PATH}")
        sys.exit(1)
    
    load_dotenv(ENV_PATH)
    
    # Get connection strings directly, similar to how your env.py does it
    prod_db_dsn = os.getenv("PRODUCTION_DB_DSN")
    staging_db_dsn = os.getenv("STAGING_DB_DSN")
    
    # Validate that all necessary variables are set
    if not prod_db_dsn:
        print("Error: PRODUCTION_DB_DSN is not set in the .env file")
        sys.exit(1)
    
    if not staging_db_dsn:
        print("Error: STAGING_DB_DSN is not set in the .env file")
        sys.exit(1)
    
    # sqlalchemy does not support postgres://, so we replace it
    # This matches your approach in env.py and db.py
    prod_db_dsn = prod_db_dsn.replace("postgres://", "postgresql://")
    staging_db_dsn = staging_db_dsn.replace("postgres://", "postgresql://")
    
    return {
        "prod_db_user": os.getenv("PRODUCTION_DB_USER"),
        "prod_db_password": os.getenv("PRODUCTION_DB_PASSWORD"),
        "prod_db_host": os.getenv("PRODUCTION_DB_HOST"),
        "prod_db_name": os.getenv("PRODUCTION_DB_NAME"),
        "staging_db_user": os.getenv("STAGING_DB_USER"),
        "staging_db_password": os.getenv("STAGING_DB_PASSWORD"),
        "staging_db_host": os.getenv("STAGING_DB_HOST"),
        "staging_db_name": os.getenv("STAGING_DB_NAME"),
        "prod_db_dsn": prod_db_dsn,
        "staging_db_dsn": staging_db_dsn
    }


def create_backup_dir():
    """Create a directory for database backups if it doesn't exist"""
    backup_dir = os.path.join(os.path.dirname(__file__), 'db_backups')
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir


def backup_staging_database(db_vars, backup_dir):
    """Create a backup of the staging database before making changes"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"staging_backup_{timestamp}.sql"
    backup_path = os.path.join(backup_dir, backup_filename)
    
    print(f"Creating backup of staging database to {backup_path}...")
    
    # Set environment variables for pg_dump
    env = os.environ.copy()
    env["PGPASSWORD"] = db_vars["staging_db_password"]
    
    try:
        subprocess.run([
            "pg_dump",
            "-h", db_vars["staging_db_host"],
            "-U", db_vars["staging_db_user"],
            "-d", db_vars["staging_db_name"],
            "-f", backup_path,
            "--clean",
            "--if-exists",
        ], env=env, check=True, capture_output=True, text=True)
        
        print(f"Staging database backup completed successfully")
        return backup_path
    except subprocess.CalledProcessError as e:
        print(f"Error backing up staging database: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)


def backup_production_database(db_vars, backup_dir):
    """Create a backup of the production database"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"production_backup_{timestamp}.sql"
    backup_path = os.path.join(backup_dir, backup_filename)
    
    print(f"Creating backup of production database to {backup_path}...")
    
    # Set environment variables for pg_dump
    env = os.environ.copy()
    env["PGPASSWORD"] = db_vars["prod_db_password"]
    
    try:
        result = subprocess.run([
            "pg_dump",
            "-h", db_vars["prod_db_host"],
            "-U", db_vars["prod_db_user"],
            "-d", db_vars["prod_db_name"],
            "-f", backup_path,
            "--clean",
            "--if-exists",
        ], env=env, check=True, capture_output=True, text=True)
        
        print(f"Production database backup completed successfully")
        return backup_path
    except subprocess.CalledProcessError as e:
        print(f"Error backing up production database: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)


def restore_to_staging(db_vars, backup_path):
    """Restore the production backup to the staging database"""
    print(f"Restoring production data to staging database...")
    
    # Set environment variables for psql
    env = os.environ.copy()
    env["PGPASSWORD"] = db_vars["staging_db_password"]
    
    try:
        result = subprocess.run([
            "psql",
            "-h", db_vars["staging_db_host"],
            "-U", db_vars["staging_db_user"],
            "-d", db_vars["staging_db_name"],
            "-f", backup_path,
        ], env=env, check=True, capture_output=True, text=True)
        
        print("Restore to staging completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error restoring to staging database: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)


def get_db_connection(is_production, db_vars):
    """Create database connection based on environment, as shown in your example"""
    if is_production:
        db_dsn = db_vars["prod_db_dsn"]
    else:
        db_dsn = db_vars["staging_db_dsn"]
    
    return create_engine(db_dsn)


def main():
    parser = argparse.ArgumentParser(description="Sync production database to staging")
    parser.add_argument("--yes", "-y", action="store_true", help="Skip confirmation prompt (use with caution)")
    args = parser.parse_args()
    
    print("Starting database sync process...")
    
    # Load environment variables
    db_vars = load_environment_variables()
    
    # Test connections to ensure we can access both databases
    try:
        prod_engine = get_db_connection(True, db_vars)
        with prod_engine.connect() as conn:
            print("✅ Successfully connected to production database")
    except Exception as e:
        print(f"❌ Error connecting to production database: {e}")
        sys.exit(1)
        
    try:
        staging_engine = get_db_connection(False, db_vars)
        with staging_engine.connect() as conn:
            print("✅ Successfully connected to staging database")
    except Exception as e:
        print(f"❌ Error connecting to staging database: {e}")
        sys.exit(1)
        
    # Create backup directory
    backup_dir = create_backup_dir()
    
    # Backup staging database first
    staging_backup_path = backup_staging_database(db_vars, backup_dir)
    print(f"Staging database backed up to: {staging_backup_path}")
    
    # Backup production database
    prod_backup_path = backup_production_database(db_vars, backup_dir)
    print(f"Production database backed up to: {prod_backup_path}")
    
    # Confirm before proceeding
    if not args.yes:
        confirm = input("\n⚠️  WARNING: This will overwrite all data in your staging database with production data. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Sync cancelled")
            sys.exit(0)
    
    # Restore production backup to staging
    restore_success = restore_to_staging(db_vars, prod_backup_path)
    
    if restore_success:            
        print("\n✅ Database sync completed successfully!")
        print(f"Production data has been copied to the staging database.")
        print(f"A backup of the production database is available at: {prod_backup_path}")
        print(f"A backup of the previous staging database is available at: {staging_backup_path}")
    else:
        print("\n❌ Database sync failed")


if __name__ == "__main__":
    main()