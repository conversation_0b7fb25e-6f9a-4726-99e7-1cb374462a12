"""08 utterances table

Revision ID: 25be54d50732
Revises: 2a5a52f83b19
Create Date: 2023-08-15 13:39:32.433114

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '25be54d50732'
down_revision = '2a5a52f83b19'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('utterances',
        sa.Column('utt_id', sa.Integer, primary_key=True),
        sa.Column('speaker', sa.String(8), nullable=False),
        sa.Column('text', sa.String, nullable=False),
        sa.Column('utt_type', sa.String(32), nullable=False, default='text'),
        sa.Column('convo_id', sa.Integer, sa.<PERSON>ey('conversations.convo_id'), nullable=False),
    )

def downgrade() -> None:
    op.drop_table('utterances')

