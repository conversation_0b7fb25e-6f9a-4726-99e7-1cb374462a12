#!/bin/bash
# deploy.sh - <PERSON><PERSON>t to deploy staging to production with improved safety measures

# Exit on error
set -e

# Function to handle errors
handle_error() {
  echo "ERROR: Deployment failed at step: $1"
  echo "Rolling back to previous state..."
  git checkout master
  echo "Deployment aborted. Please fix the issues and try again."
  # Optional: Add notification to team here
  exit 1
}

echo "===== Starting deployment process ====="

# Create a backup branch in case we need to rollback
BACKUP_BRANCH="backup-master-$(date +%Y%m%d-%H%M%S)"
echo "Creating backup branch: $BACKUP_BRANCH"
git checkout master
git branch $BACKUP_<PERSON>ANCH
echo "Backup created. If deployment fails, you can restore with: git reset --hard $BAC<PERSON>UP_<PERSON>ANCH"

# Switch to staging and update
echo "Updating staging branch..."
git checkout staging || handle_error "checkout staging"
git pull origin staging || handle_error "pull staging"

# We can run tests here in the future.

# Switch to master and update
echo "Switching to master branch..."
git checkout master || handle_error "checkout master"
git pull origin master || handle_error "pull master"

# Merge staging into master
echo "Merging staging into master..."
git merge staging || handle_error "merge staging into master"

# Push to production
echo "Pushing to production..."
git push origin master || handle_error "push to master"

# Create a tag with the current date
RELEASE_DATE=$(date +%Y%m%d)

# Ask for a description
echo "What features or fixes are included in this release? (brief summary)"
read RELEASE_DESCRIPTION

# Create the tag
git tag -a release-$RELEASE_DATE -m "Production release: $RELEASE_DESCRIPTION"
git push origin release-$RELEASE_DATE || handle_error "push tag"
echo "Created and pushed tag: release-$RELEASE_DATE"

# Update staging from master to keep them in sync
echo "Updating staging branch with latest master..."
git checkout staging || handle_error "final checkout staging"
git pull origin master || handle_error "final pull master to staging"
git push origin staging || handle_error "push updated staging"

# Clean up the backup branch since deployment was successful
echo "Cleaning up backup branch..."
git branch -D $BACKUP_BRANCH || echo "Note: Could not delete local backup branch"

echo "===== Deployment complete! ====="

exit 0