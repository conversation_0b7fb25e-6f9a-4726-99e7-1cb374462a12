"""Add table to store user data

Revision ID: bbadc73bebd4
Revises: 0814e87e0f7d
Create Date: 2025-05-15 11:37:21.247918

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid

# revision identifiers, used by Alembic.
revision = 'bbadc73bebd4'
down_revision = '0814e87e0f7d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    op.create_table('user_data_source',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, server_default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('source_type', sa.Enum('upload', 'api', name='source_type_enum'), nullable=True),
        sa.Column('provider', sa.String(), nullable=True),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('size_kb', sa.Integer(), nullable=True),
        sa.Column('content', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_index('idx_user_data_source_user_id', 'user_data_source', ['user_id'])
    
    op.execute('''
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    ''')
    # Add trigger for updated_at
    op.execute('''
    CREATE TRIGGER update_user_data_source_updated_at
    BEFORE UPDATE ON user_data_source
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
    ''')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('DROP TRIGGER IF EXISTS update_user_data_source_updated_at ON user_data_source')
    op.drop_index('idx_user_data_source_user_id', table_name='user_data_source')
    op.drop_table('user_data_source')
    op.execute('DROP TYPE IF EXISTS source_type_enum')
    # ### end Alembic commands ###