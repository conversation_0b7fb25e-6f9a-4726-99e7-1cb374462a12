beta_test_set = [
    {
        "convo_id": "1",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What columns are in the inventory table?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            }
        ],
    },
    {
        "convo_id": "2",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Which channels had the most pageviews in August?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Among the channels, TikTok has the most pageviews, followed by Google Ads, Google Display, Email and Marchex",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "What are their click-thru rates?",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "5",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you help me remove test users from the spreadsheet?  Test users are those that don't have any purchases recorded.",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            }
        ],
    },
    {
        "convo_id": "6",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What was our ad spend on GoogleAds for each week last month?",
                "intent": "Analyze",
                "dact": "query + table",
                "dax": "01A",
            }
        ],
    },
    {
        "convo_id": "8",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Which products have low levels of stock under 50 units?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            }
        ],
    },
    {
        "convo_id": "9",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How many rows of feedback are there in the product survey results?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            }
        ],
    },
    {
        "convo_id": "11",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What are the conversion rates for the active campaigns?",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "The conversion rate will use the PurchaseMade column to count conversions. Is this right?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, that's right.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "15",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's calculate the churn rate for the last quarter.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            }
        ],
    },
    {
        "convo_id": "19",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you plot the total clicks for rubber stamp campaign this month?",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Here's a line graph showing the daily clicks for the rubber stamp campaign.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Actually, can I get a breakdown of clicks and pageviews for all campaigns, grouped by day.",
                "intent": "Analyze",
                "dact": "query + table",
                "dax": "01A",
            },
        ],
    },
    {
        "convo_id": "20",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Show me a graph of clicks and pageviews for all campaigns each day this month.",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            }
        ],
    },
    {
        "convo_id": "21",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's add a new column for the attribution source on each campaign.",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Certainly, I've added a new column called 'Attribution Source'.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Using last-click attribution, which channel brought in most users from this group?",
                "intent": "Analyze",
                "dact": "analyze + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "22",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "I'd like to see our engagement rate for promoted tweets.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            }
        ],
    },
    {
        "convo_id": "24",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you get rid of whitespace in the product description?",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            }
        ],
    },
    {
        "convo_id": "26",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What is the total impressions, clicks, and spend for each ad group?",
                "intent": "Analyze",
                "dact": "query + table",
                "dax": "01A",
            }
        ],
    },
    {
        "convo_id": "29",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What is the package that has the most subscriptions in the last month?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found 4 rows with null values in the 'IsSubscribed' column. Should we take a look?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's just ignore those users in our calculation.",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
        ],
    },
    {
        "convo_id": "30",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you add a new column that calculates the total revenue from the start of the month?",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, based on your preferences the start of the month is on November 3rd. Is that correct?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yea, the start date is on the 3rd.",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "31",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Is there any column that shows the country location of each store?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            }
        ],
    },
    {
        "convo_id": "35",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do you see any expense column related to our energy consumption?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "In the table, we have 'eletricity use' and 'eletricity rate'. We can calculate energy consumption expense based on these two columns",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's do it then. Add the calculated energy consumption expense as a new column. ",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "36",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you group all the campaigns from last year by the social platform and compare the total expense to the impression and clicks generated?",
                "intent": "Analyze",
                "dact": "query + table",
                "dax": "01A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. I've created a pivot table for this.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Great. Can you also calculate the click-through-rate for each group.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "39",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How has our engagement rate changed this year?",
                "intent": "Analyze",
                "dact": "analyze + multiple",
                "dax": "02D",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I can take a look. Should I calculate month-over-month engagement rate across all the platforms?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Calculate engagement rate for each platform as of month end, take the average, and show me the result for the last 12 months",
                "intent": "Analyze",
                "dact": "analyze + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "46",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's the total returns of my stock portfolio in the last three years? I want to see a line chart for this",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            }
        ],
    },
    {
        "convo_id": "48",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do we have any column that shows the education level of our subscribers?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            }
        ],
    },
    {
        "convo_id": "50",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How many battery startups that have raised funding recently are already gone?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            }
        ],
    },
    {
        "convo_id": "53",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Group all the events based on year. Calculate last 5 years of event spend from different regions.",
                "intent": "Analyze",
                "dact": "query + table",
                "dax": "01A",
            }
        ],
    },
    {
        "convo_id": "54",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "In the 'daily returns' column, use 0 rather than null please.",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            }
        ],
    },
    {
        "convo_id": "56",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's the correlation between new user sign up and traffic?",
                "intent": "Analyze",
                "dact": "analyze + multiple",
                "dax": "02D",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I don't see any column called new user sign up or traffic.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Just use the 'signup' and 'visit' columns. Aggregate them by hours and calculate the correlation based on the hourly total 'sign up' and 'visit'",
                "intent": "Analyze",
                "dact": "analyze + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "58",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's our highest, lowest and average rating based on the 'customer rating' column?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "014",
            }
        ],
    },
    {
        "convo_id": "59",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What are the different values in column 'Source'?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "We have 'referral', 'social media', 'company website', 'events' and 'other'.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you show me the distribution of applicants coming from different sources as a histogram?",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
        ],
    },
    {
        "convo_id": "65",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do we have data on user abandoning the cart?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            }
        ],
    },
    {
        "convo_id": "66",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Are there any indication whcih country our suppliers are from?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            }
        ],
    },
    {
        "convo_id": "67",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Add a new column for product name in purchases table",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            }
        ],
    },
    {
        "convo_id": "68",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Create a new column showing profit margin for each product based on price and estimated cost.",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            }
        ],
    },
    {
        "convo_id": "69",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you remove all activities after 1/10?",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            }
        ],
    },
    {
        "convo_id": "70",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Remove all records of discontinued products, those with ProductIDs below 9020.",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            }
        ],
    },
    {
        "convo_id": "71",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Correct the supplier name 'SkinLove Laboratories' to the nickname 'SkinLove Labs' across all records.",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            }
        ],
    },
    {
        "convo_id": "72",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Change all product for product ID above 9020 to 'Discontinued' category",
                "intent": "Transform",
                "dact": "update",
                "dax": "006",
            }
        ],
    },
]
