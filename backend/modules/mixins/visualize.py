import calendar
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go

from backend.prompts.mixins.for_visualize import *
from backend.components.engineer import PromptEngineer
from backend.components.frame import Frame

class VisualizeMixin:
  """ Methods that manage the visualizations, graphs, charts, or dashboards """

  def plot_action(self, context, state, world):
    # Supports {003} and {03B} by preparing a derived table to create a visualization
    flow = state.get_flow(flow_type='plot')
    valid_ent_list = state.dict_to_entity(world.valid_columns)

    if self.decide_to_carry(context, world):
      previous_frame = world.frames[-1]
      db_output, sql_query = previous_frame.get_data(), previous_frame.code
    else:
      db_output, sql_query = self.database.query_data(context, flow, state, world)

    if sql_query == 'error':
      frame = self.default_frame(state, valid_ent_list)
      frame.source = 'sql'
      frame.signal_failure('code_generation', db_output)
    else:
      tab_name = state.entities[0]['tab'] if len(state.entities) > 0 else state.current_tab
      frame = Frame(tab_name, 'derived', source='plotly')
      frame.set_data(db_output, sql_query)
      if world.has_data() and world.frames[-1].properties.get('converted', False):
        frame.properties['converted'] = True

    if frame.is_successful() and not state.ambiguity.present():
      frame, state = self.create_px_figure(context, frame, state)
    flow.completed = True
    return frame, state

  def create_px_figure(self, context, frame, state):
    """ Attempt to execute the Plotly code generated by the model
    If it fails, then try again up to 3 times. If it still fails, then
    create a default figure. In any case, return a valid Plotly figure. """
    plot_df = frame.get_data()
    preview = PromptEngineer.display_preview(plot_df, max_rows=32)
    prompt = plot_prompt.format(history=context.compile_history(look_back=3), data_preview=preview)
    execution_context = { 'df': plot_df, 'px': px }

    custom_params = {'artifact_key': 'fig', 'data_preview': preview, 'exec_context': execution_context}
    figure, _ = self.database.generate_artifact(context, prompt, state, custom_params)

    # One final attempt to create a valid figure
    if isinstance(figure, str):
      pred_figure = figure
      figure = eval(pred_figure)
    # If it's still not a valid figure, then create a default one
    if not isinstance(figure, go.Figure):
      print(f"Maximum attempts reached. Creating a default figure.")
      figure = px.line(plot_df, x=plot_df.columns[0], y=plot_df.columns[1])

    figure.update_layout(
      title_x=0.5,
      paper_bgcolor='#F9FAFB',
      margin={'t': 45, 'b': 40, 'r': 40, 'pad': 10}
    )

    frame.visual = figure
    return frame, state

  def backfill_from_plot(self, curr_flow, prev_flow, state):
    table_df = self.database.db.tables[state.current_tab]
    curr_flow, prev_flow = self.write_to_scratchpad(curr_flow, prev_flow, table_df)
    return state

  def decide_to_carry(self, context, world):
    if len(world.frames) > 1:  # this is not the same as world.has_data() since it checks for 2+ frames
      prior_state = world.states[-2]
      intent_mismatch = prior_state.intent != 'Analyze'
      previous_frame = world.frames[-1]
    else:
      return False

    if previous_frame.properties.get('do_carry', False):
      do_carry = True
    elif context.num_utterances < 3 or intent_mismatch:
      do_carry = False
    else:
      prior_query = previous_frame.code  # we know a frame is accessible since there must be at least 3 frames
      prior_dact = prior_state.get_dialog_act(form='string')

      if len(prior_query) == 0:
        do_carry = False
      elif prior_dact not in ['query', 'pivot', 'measure', 'segment']:
        do_carry = False
      else:
        data_preview = PromptEngineer.display_preview(previous_frame.get_data(), max_rows=32)
        convo_history = context.compile_history()
        prompt = carry_prompt.format(sql_query=prior_query, history=convo_history, data_preview=data_preview)
        raw_output = self.api.execute(prompt)
        decision = PromptEngineer.apply_guardrails(raw_output, 'json')
        do_carry = decision['carry']

    return do_carry

  def trend_analysis(self, context, state, world):
    # Supports {023} by identifying a pattern or trend in the data, often related to clustering or trendlines
    flow = state.get_flow(flow_type='trend')
    valid_ent_list = state.dict_to_entity(world.valid_columns)
    previous_frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)
    tab_name = state.entities[0]['tab'] if len(state.entities) > 0 else state.current_tab
    return self.pattern_frame(previous_frame, tab_name, state, world)

  def explain_action(self, context, state, world):
    # Supports {038} by generating an explanation or summary of the chart or graph
    flow = state.get_flow(flow_type='explain')
    valid_ent_list = state.dict_to_entity(world.valid_columns)
    previous_frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)
    tab_name = state.entities[0]['tab'] if len(state.entities) > 0 else state.current_tab
    return self.explain_frame(previous_frame, tab_name, state, world)

  def manage_report(self, context, state, world):
    # Supports {23D} by managing the report and dashboard settings as part of Dashboard Flow
    flow = state.get_flow(flow_type='report')
    flow.completed = True
    return None, state

  def save_to_dashboard(self, context, state, world):
    # Supports {38A} by saving the current visualization to the dashboard
    flow = state.get_flow(flow_type='save')
    flow.completed = True
    return None, state

  def design_chart(self, context, state, world):
    # Supports {136} by designing the chart layout and appearance
    flow = state.get_flow(flow_type='design')
    valid_entities = state.dict_to_entity(world.valid_columns)
    frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_entities)

    if flow.slots['source'].filled:
      prompt = design_prompt.format(df_tables=self.database.tab_desc, history=context.compile_history(), thought=state.thought)
      db_output, code = self.database.manipulate_data(context, state, prompt, world.valid_tables)
      if code == 'error':
        frame = Frame(state.current_tab)
        frame.signal_failure('code_generation', db_output.strip())
        self.actions.add("SHARE_MSG")
      else:
        frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

    else:
      self.actions.add("CLARIFY")
      frame.warning = "it is unclear what you would like to format"
      state.ambiguity.observation = self.format_clarification(flow)

    if frame.is_successful():
      flow.completed = True
    return frame, state

  def style_table(self, context, state, world):
    # Supports {13A} by styling the appearance of a permanent derived table
    flow = state.get_flow(flow_type='style')
    flow.completed = True
    return None, state

