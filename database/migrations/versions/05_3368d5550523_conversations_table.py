"""05 conversations table

Revision ID: 3368d5550523
Revises: 273a9c1ed6a3
Create Date: 2023-05-12 19:29:02.917007

"""
from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import Vector

# revision identifiers, used by Alembic.
revision = '3368d5550523'
down_revision = '273a9c1ed6a3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('conversations',
        sa.<PERSON>umn('convo_id', sa.Integer, primary_key=True),

        sa.<PERSON>umn('short_embed', Vector(384), nullable=False),
        sa.<PERSON>n('medium_embed', Vector(768)),
        sa.<PERSON><PERSON>('long_embed', <PERSON>ector(1536)),

        sa.<PERSON>('start_time', sa.DateTime),
        sa.<PERSON>umn('end_time', sa.DateTime),
        sa.<PERSON>umn('agent_id', sa.Integer, sa.<PERSON><PERSON>('agents.agent_id')),
        sa.<PERSON>umn('user_id', sa.<PERSON><PERSON>, sa.<PERSON>('users.user_id')),

        sa.<PERSON>('labels', sa.JSON),
        sa.<PERSON><PERSON>('dact_id', sa.Integer, sa.<PERSON><PERSON>ey('dialogue_acts.dact_id')),
    )

def downgrade() -> None:
    op.drop_table('conversations')