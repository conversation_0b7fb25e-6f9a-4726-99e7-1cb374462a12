"""make_refresh_token_nullable

Revision ID: ca61d31053fe
Revises: fa34dd62ea09
Create Date: 2025-02-28 14:20:36.375696

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ca61d31053fe'
down_revision = 'fa34dd62ea09'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Make refresh_token column nullable
    op.alter_column('credential', 'refresh_token',
                    existing_type=sa.String(),
                    nullable=True)
    
    # Add instance_url column
    op.add_column('credential', 
                  sa.Column('instance_url', sa.String(), nullable=True))


def downgrade() -> None:
    # Remove instance_url column
    op.drop_column('credential', 'instance_url')
    
    # Make refresh_token column non-nullable again
    op.alter_column('credential', 'refresh_token',
                    existing_type=sa.String(),
                    nullable=False)
