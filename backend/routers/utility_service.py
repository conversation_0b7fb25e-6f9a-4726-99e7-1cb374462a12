from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from backend.utilities.logger import logger

utility_router = APIRouter()

@utility_router.post("/telemetry")
async def forward_telemetry(request: Request):
    """
    Receives logs in custom format and forwards them to <PERSON>.
    No trace handling for now - focusing only on logs.
    """
    log_data = await request.json()
    
    # Forward telemetry data to <PERSON> using our centralized logger
    return await logger.forward_telemetry(log_data)

@utility_router.get('/')
async def root():
  return HTMLResponse("""<h1>Soleda AI</h1>
    <p>Soleda provides a useful, reliable and trustworthy conversational agent to help you understand your data.</p>""")

@utility_router.get('/health')
async def heartbeat():
  return {'status': 'ok'} 