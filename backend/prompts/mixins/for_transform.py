insert_prompt = """Given the conversation history and supporting details, follow the thought process to insert or add data using Pandas code.
Supporting details include the name of the column(s) to add, as well as possible operations for populating the values within the column.
For the final example, you will also be given the first few rows of the table to help you understand the data.
The existing dataframes you have are {df_tables}.

Focus on writing directly executable Python, which may contain comments to help with reasoning. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe.
When calculating a percentage, DO NOT multiply by 100. When performing any division operation, always return the result with at least 4 decimal places of precision.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

For example,
---
User: Create a formula that is True if the ad spend is greater than $1000 and False otherwise.
* Current columns: ad_spend in orders
* New columns: expensive in orders
* Operations: filter ad spend > 1000
* Thought: I can check if the ad spend is greater than $1000 by using a comparison operator.  I will then create a new column with a short descriptive name, such as 'expensive'.

_Output_
```python
db.orders['expensive'] = db.orders['ad_spend'] > 1000
```

User: We can make a new column for that
* Current columns: Europe, Americas, TotalAmount, ExchangeRate in InternationalSales
* New columns: AsiaPacific in InternationalSales
* Operations: none
* Thought: I should add a new column to hold the total sales in the Asia Pacific region.

_Output_
```python
db.InternationalSales['AsiaPacific'] = ''
```

User: Actually, can you add a column showing the change every month from last year to this year?
* Current columns: channel_id, mq_leads, and month in Qualified Leads; channel_id and channel_name in Channels with Spend
* New columns: monthly_mql_change in Qualified Leads
* Operations: filter for channel is newsletter, group by month, compare all months in the past year
* Thought: The new column will track the change in MQLs on a month-to-month basis. The values will be calculated using the formula (current month - previous month) / previous month.

_Output_
```python
db['Qualified Leads']['monthly_mql_change'] = db['Qualified Leads'].groupby('month')['mq_leads'].pct_change()
```

User: Add a column that applies the tax rate to the item_price.
* Current columns: tax_rate and item_price in purchases
* New columns: total_price in purchases
* Operations: apply tax rate to item price
* Thought: I can apply the tax rate by multiplying the price with 1 + rate.  I will then create a new column with a short descriptive name, such as 'total_price'.

_Output_
```python
db.purchases['total_price'] = db.purchases['item_price'] * (1 + db.purchases['tax_rate'])
```

User: Could you add a column showing the total number of emails sent for each campaign?
* Current columns: campaign in campaigns
* New columns: emails_sent in email_metrics
* Operations: none
* Thought: I can count the number of emails sent for each campaign by grouping by campaign and then counting the number of rows.

_Output_
```python
db.email_metrics['emails_sent'] = db.campaigns.groupby('campaign')['campaign'].transform('count')
```

User: Can you also add a column for the contact phone number for each event?
* Current columns: event_id and event_name in events
* New columns: CustomerPhone in events
* Operations: none
* Thought: The new column will hold the customer's phone number. However, I do not see a way to fill it using existing columns, so I will create a placeholder for now.

_Output_
```python
db.events['CustomerPhone'] = ''
```

User: OK, so let's mark customers with more than 1000 points and 10 purchases as high value, 500 points and 5 purchases as medium value, and everyone else as low.
* Current columns: CustomerID, FirstName, LastName, LoyaltyPoints in Customers; PurchaseID in Purchases
* New columns: CustomerValue in customers
* Operations: filter loyalty points > 1000; filter loyalty points > 500; filter purchases > 10; filter purchases > 5; group by customer
* Thought: The new column will assign customer status as either high, medium or low value. This is determined by a combination of loyalty points and purchase amount.

_Output_
```python
# calculate the number of purchases for each customer
purchase_counts = db.purchases['CustomerID'].value_counts().rename('PurchaseCount')
db.customers = db.customers.merge(purchase_counts, left_on='CustomerID', right_index=True, how='left')
db.customers['PurchaseCount'].fillna(0, inplace=True)

# assign customer value based on conditions
conditions = [
    (db.customers['LoyaltyPoints'] > 1000) & (db.customers['PurchaseCount'] >= 10),
    (db.customers['LoyaltyPoints'] > 500) & (db.customers['PurchaseCount'] >= 5)
]
choices = ['high', 'medium']
db.customers['CustomerValue'] = np.select(conditions, choices, default='low')
```

User: What if I want to know how long someone has been a member with us at Sixby Fitness
* Current columns: MemberID, FirstName, LastName, EndDate, StartDate in Members
* New columns: MemberDuration in AttendanceReport
* Operations: filter for EndDate is null
* Thought: The new column will calculate the duration of membership. The values can be calculated using the formula Present Date - StartDate.

_Output_
```python
db.AttendanceReport['MemberDuration'] = (pd.to_datetime('today') - db.Members['StartDate']).dt.days
```
---
Now it's your turn! Please output executable Python code with inline comments to insert the columns(s) as requested.

User: {utterance}
* Current columns: {source_cols}
* New columns: {target_cols}
* Operations: {operations}
* Thought: {thought}

For additional context, here are the first few rows of the table:
{example_rows}

_Output_
"""

delete_prompt = """Given the conversation history and supporting details, follow the thought process to delete or drop data using Pandas code.
Supporting details includes the location of the data to remove. You can access dataframes as 'db' followed by a table name: `db.table_name`.
Concretely, you have {df_tables}.

Please write directly executable Python, which may contain comments to help with reasoning. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe.
When comparing dates, make sure to use `.date()` rather than relying on `to_datetime` alone, since the latter makes implicit conversions that behave unexpectedly.
Only focus on the request in the final user turn. You should ignore any requests in previous turns since they have already been addressed.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

For example,
---
User: Can you delete all rows that have empty values?
* Tables: purchases
* Columns: * in purchases

Thought: I should look for all rows within the purchases table and drop those that have empty values.

_Output_
```python
# drop rows with null values
db.purchases.dropna(inplace=True)
```

User: Please remove all users without a valid phone number.
Agent: You got it, I have removed 152 rows from the users table.
User: Great, now remove everyone without a last name.
* Tables: customers
* Columns: first, last in customers

Thought: I should look in the customers table for rows where the last name is null and remove them.

_Output_
```python
# drop rows with null last names
db.customers.dropna(subset=['last'], inplace=True)
```

User: Delete all columns containing 'deprecated'.
* Tables: EmailCampaigns
* Columns: mulitple columns in EmailCampaigns

Thought: I should look in all tables for columns containing 'deprecated' and remove them using the drop method.

_Output_
```python
# find all columns containing 'deprecated' and drop them
to_be_dropped = [col for col in db.EmailCampaigns.columns if 'deprecated' in col]
db.EmailCampaigns.drop(columns=to_be_dropped, inplace=True)
```

User: Please show me a graph of the clicks for each day this month.
Agent: No problem, here you go.
User: Shoot, we're double counting. Can you get rid of all events recorded after the pixel was installed on 8/23/2024?
* Tables: activities
* Columns: EventTimestamp in activities

Thought: I should look in the activities table for rows where the EventTimestamp is after 8/23/2024 and remove them.

_Output_
```python
# make sure to use .date() so we are comparing dates rather than timestamps
cutoff_date = pd.to_datetime('2024-08-23').date()
db.activities = db.activities[db.activities['EventTimestamp'].dt.date <= cutoff_date]
```

User: Yes, remove the customer with id 15900276 from both tables.
* Tables: customers; orders
* Columns: customer_id from customers; customer_id from orders

Thought: I will first find and remove the customer with id 15900276 from the customers table. Then, I will do the same on the orders table.

_Output_
```python
# remove customer with id 15900276 from both tables
db.customers = db.customers[db.customers['customer_id'] != 15900276]
db.orders = db.orders[db.orders['customer_id'] != 15900276]
```

User: Can you drop all states that are blank or null?
* Tables: customers
* Columns: state in customers

Thought: I can remove blank states by searching for an empty string. Then, I will remove states with null using the dropna method.

_Output_
```python
# search for empty states
db.customers = db.customers[db.customers['state'] != '']
# drop rows with null states
db.customers.dropna(subset=['state'], inplace=True)
```

User: Erase all rows where the year is before 2019 and the revenue is negative.
* Tables: orders
* Columns: year, price in orders

Thought: The orders table has a year and price column, which be used for revenue calculations.  I can filter for before 2019 and negative price, and then remove them.

_Output_
```python
# Eliminate unwanted rows by keeping the reverse of the condition
db.orders = db.orders[(db.orders['year'] >= 2019) & (db.orders['price'] >= 0)]
```
---
Now it's your turn! Please generate executable Python with inline comments to guide your thinking followed by code to delete the data as requested.

{history}
{location}

Thought: {thought}

_Output_
"""

text2cols_prompt = """Given the conversation history and supporting details, think briefly about the best way to split a single column into multiple columns, and then generate the Pandas code for doing so.
Supporting details includes the head of the source column and the resulting target columns. Sometimes, the target columns may be unknown, in which case you should use your best judgement to name them.

You are given access to the source dataframe in form of `db.{tab_name}`, which means you can access the columns directly.
Please only output executable Python code and inline comments. If a request requires multiple steps, write each step on a new line.
It is absolutely critical that there are no explanations after the code.

For example,
---
_Conversation History_
User: Are there any patterns if we look at it on a monthly basis?
Agent: Should I look for patterns based on the submission_date or the delivery_date?
User: Go with the delivery date

_Supporting Details_
|    | delivery_date  |
|---:|:---------------|
|  0 |  02/03/23      |
|  1 |  02/18/23      |
|  2 |  03/13/23      |
|  3 |  09/15/23      |
|  4 |  10/27/23      |
Target: ['month', 'day', 'year']
Delimiter: '/' (slash)

_Output_
```python
# try to extract the date directly
try:
  db.Deliveries['delivery_date'] = pd.to_datetime(db.Deliveries['delivery_date'], format='%m/%d/%y')
  db.Deliveries['month'] = db.Deliveries['delivery_date'].dt.month
  db.Deliveries['day'] = db.Deliveries['delivery_date'].dt.day
  db.Deliveries['year'] = db.Deliveries['delivery_date'].dt.year

# otherwise, split the date by slash delimiter
except AttributeError:
  db.Deliveries[['month', 'day', 'year']] = db.Deliveries['delivery_date'].str.split('/', expand=True)
  db.Deliveries['month'] = db.Deliveries['month'].astype(int)
  db.Deliveries['day'] = db.Deliveries['day'].astype(int)
  db.Deliveries['year'] = db.Deliveries['year'].astype(int)
```

_Conversation History_
User: Yea, those seem fine to me
Agent: No problem, I'll ignore those for now
User: Can you divide up the attributes for each lead into their own columns?

_Supporting Details_
|    | lead_attributes                            |
|---:|:-------------------------------------------|
|  0 |  filled form & contacted & high net worth  |
|  1 |  return visitor & downloaded brochure      |
|  2 |  referral                                  |
|  3 |  filled form & return visitor              |
|  4 |  high net worth & referral                 |
Target: <unknown>
Delimiter: '&' (ampersand)

_Output_
```python
# Split by ampersand and strip nulls to identify unique attributes
split_attributes = db.mql_reviewed['lead_attributes'].fillna('').str.split('&').apply(lambda x: [item.strip() for item in x if item])
unique_attributes = split_attributes.explode().replace('', pd.NA).dropna().unique()

# Create binary columns for each unique attribute
for attr in unique_attributes:
  target_col_name = f"{{attr}}_attr"
  db.mql_reviewed[target_col_name] = split_attributes.apply(lambda row: attr in row)

# the original column is not useful anymore, so we can remove it
db.mql_reviewed.drop('lead_attributes', axis=1, inplace=True)
```

_Conversation History_
User: Let's take a look at the survey results for the ice cream flavors now.
Agent: Sure, I see a column for favorite_flavors, what would you like me to do with it?
User: Each participant ranked their top 3 favorite flavors, can you separate them out?

_Supporting Details_
|    | favorite_flavors              |
|---:|:------------------------------|
|  0 |  chocolate;vanilla;strawberry |
|  1 |  mint;caramel;coffee          |
|  2 |  rocky road;coffee;chocolate  |
|  3 |  vanilla;strawberry;caramel   |
|  4 |  chocolate;rocky road;coffee  |
Target: <unknown>
Delimiter: ';' (semicolon)

_Output_
```python
# Split by semicolon and strip nulls to identify unique flavors
split_flavors = db.CustomerSurvey['favorite_flavors'].fillna('').str.split(',').apply(lambda x: [item.strip() for item in x if item])
unique_flavors = split_flavors.explode().replace('', pd.NA).dropna().unique()

# Create ranking columns for the top three flavors
for rank in range(1, 4):
  target_col_name = f"flavor_rank_{{rank}}"
  db.CustomerSurvey[target_col_name] = split_flavors.apply(lambda row: row[rank - 1] if len(row) >= rank else pd.NA)

# the original column is not useful anymore, so we can remove it
db.CustomerSurvey.drop('favorite_flavors', axis=1, inplace=True)
```

_Conversation History_
User: Let's see if there's a relationship between the origin of the referral and the conversion rate
Agent: It might help to split up the origin column into separate columns first. What do you think?
User: yea, go for it

_Supporting Details_
|     |  Origin               |
|----:|:----------------------|
|  0  |  google, email        |
|  1  |  email                |
|  2  |  facebook, instagram  |
|  3  |  google               |
|  4  |  word of mouth        |
Target: <unknown>
Delimiter: ',' (comma)

_Output_
```python
# Split by comma and strip nulls to identify unique origins
split_origins = db.referrals['Origin'].fillna('').str.split(',').apply(lambda x: [item.strip() for item in x if item])
referral_origins = split_origins.explode().replace('', pd.NA).dropna().unique()

# Create binary columns for each unique origin
for origin in referral_origins:
  target_col_name = f"from_{{origin}}"
  db.referrals[target_col_name] = split_origins.apply(lambda row: origin in row)
```

_Conversation History_
User: Which city had the highest number of signups?
Agent: There isn't a city column, but I can create one by splitting the location column. How does that sound?
User: That works for me

_Supporting Details_
|    | location         |
|---:|:-----------------|
|  0 |  Los Angeles,CA  |
|  1 |  New York, NY    |
|  2 |  Chicago,IL      |
|  3 |  Miami, FL       |
|  4 |  Seattle, WA     |
Target: <unknown>
Delimiter: ',' (comma)

_Output_
```python
# split the location column by comma, but with a regex to account for any spaces
db.february_signups[['city', 'state']] = db.february_signups['location'].str.split(r',\s*', expand=True)
# unlike a date object, searching by location is better off querying the city or state directly, so we can remove the original column
db.february_signups.drop('location', axis=1, inplace=True)
```

_Conversation History_
User: I think that's all we need for now
Agent: I'll go ahead and remove the duplicates then
User: Run text-to-columns on the different channels please

_Supporting Details_
|    | channels                                           |
|---:|:---------------------------------------------------|
|  0 |  ['facebook', 'twitter', 'instagram', 'linkedin']  |
|  1 |  ['snapchat', 'tiktok', 'instagram', 'pinterest']  |
|  2 |  ['twitter', 'instagram', 'linkedin']              |
|  3 |  ['reddit', 'quora']                               |
|  4 |  ['facebook', 'twitter']                           |
Target: [''linkedin'', 'snapchat', 'reddit', 'pinterest', 'tiktok', quora', 'facebook', 'instagram', 'twitter']
Delimiter: ',' (comma)

_Output_
```python
# Check if the channel is already a list of strings or a comma-separated string
if isinstance(db.spend_aggregated['channels'].iloc[0], list):
    exploded_channels = db.spend_aggregated['channels'].explode()
else:
    exploded_channels = db.spend_aggregated['channels'].str.split(',').explode().str.strip()

# Create dummies and aggregate them back to the original dataframe
channel_dummies = pd.get_dummies(exploded_channels, prefix_sep='channel_')
channel_dummies = channel_dummies.groupby(level=0).max()
db.spend_aggregated = db.spend_aggregated.join(channel_dummies)
```
---
Now it's your turn! When we are simply running text-to-columns, we can simply split the column by a delimiter.
However, if we are creating binary columns (and targets are not given), then remember to first capture all unique values in the source column to determine the new target columns.
Also, when determining the unique values we should avoid null values whenever possible.

_Conversation History_
{history}

_Supporting Details_
{source_markdown}
Target: {target_cols}
Delimiter: {delimiter}

_Output_
"""

merge_categories = """User: Thank you. Can you show me the number of users who have subscribed to this software, sorted by channel?
* Target: the count of users
* Operations: ["group by channel", "sort by number of users", "filter for subscribed"]
Agent: Sure, here's the breakdown.
User: I'm checking the channel data, and some of the entries are not correct.
Agent: Yes, I found [Facebook, FB, Booking, Facebook Ads]. How should we handle this?
User: Facebook and Facebook Ads should both be FB. We can just drop the Booking one.
* Target: channel

_Operations_
```json
[ "update similar terms of 'Facebook' and 'Facebook Ads' into 'FB'",
  "delete Booking value from the channel column" ]
```

User: What are all the unique payment methods we have?
* Target: none
* Operations: ["none"]
Agent: Unique payment methods include Credit Card, cash, and check. See table for more.
User: ok, treat Credit Card, CC, and credit card all as credit.
* Target: payment methods
* Operations: ["update similar terms of 'Credit Card', 'CC', and 'credit card' into 'credit'"]

User: Can you show me the ad spend in Canada for our recent campaign starting from Saturday?
* Target: the sum of ad spend
* Operations: ["filter for Canada", "filter date >= Saturday", "filter for recent campaign"]
Agent: I found a group with [Canada, canada, CA, and Can] in the region column. Should I merge these together?
User: Yes, but ignore CA as it might refer to California.
* Target: region
* Operations: ["update similar terms of 'Canada', 'canada', and 'Can' into 'Canada'"]
"""

merge_methods_prompt = """Given the most recent utterances and the available columns, your goal is to choose the most likely methods for merging together the selected columns to create a new one.
The selected columns will start with an at symbol '@' to help you identify them.
Sometimes merging means keeping only the content from one column and discarding the rest, while other times it means combining the content together with a delimiter.
Start by briefly thinking about the top methods you would use. If the top method is patently obvious, then choose that 1 method. Otherwise, rank the top 3 most likely methods in order of most likely to least likely.
When no single method is sufficient or some calculation is necessary, then output 'formula' as the method. This is especially useful when the user's request is unclear or when the process requires filtering or aggregation.

There are 11 possible methods to choose from:
  - *order*: keep the column content that appeared first, a good default option when no other method seems appropriate
  - *contains*: keep the column based on whether it contains a substring, a good method for finding specific values
  - *size*: keep the value that is smaller or larger, a good method for choosing the minimum or maximum
  - *length*: keep the value based on the length of the text, for example to keep the longer or shorter text
  - *alpha*: keep the text which comes earlier or later in the alphabet, for example to sort a list
  - *concat*: combine the text together directly, with no space in between, a good method for combining text
  - *comma*: join the text of the columns with a comma, for example to join a city and state
  - *underscore*: connect the text with an underscore, for example to create a username
  - *period*: join the text with a period, for example to create a domain name or file extension
  - *space*: join the text with a space, for example to create a full name
  - *separator*: join the text with a custom separator, for example using '/' to create a file path or a date

Your entire response should be in well-formatted JSON including rationale (string) and methods (list), with no further explanations after the JSON output.

For example,
#############
User: You see how we have the cost and the revenue columns?
Agent: Yes, what would you like to do with them?
User: I want to create a new column that is true when the total return is positive, and false otherwise.
Columns: activity_id, user_id, product_id, send_timestamp, @ad_cost, num_displays, num_views, num_clicks, payment_method, shipping_address, @estimated_revenue

_Output_
```json
{{
  "rationale": "total return is typically revenue minus the cost, or a ratio of the two, which requires calculation",
  "methods": ["formula"]
}}
```

User: sure, ok
Agent: Given these columnns, how would you like to create each student's folder path?
User: I don't know, you tell me
Columns: TestimonialID, @StudentID, CourseID, @Username, TestimonialText, DateProvided, Featured, ApprovalStatus, @PhotoLink, OutreachID, CampaignName, TargetAudience

_Output_
```json
{{
  "rationale": "a folder path typically uses a separator like '/' to join the student ID and username, this requires a custom separator",
  "methods": ["separator", "concat", "underscore"]
}}
```

User: The FoodLiked is actually pretty much the same as the favorite cuisine type
Agent: Ok, I can merge the FoodLiked and FavCuisineType columns together. How does that sound?
User: Yea, go for it
Columns: CustomerID, CustName, @FavCuisineType, ShippingAddress, ContactNumber, @FoodLiked, IsActive, Twitter, Instagram, Yelp


_Output_
```json
{{
  "rationale": "If we keep both, separating with a comma is a good way to go. If we only keep one, we can use the length of the text since that avoids blank values.",
  "methods": ["comma", "length", "concat"]
}}
```

User: Can we make a field for the full name?
Agent: Certainly, I have found the FirstName and LastName columns to merge together. What method would you like to use?
User: What method would you suggest?
Columns: ContactID, @FirstName, @LastName, DateTimeJoined, EmailAddress, OpportunityID, Stage, DealSize, LastContactDate, NextStep, DecisionMaker, City, State, ZipCode

_Output_
```json
{{
  "rationale": "A full name is typically the first name followed by the last name, with a space in between",
  "methods": ["space", "concat", "comma"]
}}
```

User: Can we make a field which captures both of them together?
Agent: Sure, what method would you like to use to merge the columns together?
User: Go with a period
Columns: ContactID, FirstName, LastName, DateTimeJoined, EmailAddress, OpportunityID, @Stage, @DealSize, LastContactDate, NextStep, DecisionMaker, City, State, ZipCode

_Output_
```json
{{
  "rationale": "The user explicitly requested a period, which makes the method unambiguous.",
  "methods": ["period"]
}}
```

User: in that case, we should just keep one of them
Agent: You got it, what method would you like to use to determine which channel name to keep?
User: doesn't matter, just pick one
Columns: CampaignID, CampaignName, @CampaignSource, StartDate, EndDate, TargetAudience, @Channel, ResponseRate, CreativeAsset in Campaigns

_Output_
```json
{{
  "rationale": "The order of appearance is a good default option. The length of the text can also be useful, as well as the alphabetical order.",
  "methods": ["order", "alpha", "length"]
}}
```

User: because we want to figure out the average return for each package
Agent: I am considering the membership fee and the packages bought columns for merging together.
User: Merge the membership fee, packages bought columns together to form a new column in the original table.
Columns: full_name, email_address, phone_number, date_joined, membership_type, @membership_fee, expiration_date, @packages_bought, emergency_contact

_Output_
```json
{{
  "rationale": "The average return depends on calculating a ratio of the membership fee to the number of packages bought. This requires a formula.",
  "methods": ["formula"]
}}
```

User: yes, they are repeated
Agent: When merging these columns, what method would you like to use to decide which value to keep?
User: Keep the one where the email and organization are the same.
Columns: uuid, activity_type, duration, data_source, outcome, @email, organization, lead_source, contact_date, @email_address, status, notes

_Output_
```json
{{
  "rationale": "The email and organization columns are the most important, so we can keep the values based on those.",
  "methods": ["contains", "length", "order"]
}}
```
#############
We now move on to the real case. Please generate a rationale for the user's request followed by a list of methods.
Remember that rationales should be very concise, follow the given examples and keep to only one or two sentences!

{history}
Columns: {column_info}

_Output_
"""

merge_col_thoughts = {
  'concat': "concatenate the column content, with no space in between",
  'space': "merge the text with a space in between: ' '",
  'underscore': "connect the text with an underscore: '_'",
  'period': "join the text with a period in between: '.'",
  'comma': "combine the text in the columns with a comma in between: ','",
  'separator': "join the text in the columns using '<INPUT>' as a custom separator",
  'contains': "keep the column content that contains the '<INPUT>' substring, or the first instance if both contain it",
  'order': {
    'first': "keep the column content that appeared first, furthest to the left",
    'last': "keep the column content that appeared last, furthest to the right"},
  'size': {
    'minimum': "keep the smallest value in the selected columns (ie. minimum)",
    'maximum': "Keep the largest value in the selected columns (ie. maximum)"},
  'length': {
    'shorter': "keep the text with the shorter length",
    'longer': "keep the text with the longer length"},
  'alpha': {
    'A to Z': "keep the text which comes earlier in the alphabet",
    'Z to A': "keep the text which comes later in the alphabet"}
}

merge_cols_prompt = """Given the user request and supporting details, please generate the Pandas code for merging the columns together.
{thought_desc}

You are given access to the source dataframe in form of `db.{source_tab}`, which means you can access the columns directly.
If a request requires multiple steps, write each step on a new line. When possible to do so easily, perform the operation in place rather than assigning to a dataframe.
Critically, if there are multiple requests within the conversation, only pay attention to the most recent one in the final user turn.
Finally, whenever only one value is kept from a set of columns, always choose the non-null value first before processing the remaining logic.
Your entire response should only contain directly executable Python code, without any additional text or explanations after the output.

For example,
#############
Suppose we are trying to merge the ad_cost and estimated_revenue columns to create the positive_return column.

_Conversation History_
User: You see how we have the cost and the revenue columns?
Agent: Yes, what would you like to do with them?
User: I want to create a new column that is true when the total return is positive, and false otherwise.

_Output_
```python
db.fb_campaign_stats['positive_return'] = db.fb_campaign_stats['estimated_revenue'] > db.fb_campaign_stats['ad_cost']
```

In our next example, we are trying to merge the StudentID, Username, and PhotoLink columns to create the folder_path column.
Suppose that we have decided to use the 'separator' method with a custom separator of '/'.

_Conversation History_
User: sure, ok
Agent: Given these columnns, how would you like to create each student's folder path?
User: I don't know, you tell me

_Output_
```python
student_id, username, photo_link = db.student_records['StudentID'], db.student_records['Username'], db.student_records['PhotoLink']
db.student_records['folder_path'] = student_id + '/' + username + '/' + photo_link
```

In this example, we are trying to merge the FoodLiked and FavCuisineType columns to create the FavCuisineType column.
Based on the conversation history, we are using the 'length' method.

_Conversation History_
User: The FoodLiked is actually pretty much the same as the favorite cuisine type
Agent: Ok, I can merge the FoodLiked and FavCuisineType columns together. How does that sound?
User: Yea, whenever there is conflict, choose the one with more characters

_Output_
```python
liked, fav_type = db.food_preferences['FoodLiked'], db.food_preferences['FavCuisineType']
db.food_preferences['FavCuisineType'] = np.where(liked.str.len() > fav_type.str.len(), liked, fav_type)
```

In our next case, we are trying to merge the first_name and last_name columns to create the full_name column.

_Conversation History_
User: Can we make a field for the full name?
Agent: Certainly, I have found the FirstName and LastName columns to merge together. What method would you like to use?
User: I want them to be like "Smith, John"

_Output_
```python
df.contact_info['full_name'] = df.contact_info['last_name'] + ', ' + df.contact_info['first_name']
```

Next, we have an example where we are trying to merge the Stage and DealSize columns to create the Stage column.

_Conversation History_
User: Can we make a field which captures both of them together?
Agent: Sure, what method would you like to use to merge the columns together?
User: Go with a underscore

_Output_
```python
df['Stage'] = df['Stage'] + '_' + df['DealSize']
```

For our sixth example, we are trying to merge the CampaignSource and Channel columns to create the CampaignSource column.
This is an example of the 'contains' method, where we default to the 'order' method as a tiebreaker. The assumption is that the CampaignSource column came first within the table.

_Conversation History_
User: in that case, we should just keep one of them
Agent: You got it, what method would you like to use to determine which channel name to keep?
User: If it has the word 'revised' in it, keep that one.

_Output_
```python
source, channel = db.ga4_download['CampaignSource'], db.ga4_download['Channel']
db.ga4_download['CampaignSource'] = np.where(channel.str.contains('revised', case=False), channel, source)
```

Now suppose we are trying to merge the membership_fee and packaging_fee columns to create the joint_fee column.
For this example, the user has chosen the 'size' method with the 'maximum' option.

_Conversation History_
User: because we want to figure out the joint fees for each package, but we can't keep both
Agent: I am considering the membership fee and the packaging fee as candidates to merge together.
User: Yea, just go with the larger one

_Output_
```python
db.ups_tracking['joint_fee'] = db.ups_tracking[['membership_fee', 'packaging_fee']].max(axis=1)
db.ups_tracking['joint_fee'] = np.maximum(db.ups_tracking['membership_fee'], db.ups_tracking['packaging_fee'])
```

For our final example, we are trying to merge the email and organization columns to create the email column.
Notice that we have broken down the task into multiple steps in order to avoid nested conditionals.

_Conversation History_
User: yes, they are repeated
Agent: When merging these columns, what method would you like to use to decide which value to keep?
User: Keep whichever one is not blank
Agent: But what if neither is blank?
User: I guess we can prioritize alphabetically

_Output_
```python
email, org = db.salesforce_final['email'], db.salesforce_final['organization']
both_valid = email.notnull() & org.notnull()
alphabetical_min = np.where(email < org, email, org)
db.salesforce_final['email'] = np.where(both_valid, alphabetical_min, email.fillna(org))
```
#############
Now it's your turn! Please output executable Python code to merge the {source_cols} columns to create the {target_col} column.
A preview of the data is shown to help decide whether you need to deal with null values or other issues before merging:
{preview}

_Conversation History_
{history}

_Output_
"""

checkbox_opt_prompt = """We are currently in the process of merging together two tables for a more comprehensive view of the data.
Given the most recent utterances and the available columns, please choose the most useful columns to answer the user's request.
You can choose ten (10) columns at most, and of course, you can choose fewer if you think that is more appropriate.

The new table name should be short and descriptive, reflecting the semantic meaning of the combined data rather than just copying the two original table names.
The format should match the existing tables, so if the tables are snake_case, then the new table name should also be snake_case, but if the tables 'Uses Spaces With Capitalization', then the new one should 'Also Contain Spaces'.
A short name contains fewer parts, so if the new table involves joins 'google_ads_2024_final' and 'facebook_ad_sets_Q32024', then a good name:
  * should be as concise as possible, while still being accurate
  * is certainly _not_ 'google_ads_facebook_ad_sets' or 'google_and_facebook_advertising_2024' which are excessively long
  * probably should not even be 'google_and_facebook_ads' which mindlessly concatenates the names of the original tables
  * instead aim for 'paid_ads_2024' or perhaps even 'paid_media' since this summarizes the content of the two tables

A subset of columns have already been chosen to connect the tables, acting as primary and foreign keys. They will be marked with an at symbol '@' to help you distinguish them.
Remember, when using the table names as keys, please ensure you are copying them over exactly by preserving capitalization and spacing.
Please start by thinking carefully about the meaning of the joint table in order to decide which columns are most relevant.
Your entire response should be in well-formatted JSON with keys of thought (string), new_table (string), '{tab_one}' (list), and '{tab_two}' (list).
The list following each table name should contain the names of the columns to include from that table. There should be no further explanations or comments after the JSON output.

For example,
#############
User: We ran an updated user survey with new attributes. I want to figure out which promos those users saw to see if it affected their responses.
Agent: Sure, I'm trying to join the Promotions table with the SurveyMonkey table, but I hit some errors. Can you help me figure out which columns to use?
User: I'm not sure either, what columns do we have?
Promotions - PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, @RedeemedBy, RedemptionCount
SurveyMonkey - SurveyID, @ContactEmail, Date, SatisfactionScore, Comments (free form), SurveyType, Duration (hours), Location, LikelyToRecommend

_Output_
```json
{{
  "thought": "RedeemedBy and ContactEmail are possibly email addresses, which represent unique users.",
  "new_table": "PromoResults",
  "Promotions": ["PromoID", "PromoName", "StartDate", "EndDate", "ApplicableProducts", "RedeemedBy"],
  "SurveyMonkey": ["ContactEmail", "SatisfactionScore", "Comments (free form)", "LikelyToRecommend"]
}}
```

User: We added a new channel with LinkedIn last week, so we need to add it to our report. Can we connect it to our CRM data?
Agent: I can create a merged table that combines the LinkedIn and HubSpot tables. Does that sound good to you?
User: Yea, taht's right
LinkedIn Ads - Lead ID, @Position, @Field, @Subfield, Clicks, Pages Visited, Firsttime Visit, Downloaded Content, Form Submitted, Form Submission Datetime, Lead Score
Hubspot CRM - Contact ID, First Name, Last Name, Email, Phone Number, Company, @Job Title, @Industry, Lead Source, Lead Status

_Output_
```json
{{
  "thought": "Position and Field are both related to job titles, so they are probably related to each other.",
  "new_table": "Combined CRM",
  "LinkedIn Ads": ["Position", "Field", "Subfield", "Lead Score"],
  "Hubspot CRM": ["First Name", "Last Name", "Email", "Company", "Job Title", "Industry"]
}}
```

User: Yea, just to see the students who are currently not enrolled in any courses.
Agent: In order to progress with integrating the tables, we will need to select some columns to use for merging. What columns do you think we should use?
User: What do you mean?
testimonials - TestimonialID, @StudentID, TestimonialText, DateProvided, Rating, Featured, ReviewStatus, ApprovalStatus, PhotoLink
bb_enrollments - EnrollmentID, CourseID, @StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource

_Output_
```json
{{
  "thought": "StudentID is a common column between the two tables.",
  "new_table": "inactive_students",
  "Testimonials": ["StudentID", "TestimonialText", "Rating"],
  "BB_enrollments": ["CourseID", "EnrollmentDate", "CompletionStatus"]
}}
```

User: Can you sort the unresolved tickets based on their deal size? That should help us prioritize them.
Agent: I am trying to join the Zendesk and Salesforce tables to sort the tickets, but I can't find a common column. Can you help me figure out which columns to use?
User: We should create a new table that combines the data from both of them then
Zendesk - TicketID, DateOpened, DateClosed, TicketType, Status, Priority, TicketSource, @Requester, Category, Subject, Description, Resolution, Agent, TicketGroup, Tags, Comments
Salesforce - OpportunityID, AccountID, @ContactName, CloseDate, Amount, Stage, Probability, LeadSource, Type, Description, Owner, CreatedDate, LastModifiedDate, IsClosed, IsWon

_Output_
```json
{{
  "thought": "The same person can be represented by Requester and ContactName.",
  "new_table": "Tickets",
  "Zendesk": ["DateOpened", "DateClosed", "TicketType", "Status", "Priority", "Requester"],
  "Salesforce": ["ContactName", "CloseDate", "Amount", "Stage"]
}}
```

User: Please create a joint table with just the member activities for those folks expiring this month.
Agent: I can use member_id and user_id to join the members and activities tables. What do you think?
User: So members are those who bought a premium package, which is listed as product_id 4316
members - @member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact
activities - activity_id, user_id, @product_id, activity, send_timestamp, ad_cost, num_displays, num_views, num_clicks, payment_method, shipping_address, estimated_revenue

_Output_
```json
{{
  "thought": "The user mentioned the way members are identified is through buying premium packages",
  "new_table": "expiring_members",
  "members": ["member_id", "full_name", "email_address", "packages_bought"],
  "activities": ["activity_id", "product_id", "activity", "send_timestamp", "ad_cost", "estimated_revenue"]
}}
```

User: I want to see if the membership level affects the likelihood of a return.
Agent: I found membership_level in the CustomerInfo table, but I did not find a corresponding column for measuring returns. What columns should we use?
User: There's a order status column in the PurchaseHistory table that should help us measure returns.
purchaseHistory - ProductID, ShopifyID, SKU, Quantity, Price, Discount, TotalCost, PaymentMethod, @ShippingAddress, OrderStatus
customerInfo - CustomerID, FirstName, LastName, Email, Phone, @Address, @City, @State, @ZipCode, PreferredBrand, MembershipLevel

_Output_
```json
{{
  "thought": "MembershipLevel is a common column between the two tables.",
  "new_table": "returnLikelihood",
  "PurchaseHistory": ["SKU", "ShippingAddress", "OrderStatus"],
  "CustomerInfo": ["FirstName", "LastName", "Address", "City", "State", "ZipCode", "MembershipLevel"]
}}
```

User: You see how we have the amplitude data and the ad spend data?
Agent: Yes, what would you like to do with them?
User: Let's merge the data from the two tables to see if there's a correlation between the number of clicks and the revenue.
amplitude_08122003_08132003 - @EventTimestamp, Sessions, Users, Pageviews, PagesPerSession, AvgSessionDuration, BounceRate, Revenue, Transactions, EcommerceConversionRate
adspend_week_19 - Campaign, AdGroup, Impressions, Clicks, Cost, AvgCPC, Conversions, ConversionRate, CostPerConversion, @Month, @Day, @Year

_Output_
```json
{{
  "thought": "Timestamps are often composed of a date and time, so EventTimestamp and Month, Day, Year are likely to be related.",
  "new_table": "click_revenue_correlation",
  "amplitude_08122003_08132003": ["EventTimestamp", "Revenue", "Transactions"],
  "adspend_week_19": ["Campaign", "Clicks", "Cost", "AvgCPC", "Month", "Day", "Year"]
}}
```
#############
Now it is your turn, only choose from the available columns and do not add any extra text or explanations before or after the JSON output.
{history}
{tab_col_info}

_Output_
"""

custom_code_prompt = """The user has entered some custom code to create a new column in a dataframe.
First, check if the code might be potentially harmful or malicious. If it is, output 'error' and nothing else.
Next, check to see if there are any syntax errors. If everything looks good, then just output 'good' and nothing else.
Otherwise, fix any syntax errors to produce valid Pandas operations and output the corrected Python code.
In all cases, do not add any extra text, newlines, or explanations to your output.

For example,
#############
Original text code:
df['phone_number'] = (df['area_code']) df['prefix']-df['suffix']
Revised code:
df['phone_number'] = '(' + df['area_code'] + ') ' + df['prefix'] + '-' + df['suffix']

#############
Original number code:
df['total_sales'] = df['price'] * df['quantity'] * (1 + df['tax'])
Revised code:
good

#############
Original number code:
df['projected_rev'] = df['member_revenue'] * math.power((1 + df['est_growth_rate'], df['years'])
Revised code:
df['projected_rev'] = df.apply(lambda row: row['member_revenue'] * math.pow((1 + row['est_growth_rate']), row['years']), axis=1)

#############
Original text code:
df['full_name'] = df['last_name'], df['first_name']
Revised code:
df['full_name'] = df['last_name'] + ', ' + df['first_name']

#############
Original number code:
df['customer_value'] = df['total_spent'] if df['total_spent'] > 0 else 20
Revised code:
df['customer_value'] = df['total_spent'].where(df['total_spent'] > 0, 20)

#############
Original number code:
df['conversion_rate'] = (df['clicks'] / df['impressions']) * 100
Revised code:
df['conversion_rate'] = np.where(df['impressions'] > 0, (df['clicks'] / df['impressions']) * 100, 0)

#############
Original text code:
df['send_date'] = df['send_month'] / df['send_day'] / df['send_year']
Revised code:
df['send_date'] = df['send_month'] + ' / ' + df['send_day'] + ' / ' + df['send_year']

#############
Original text code:
df['sql_injection'] = df['prompt_prefix'] + df['instruction'] + df['prompt_suffix']
Revised code:
error

#############
Original text code:
df['location'] = df['city'], df['state']
Revised code:
df['location'] = df['city'] + ', ' + df['state']

#############
Original {merge_style} code:
{code}
Revised code:
"""

columns_for_joining_prompt = """The user is attempting to integrate two tables together. Given the conversation history and the list of valid columns to choose from,
please think carefully about which ones make the most sense for merging and then select the columns for your response.
Columns which the user has explicitly mentioned should be given highest priority, followed by columns which offer clear foreign key relationships.
However, this is often not the case, so look carefully for columns with similar themes (e.g. username and email) or content (e.g. dates and timestamps) that could be used to join the tables together.
In general prefer to keep fewer columns since this will reduce the complexity of the merged table.

Your entire response should be in well-formatted JSON including your thoughts (string) and target columns from each table (list), with no further explanations after the JSON output.
When using the table names as keys, please be sure to use the exact table names provided in the conversation history including capitalization and underscores.

For example,
#############
User: We ran an updated user survey with new attributes. I want to figure out which promos those users saw to see if it affected their responses.
Agent: Sure, I'm trying to join the Promotions table with the SurveyMonkey table, but I hit some errors. Can you help me figure out which columns to use?
User: I'm not sure either, what columns do we have?
* promotions - PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, RedeemedBy, RedemptionCount
* surveyMonkey - SurveyID, ContactEmail, Date, SatisfactionScore, FreeFormComments, SurveyType, Duration, Location, LikelyToRecommend

_Output_
```json
{{
  "thought": "RedeemedBy and ContactEmail are possibly email addresses, which represent unique users.",
  "promotions": ["RedeemedBy"],
  "surveyMonkey": ["ContactEmail"]
}}
```

#############
User: We added a new channel with LinkedIn last week, so we need to add it to our report. Can we connect it to our CRM data?
Agent: I can create a merged table that combines the LinkedIn and HubSpot tables. Does that sound good to you?
User: Yea, taht's right
* LinkedIn_final - Lead ID, Position, Field, Subfield, Clicks, Pages Visited, Firsttime Visit, Downloaded Content, Form Submitted, Form Submission Datetime, Lead Score
* Hubspot_revised - Contact ID, First Name, Last Name, Email, Phone Number, Company, Job Title, Industry, Lead Source, Lead Status

_Output_
```json
{{
  "thought": "Contact ID is an ID, but is unrelated to leads. Lead ID and Lead Source are more likely to be common columns.",
  "LinkedIn_final": ["Lead ID"],
  "Hubspot_revised": ["Lead Source"]
}}
```

#############
User: That sounds right
Agent: In order to progress with integrating the tables, we will need to select some columns to use for merging. What columns do you think we should use?
User: What do you mean?
* Testimonials - TestimonialID, StudentID, TestimonialText, DateProvided, Rating, Featured, ReviewStatus, ApprovalStatus, PhotoLink
* BB_enrollments - EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource

_Output_
```json
{{
  "thought": "StudentID is a common column between the two tables.",
  "Testimonials": ["StudentID"],
  "BB_enrollments": ["StudentID"]
}}
```

#############
User: Can you sort the unresolved tickets based on their deal size? That should help us prioritize them.
Agent: I am trying to join the Zendesk and Salesforce tables to sort the tickets, but I can't find a common column. Can you help me figure out which columns to use?
User: We should create a new table that combines the data from both of them then
* Zendesk - TicketID, DateOpened, DateClosed, TicketType, Status, Priority, TicketSource, Requester, Category, Subject, Description, Resolution, Agent, TicketGroup, Tags, Comments
* Salesforce - OpportunityID, AccountID, ContactName, CloseDate, Amount, Stage, Probability, LeadSource, Type, Description, Owner, CreatedDate, LastModifiedDate, IsClosed, IsWon

_Output_
```json
{{
  "thought": "The same person can be represented by Requester and ContactName.",
  "Zendesk": ["Requester"],
  "Salesforce": ["ContactName"]
}}
```

#############
User: Please create a joint table with just the member activities for those folks expiring this month.
Agent: I can use member_id and user_id to join the members and activities tables. What do you think?
User: So members are those who bought a premium package, which is listed as product_id 4316
* members - member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact
* activities - activity_id, user_id, product_id, activity, send_timestamp, ad_cost, num_displays, num_views, num_clicks, payment_method, shipping_address, estimated_revenue

_Output_
```json
{{
  "thought": "The user mentioned the way members are identified is through buying premium packages",
  "members": ["packages_bought"],
  "activities": ["product_id"]
}}
```

#############
User: I want to see if the membership level affects the likelihood of a return.
Agent: I found membership_level in the CustomerInfo table, but I did not find a corresponding column for measuring returns. What columns should we use?
User: There's a order status column in the PurchaseHistory table that should help us measure returns.
* PurchaseHistory - ProductID, ShopifyID, SKU, Quantity, Price, Discount, TotalCost, PaymentMethod, ShippingAddress, OrderStatus
* CustomerInfo - CustomerID, FirstName, LastName, Email, Phone, Address, City, State, ZipCode, PreferredBrand, MembershipLevel

_Output_
```json
{{
  "thought": "A shipping address is composed of Address, City, State, and ZipCode.",
  "PurchaseHistory": ["ShippingAddress"],
  "CustomerInfo": ["Address", "City", "State", "ZipCode"]
}}
```

#############
User: You see how we ave the GA data and the ad spend data?
Agent: Yes, what would you like to do with them?
User: Let's merge the data from the two tables to see if there's a correlation between the number of clicks and the revenue.
* google_analytics_08122003_08132003 - EventTimestamp, Sessions, Users, Pageviews, PagesPerSession, AvgSessionDuration, BounceRate, Revenue, Transactions, EcommerceConversionRate
* adspend_week_19 - Campaign, AdGroup, Impressions, Clicks, Cost, AvgCPC, Conversions, ConversionRate, CostPerConversion, Month, Day, Year

_Output_
```json
{{
  "thought": "Timestamps are often composed of a date and time, so EventTimestamp and Month, Day, Year are likely to be related.",
  "google_analytics_08122003_08132003": ["EventTimestamp"],
  "adspend_week_19": ["Month", "Day", "Year"]
}}
```

#############
{history}
* {table1} - {columns1}
* {table2} - {columns2}

_Output_
"""

align_columns_prompt = """We are currently merging two tables together, but the connecting columns from each table do not obey a simple 1-to-1 mapping.
Rather, there are {num_left} columns on the left table (called '{left_table}') and {num_right} on the right table (called '{right_table}').
Given the conversation history and supporting details, your task is to determine the best way to align the columns together.
In other words, we want to find the best way to map the columns from the left table to the columns in the right table.

Please start by examining the left and right columns, and then decide on their optimal alignment. When columns are aligned to each other, they will be grouped together.
Each mapping is designated a name in the form 'm' followed by the index of the group: m1, m2, m3, etc. In general, the number of mappings should never exceed beyond 'm4'.
Your entire response should be in well-formatted JSON including keys for thought (string) and alignment (dict of lists), with no further explanations after the JSON output.
The keys of the alignment dictionary should be the names of the mappings, and the values should be lists of tab-col dicts.

For example,
#############
_Conversation History_
User: Hey, we got customer complaints about delayed deliveries in Munich. Can you check what's going on?
Agent: I see the Munich store has high order volume, but I can only see their retail metrics.
User: Right - we need to know if it's a store issue or a warehouse capacity problem. Can you map out which warehouses support each store? That might help us spot the bottleneck.

Left table: RetailLocations
Left columns: StoreId, OpenDate, City, Country, SquareFootage, MonthlyRent, StoreManager, RegionalDistrict
Preview of connection columns:
| City        | Country  |
|-------------|----------|
| Munich      | Germany  |
| Lyon        | France   |
| Bologna     | Italy    |
| Marseille   | France   |
| Hamburg     | Germany  |
| Dresden     | Germany  |
| Toulouse    | France   |
| Florence    | Italy    |

Right table: distribution_centers
Right columns: warehouse_id, loc_city, loc_country, max_capacity_tons, loading_docks, operating_hours, facility_type, lease_expiration, annual_throughput
Preview of connection columns:
| loc_city     | loc_country |
|:-------------|:------------|
| Bordeaux     | France      |
| Florence     | Italy       |
| Marseille    | France      |
| Munich       | Germany     |
| Nice         | France      |
| Leipzig      | Germany     |
| Milan        | Italy       |
| Berlin       | Germany     |

_Output_
```json
{{
  "thought": "City clearly matches loc_city, while Country matches loc_country.",
  "alignment": {{
    "m1": [
      {{"tab": "RetailLocations", "col": "City"}},
      {{"tab": "distribution_centers", "col": "loc_city"}}
    ],
    "m2": [
      {{"tab": "RetailLocations", "col": "Country"}},
      {{"tab": "distribution_centers", "col": "loc_country"}}
    ]
  }}
}}
```

_Conversation History_
User: We need to figure out the tax status for our network providers so we can process claims correctly.
Agent: I'm having trouble with this query, can you help me figure out the right columns to join these tables?
User: The company name goes with the TIN number, which we can cross reference with the state to make sure they are connected.

Left table: NetworkProviders
Left columns: specialty_type, network_status, contract_start, contract_end, preferred_provider, daily_capacity, network_region, company_name, last_review_date
Preview of connection columns:
| company_name                    |
|---------------------------------|
| Alabama University Orthopedic   |
| Health Clinic of North Carolina |
| MSS Valley Medical Group        |
| Summit Medical Associates       |
| Memorial Hospital of Tennessee  |
| Western Kentucky Healthcare     |
| Riverside Family Practice       |

Right table: TaxRegistration
Right columns: ProviderCategory, TIN_Number, TaxStatus, RegistrationDate, EINExpiration, PaymentTerms, State, BillingFrequency, ComplianceStatus
Preview of connection columns:
| TIN_Number                              | State          |
|-----------------------------------------|----------------|
| ORTHOPEDICS AT UNIVERSITY OF ARKANSAS   | Arkansas       |
| GAINSVILLE MEDICAL CENTER               | Florida        |
| ALABAMA UNIVERSITY MEDICAL GROUP        | Alabama        |
| MOUNTAIN STATE HEALTHCARE SERVICES      | West Virginia  |
| BAYSIDE REGIONAL HOSPITAL               | South Carolina |
| KENTUCKY VALLEY SPECIALISTS             | Kentucky       |
| METROPOLITAN SPECIALTY CLINIC           | Mississippi    |

_Output_
```json
{{
  "thought": "The company_name column from the left table aligns with the TIN_Number while the State column can be used as an extra check.",
  "alignment": {{
    "m1": [
      {{"tab": "NetworkProviders", "col": "company_name"}},
      {{"tab": "TaxRegistration", "col": "TIN_Number"}},
      {{"tab": "TaxRegistration", "col": "State"}}
    ]
  }}
}}
```

_Conversation History_
User: The engineering recruitment campaign is performing way better than our other roles. What are we doing differently there?
Agent: I am thinking about joining the social media metrics with HR campaigns to measure engagement rate, should we do that?
User: Good idea, let's figure out what's working so we can replicate it.

Left table: Recruitment Campaigns
Left columns: campaignName, targetRoles, budgetAllocated, startDate, endDate, hiringGoal, jobFamily, targetLocation, departmentCode
Preview of connection columns:
| campaignName                                     | startDate   | endDate    |
|:-------------------------------------------------|:------------|:-----------|
| LI - Senior Leadership - Sponsored Posts         | 2023-09-01  | 2023-10-25 |
| FB - Entry Level Hiring - Photo Series           | 2023-09-15  | 2023-11-30 |
| Insta - Company Culture - Story Highlights       | 2023-09-20  | 2023-11-15 |
| LI - Tech Talent - Job Listing Bundle            | 2023-10-15  | 2023-12-20 |
| LI - Product Manager - Premium Posts             | 2023-11-01  | NULL       |
| FB - Engineering Roles - Video Campaign          | 2023-11-15  | 2024-01-31 |
| LI - Remote Positions - Sponsored Series         | 2023-12-01  | NULL       |
| Insta - Career Growth - Reel Series              | 2023-12-10  | 2024-02-28 |

Right table: social_media_metrics
Right columns: launch_date, completion_date, total_spend, click_through_rate, cost_per_click, engagement_rate, application_count, ad_platform, campaign_status
Preview of connection columns:
| launch_date | completion_date | ad_platform |
|-------------|-----------------|-------------|
| 2023-09-18  | 2023-11-25      | Facebook    |
| 2023-09-20  | 2023-11-15      | LinkedIn    |
| 2023-09-22  | 2023-11-20      | Instagram   |
| 2023-10-12  | 2023-12-15      | LinkedIn    |
| 2023-11-05  | NULL            | LinkedIn    |
| 2023-11-15  | 2024-01-31      | Facebook    |
| 2023-12-05  | NULL            | LinkedIn    |
| 2023-12-12  | 2024-02-25      | Instagram   |

_Output_
```json
{{
  "thought": "Start and end dates correspond with launch and completion dates. Campaign contains the abbreviation of the ad platform as part of the name, which can serve as an extra check.",
  "alignment": {{
    "m1": [
      {{"tab": "Recruitment Campaigns", "col": "campaignName"}},
      {{"tab": "social_media_metrics", "col": "ad_platform"}}
    ],
    "m2": [
      {{"tab": "Recruitment Campaigns", "col": "startDate"}},
      {{"tab": "social_media_metrics", "col": "launch_date"}}
    ],
    "m3": [
      {{"tab": "Recruitment Campaigns", "col": "endDate"}},
      {{"tab": "social_media_metrics", "col": "completion_date"}}
    ]
  }}
}}
```

_Conversation History_
User: Dr. Thompson mentioned several patients couldn't schedule follow-ups because of insurance verification issues.
Agent: That's unfortunate. Is there a way we can check their insurance status?
User: Can you check all our active patients against the latest insurance eligibility file?

Left table: VHM_patient_records
Left columns: patient_name, last_visit_date, assigned_provider, payment_status, balance_due, chart_number, patient_status, birth_month, birth_day, birth_year
Preview of connection columns:
| birth_month | birth_day | birth_year | patient_name     |
|-------------|-----------|------------|------------------|
| Mar         | 15        | 1992       | Priyanka Patel   |
| Jun         | 23        | 1988       | Marcus Rodriguez |
| Sep         | 7         | 1995       | Aisha Thompson   |
| Jan         | 29        | 1983       | Howard Chen      |
| Dec         | 11        | 1990       | Liam O'Connor    |
| Aug         | 4         | 1987       | Sofia Esposito   |
| Nov         | 19        | 1994       | Devon Williams   |

Right table: Insurance Eligibility
Right columns: Applicant, PolicyNumber, CoverageStartDate, CoverageEndDate, PlanType, DOB, GroupNumber, BenefitLevel, DependentStatus
Preview of connection columns:
| Applicant       | DOB         |
|-----------------|-------------|
| Priyanka Patel  | 1992-03-15  |
| Karim Hassan    | 1991-05-12  |
| Sofia Esposito  | 1987-08-04  |
| Nina Kowalski   | 1989-10-30  |
| Devon Williams  | 1994-11-19  |
| Rachel Goldstein| 1986-07-25  |
| Liam O'Connor   | 1990-12-11  |

_Output_
```json
{{
  "thought": "DOB stands for Date of Birth, which matches the birth month, day, and year columns. Patient name can further confirm the match against the applicant.",
  "alignment": {{
    "m1": [
      {{"tab": "VHM_patient_records", "col": "birth_month"}},
      {{"tab": "VHM_patient_records", "col": "birth_day"}},
      {{"tab": "VHM_patient_records", "col": "birth_year"}},
      {{"tab": "Insurance Eligibility", "col": "DOB"}}
    ],
    "m2": [
      {{"tab": "VHM_patient_records", "col": "patient_name"}},
      {{"tab": "Insurance Eligibility", "col": "Applicant"}}
    ]
  }}
}}
```

_Conversation History_
User: Our email campaigns have really low open rates for the loyalty rewards program.
Agent: Sure, I can help with that. Can you tell me which campaigns you're referring to?
User: Could you cross-reference the CDP data with the email platform? We need to make sure we're using their preferred contact methods.
Agent: It sounds like you want to join the Amperity and Email tables, but I couldn't find any shared columns.
User: Please join the two tables together based on the name and email address.

Left table: amperity_contacts
Left columns: first_name, last_name, email_address, account_status, last_purchase_date, loyalty_tier, preferred_language, customer_id, opt_in_status
Preview of connection columns:
| first_name | last_name    | email_address               |
|------------|--------------|-----------------------------|
| Rohan      | Desai        | <EMAIL>         |
| Gabriel    | Santos       | <EMAIL>        |
| Ayesha     | Khan         | <EMAIL>        |
| Elena      | Popov        | <EMAIL>       |
| Xavier     | Bennett      | <EMAIL>   |
| Michelle   | Park         | <EMAIL>         |
| Lucia      | Morales      | <EMAIL>|

Right table: EmailSubscribers
Right columns: full name, email status, last campaign, click rate, preferred format, contact info, device type, subscription date, marketing segment
Preview of connection columns:
| full name         | contact info              |
|-------------------|---------------------------|
| Rohan Desai       | <EMAIL>       |
| Christopher Lee   | <EMAIL>    |
| Elena Popov       | <EMAIL>     |
| Sarah Mitchell    | <EMAIL>   |
| Michelle Park     | <EMAIL>       |
| Lucia Morales     | <EMAIL>         |
| Benjamin Foster   | <EMAIL>     |

_Output_
```json
{{
  "thought": "Full name is a combination of first and last name. By looking at the contents, we can see that contact info is an email address.",
  "alignment": {{
    "m1": [
      {{"tab": "amperity_contacts", "col": "first_name"}},
      {{"tab": "amperity_contacts", "col": "last_name"}},
      {{"tab": "EmailSubscribers", "col": "full name"}}
    ],
    "m2": [
      {{"tab": "amperity_contacts", "col": "email_address"}},
      {{"tab": "EmailSubscribers", "col": "contact info"}}
    ]
  }}
}}
```
#############
Now it's your turn! Please decide on the alignment of the connection columns in well-formatted JSON with no further explanations before or after output.

_Conversation History_
{history}

_Supporting Details_
Left table: {left_table}
Left columns: {left_cols}
Preview of connection columns:
{left_preview}

Right table: {right_table}
Right columns: {right_cols}
Preview of connection columns:
{right_preview}

_Output_
"""

join_tables_prompt = """As seen in the conversation history, our situation requires joining two dataframes together to form a new dataframe.
Specifically, we are trying to join the 'db.{left_tab}' and 'db.{right_tab}' tables together to form a new table called 'db.{target_tab}'.
There {crew_description} of columns to align between the two tables. In particular,
{alignment_snippet}
  * Always choose to keep the non-null values when there are conflicts between the alignment columns
  * To aid you, an alignment function is proposed where df_1 and df_2 represent '{left_tab}' and '{right_tab}' respectively

Furthermore, you must also perform the following:
  * Rename columns if the same column name appears in both tables
  * Avoid over-complicating the function. Namely, if the data preview does not suggest that a regex is needed, then don't use one.
  * Fill in missing cells with a default value (e.g. 0) if and only if they are numeric data types
  * Sort the alignment columns. For example, from earliest to latest for dates or alphabetically for usernames
  * Re-arrange the columns in the target table to a good order. Namely,
    - id columns should be at the front
    - followed by the alignment columns, which are likely dates, users, or companies
    - followed by the columns from the left table
    - and finally the columns from the right table

Given the conversation history and the supporting details, please generate the Pandas code to join the two tables together.
The supporting details includes the a markdown preview of the kept columns from each table, as well as the alignment function.
Your entire response should be directly executable Python code with inline comments, with no explanations before or after the code.

For example,
---
_Conversation History_
User: Can I join the email campaign data with the support ticket data?
Agent: I'm not seeing any shared ids, how did you want to join these tables together?
User: By date

_Source Samples_
Left table: EmailCampaigns
| open_rate | click_rate | campaign_theme                    | campaign_month |
|-----------|------------|-----------------------------------|----------------|
| 22.4      | 3.8        | New Year Product Launch           | 2024-01        |
| 19.7      | 2.9        | Valentine's Day Flash Sale        | 2024-02        |
| 21.2      | 3.2        | Spring Collection Preview         | 2024-04        |
| 18.9      | 2.7        | Winter Clearance Final Days       | 2023-12        |
| 20.8      | 3.4        | Loyalty Member Exclusive Access   | 2024-07        |
| 23.1      | 4.1        | March Madness Special             | 2024-03        |
| 20.3      | 3.6        | Spring Break Early Bird           | 2024-05        |

Right table: SupportTicketsDetail
| ticket_count | category          | ticket_date |
|--------------|-------------------|-------------|
| 342          | Account Access    | 2024-01-15  |
| 289          | Payment Issues    | 2024-02-08  |
| 456          | Product Inquiry   | 2024-03-22  |
| 378          | Returns           | 2024-01-29  |
| 412          | Shipping Status   | 2024-02-14  |
| 267          | Account Access    | 2024-03-07  |
| 521          | Payment Issues    | 2024-02-28  |
| 398          | Product Inquiry   | 2024-01-08  |

Target Table: EmailSupport
Alignment function:
right_df['ticket_date'] = pd.to_datetime(right_df['ticket_date']).dt.strftime('%Y-%m')

_Output_
```python
# Convert ticket_date to match campaign_month format
db.SupportTicketsDetail['ticket_month'] = pd.to_datetime(db.SupportTicketsDetail['ticket_date']).dt.strftime('%Y-%m')
# Aggregate ticket data by month
monthly_tickets = db.SupportTicketsDetail.groupby('ticket_month').agg({{
  'ticket_count': 'sum',
  'category': lambda x: ', '.join(sorted(set(x)))
}}).reset_index()
# Rename aggregated columns for clarity
monthly_tickets = monthly_tickets.rename(columns={{
  'ticket_count': 'total_tickets',
  'category': 'ticket_categories'
}})
# Perform the merge
db.EmailSupport = pd.merge(db.EmailCampaigns, monthly_tickets, left_on='campaign_month', right_on='ticket_month', how='left')
# Reorder columns according to id, alignment, left, right
column_order = ['campaign_month', 'campaign_theme', 'open_rate', 'click_rate', 'total_tickets', 'ticket_categories']
# Apply column ordering and sort by campaign_month
db.EmailSupport = db.EmailSupport[column_order].sort_values('campaign_month')
```

_Conversation History_
User: I need to match our trading records against a list of sanctioned companies, but it was scanned from a PDF using OCR, so the company names are broken up. Can you help?
Agent: Hmm, I can take a look. Are you trying to join the two tables together?
User: Yes, please join them together if you can.

_Source Samples_
Left table: TradingRecords_2024Q1
| company_name         | transaction_date | trade_value  |
|----------------------|------------------|--------------|
| Ace Global Trading   | 2024-01-15       | 1250000.00   |
| Phoenix Exports      | 2024-01-16       | 780000.00    |
| Crown Shipping Ltd   | 2024-01-16       | 1680000.00   |
| Silverstone Mining   | 2024-01-17       | 945000.00    |
| Meridian Resources   | 2024-01-17       | 1150000.00   |
| Quantum Industries   | 2024-01-18       | 1460000.00   |
| GT Intelligence      | 2024-01-18       | 998000.00    |
| Harbor Logistics     | 2024-01-19       | 1420000.00   |

Right table: SanctionedCompanies
| entity_name           | risk_level | jurisdiction |
|-----------------------|------------|--------------|
| Me ridan  Resources   | High       | International|
| Silv erst one Mining  | Medium     | Regional     |
| Quantum Industr ies   | High       | International|
| Red Dragon Trading    | Medium     | Regional     |
| Ace Glo bal Trading   | High       | International|
| Atlas  Petrole um     | Low        | Regional     |
| Crown Shipping  Ltd   | Medium     | Regional     |
| Sunburst En ergy      | High       | Regional     |

Target Table: SanctionedTrading
Alignment function:
left_df['company_name'] = left_df['company_name'].str.replace(r'\\s+', '', regex=True)
right_df['entity_name'] = right_df['entity_name'].str.replace(r'\\s+', '', regex=True)

_Output_
```python
# Clean trading records and sanctioned companies
db.TradingRecords_2024Q1['company_name'] = (db.TradingRecords_2024Q1['company_name']
  .str.strip().str.replace(r'\\s+', '', regex=True).str.lower()
)
db.SanctionedCompanies['entity_name'] = (db.SanctionedCompanies['entity_name']
  .str.strip().str.replace(r'\\s+', '', regex=True).str.lower()
)
# Perform the merge
db.SanctionedTrading = pd.merge(
  db.TradingRecords_2024Q1, db.SanctionedCompanies,
  left_on='company_name', right_on='entity_name', how='outer', indicator=True
)
# Add status column to identify matched/unmatched records
db.SanctionedTrading['status'] = db.SanctionedTrading['_merge'].map({{
  'left_only': 'No Sanctions',
  'right_only': 'No Trading Activity',
  'both': 'Active Trading & Sanctions'
}})
# Reorder columns according to specified pattern, fill missing values, and sort
column_order = ['company_name', 'transaction_date', 'trade_value', 'risk_level', 'jurisdiction', 'status']
db.SanctionedTrading = (db.SanctionedTrading[column_order]
  .sort_values(['transaction_date', 'company_name'])
  .fillna({{'trade_value': 0, 'risk_level': 'Unknown', 'jurisdiction': 'Unknown'}})
)
```

_Conversation History_
User: Are there any patterns in customer satisfaction ratings based on the distance from the restaurant?
Agent: I can look into that. What kind of patterns are you looking for?
User: I'm looking for any patterns that might indicate if delivery satisfaction is correlated with distance from the restaurant.
Agent: Ok, I'll need to join the two tables together based on the addresses.
User: Ok, let's do that.

_Source Samples_
Left table: Customer Feedback
| deliveryDate | streetAddress         | satisfactionScore | deliveryTimeMins  |
|--------------|-----------------------|-------------------|-------------------|
| 2024-06-18   | 123 Oak Street        | 4.5               | 25                |
| 2024-06-15   | 456 Maple Ave         | 4.0               | 35                |
| 2024-06-16   | 789 Washington Blvd   | 3.5               | 45                |
| 2024-06-16   | 321 Pine Ridge Rd     | 4.8               | 20                |
| 2024-06-17   | 654 Market St         | 3.8               | 40                |
| 2024-06-17   | 987 Broadway Ave      | 4.2               | 30                |
| 2024-06-15   | 147 Jefferson Blvd    | 4.6               | 25                |
| 2024-06-18   | 258 Lincoln Road      | 3.9               | 35                |

Right table: restaurant_locations
| street_address          | cuisine_type |
|-------------------------|--------------|
| 123 Oak Street          | American     |
| 456 Maple Avenue        | Chinese      |
| 789 Washington Blvd     | American     |
| 321 Pine Ridge Road     | Italian      |
| 654 Market Street       | Japanese     |
| 987 Broadway Avenue     | American     |
| 147 Jefferson Boulevard | Thai         |
| 258 Lincoln Rd          | Mexican      |

Target Table: DeliverySatisfaction
Alignment function:
abbrev_map = {{'St': 'Street', 'Ave': 'Avenue', 'Blvd': 'Boulevard', 'Rd': 'Road'}}
left_df['streetAddress'] = left_df['streetAddress'].replace(abbrev_map, regex=True)
right_df['street_address'] = right_df['street_address'].replace(abbrev_map, regex=True)

_Output_
```python
# Standardize addresses
abbrev_map = {{'St': 'Street', 'Ave': 'Avenue', 'Blvd': 'Boulevard', 'Rd': 'Road'}}
db.CustomerFeedback['streetAddress'] = db.CustomerFeedback['streetAddress'].replace(abbrev_map, regex=True)
db.restaurant_locations['street_address'] = db.restaurant_locations['street_address'].replace(abbrev_map, regex=True)
# Perform the merge
db.DeliverySatisfaction = pd.merge(
  db.CustomerFeedback, db.restaurant_locations,
  left_on='streetAddress', right_on='street_address', how='outer'
)
# Reorder columns according to id, alignment, left, right and sort
column_order = ['streetAddress', 'deliveryDate', 'satisfactionScore', 'deliveryTimeMins', 'cuisine_type']
db.DeliverySatisfaction = db.DeliverySatisfaction[column_order].sort_values(['deliveryDate', 'streetAddress'])
```

_Conversation History_
User: Are maintenance requests related to utility usage?
Agent: That's a good question. I'll need to join the two tables together based on the apartment numbers.
User: Ok, great. let's do that.

_Source Samples_
Left table: maintenance_requests
| apartment      | request_date | maintenance_type   |
|----------------|--------------|--------------------|
| APT 5A         | 2024-01-17   | Appliance Repair   |
| Apt. 4B        | 2024-01-15   | Plumbing           |
| Unit #2A       | 2024-01-16   | Electrical         |
| Apartment 3C   | 2024-01-16   | HVAC               |
| Unit 1B        | 2024-01-17   | Plumbing           |
| Apt #4C        | 2024-01-18   | Electrical         |
| Apartment 2B   | 2024-01-18   | HVAC               |
| APT. 3A        | 2024-01-19   | Appliance Repair   |

Right table: utility_usage
| apt_number | utility_type |
|------------|--------------|
| 4B         | Water        |
| 3C         | Electric     |
| 3A         | Water        |
| 5A         | Water        |
| 1B         | Water        |
| 4C         | Electric     |
| 2A         | Water        |
| 2B         | Electric     |

Target Table: MaintenanceUtility
Alignment function:
df_1['apartment'] = df_1['apartment'].str.replace('#', '').str.split().str[-1]

_Output_
```python
# Get the apartment number by taking the last part, no need to overthink it with regex
db.maintenance_requests['apartment'] = db.maintenance_requests['apartment'].str.replace('#', '').str.split().str[-1]
# Perform the merge and order columns
db.MaintenanceUtility = pd.merge(
  db.maintenance_requests, db.utility_usage,
  left_on='apartment', right_on='apt_number', how='outer'
)
# Reorder columns and sort
column_order = ['apartment', 'request_date', 'maintenance_type', 'utility_type']
db.MaintenanceUtility = db.MaintenanceUtility[column_order].sort_values(['request_date', 'apartment'])
```

_Conversation History_
User: I want to analyze how our daily inventory snapshots relate to actual sales transactions.
Agent: It sounds like you want to perform a Sales and Inventory Correlation analysis. Is that right?
User: Yes, that's right.
Agent: In order to perform this analysis, we need to join the sales_transactions and daily_inventory_snapshots tables.
User: Ok, how should we join them?

_Source Samples_
Left table: sales_transactions
| transaction_timestamp   | product_category | units_sold |
|-------------------------|------------------|------------|
| 2024-01-15 09:23:45     | Electronics      | 3          |
| 2024-01-15 14:17:32     | Electronics      | 2          |
| 2024-01-15 16:45:12     | Clothing         | 5          |
| 2024-01-16 10:30:00     | Electronics      | 1          |
| 2024-01-16 11:20:15     | Clothing         | 4          |
| 2024-01-16 15:45:22     | Electronics      | 2          |
| 2024-01-17 08:15:30     | Clothing         | 3          |
| 2024-01-17 13:40:18     | Electronics      | 4          |

Right table: daily_inventory_snapshots
| snapshotdate | product_category | inventory_id |
|--------------|------------------|--------------|
| 2024-01-15   | Electronics      | 14545725     |
| 2024-01-15   | Clothing         | 14523882     |
| 2024-01-16   | Electronics      | 14514044     |
| 2024-01-16   | Clothing         | 14522393     |
| 2024-01-17   | Electronics      | 14513410     |
| 2024-01-17   | Clothing         | 14522316     |
| 2024-01-18   | Electronics      | 14518330     |
| 2024-01-18   | Clothing         | 14562313     |

Target Table: sales_inventory
Alignment function:
left_df['transaction_date'] = pd.to_datetime(left_df['transaction_date']).dt.date

_Output_
```python
# Fix the column naming in alignment code to convert timestamp into date
db.sales_transactions['transaction_date'] = pd.to_datetime(db.sales_transactions['transaction_timestamp']).dt.date
# Define new variable for merging only because there are multiple columns
left_cols = ['transaction_date', 'product_category']
right_cols = ['snapshotdate', 'product_category']
# Aggregate sales by date and product category
sales_daily = db.sales_transactions.groupby(left_cols)['units_sold'].sum().reset_index()
# Perform the merge
db.sales_inventory = pd.merge(
  sales_daily, db.daily_inventory_snapshots,
  left_on=left_cols, right_on=right_cols, how='outer'
)
# Reorder columns based on id, alignment, left, right and then sort
column_order = ['inventory_id', 'transaction_date', 'product_category', 'units_sold']
db.sales_inventory = db.sales_inventory[column_order].sort_values(left_cols)
```

_Conversation History_
User: I want to combine vendor payment data with vendor risk assessment data from our compliance system.
Agent: Ok, I see the vendor payments table and the vendor risk assessments table. Is that right?
User: Yes, that's right. Join them based on the vendor name.

_Source Samples_
Left table: vendor_payments
| vendor_name         | payment_date | payment_amount | payment_terms |
|---------------------|-------------|----------------|---------------|
| McDonald's          | 2024-01-15  | 25,450.00      | Net 30        |
| AT&T Global         | 2024-01-16  | 18,320.00      | Net 45        |
| Sysco-Foods Ltd     | 2024-01-16  | 42,150.00      | Net 30        |
| Johnson & Johnson   | 2024-01-17  | 65,800.00      | Net 60        |
| Costco Wholesale    | 2024-01-17  | 33,240.00      | Net 30        |
| U.S. Bank, Inc.     | 2024-01-18  | 12,780.00      | Net 45        |
| Ben & Jerry's       | 2024-01-18  | 8,920.00       | Net 30        |
| Brother-Tech        | 2024-01-19  | 15,640.00      | Net 30        |

Right table: vendor_risk_assessments
| company_name       | risk_score | last_audit_date | industry_category |
|--------------------|------------|-----------------|-------------------|
| US Bank Inc        | 82         | 2023-12-15      | Financial         |
| McDonalds          | 78         | 2023-12-20      | Food/Beverage     |
| Brother Tech       | 75         | 2023-12-22      | Technology        |
| Wells Fargo & Co   | 85         | 2023-12-28      | Financial         |
| Sysco Foods        | 80         | 2024-01-02      | Distribution      |
| Ben and Jerrys     | 77         | 2024-01-05      | Food/Beverage     |
| Adobe Inc          | 79         | 2024-01-08      | Technology        |
| ATT Global         | 83         | 2024-01-10      | Telecommunications|

Target Table: PaymentRisk
Alignment function:
removals = '\'|\.|\,|\&|\-|corp|inc|ltd|co|and'
left_df['vendor_name'] = left_df['vendor_name'].str.lower().str.replace(removals, '', regex=True).str.strip()
right_df['company_name'] = right_df['company_name'].str.lower().str.replace(removals, '', regex=True).str.strip()

_Output_
```python
# Define patterns to remove and standardize company names
removals = '\'|\.|\,|\&|\-|corp|inc|ltd|co|and'
# Clean both company name columns
db.vendor_payments['vendor_name'] = db.vendor_payments['vendor_name'].str.lower().str.replace(removals, '', regex=True).str.strip()
db.vendor_risk_assessments['company_name'] = db.vendor_risk_assessments['company_name'].str.lower().str.replace(removals, '', regex=True).str.strip()
# Perform the merge
db.PaymentRisk = pd.merge(
  db.vendor_payments, db.vendor_risk_assessments,
  left_on='vendor_name', right_on='company_name', how='outer'
)
# Reorder columns based on id, alignment, left, right and then sort
column_order = ['vendor_name', 'payment_date', 'payment_amount', 'payment_terms', 'risk_score', 'last_audit_date', 'industry_category']
db.PaymentRisk = db.PaymentRisk[column_order].sort_values('vendor_name')
```
---
Please output clear and concise Python code that joins the columns correctly, such that it prepares the data for further analysis.
Furthermore, remember to keep the code as simple as possible while still meeting the requirements.

_Conversation History_
{history}

_Supporting Details_
Left table: {left_tab}
Preview:
{left_preview}

Right table: {right_tab}
Preview:
{right_preview}

Target Table: {target_tab}
Target columns: {target_cols}
Alignment function:
{alignment_func}

_Output_
"""

tab_col_move_prompt = """Given the conversation history and supporting details, please help the user move {task} to a different place in the spreadsheet.
To aid you in this task, the supporting details will include the relevant columns and their positions in the table.

Start by thinking about where the source column appears in relation to the target position, which may affect off-by-one calculations.
Then keeping this in mind, generate well-formatted Python code to cut and paste the column(s) to the new position.
You can access dataframes as 'db' followed by a table name: `db.{table}`. You will also have access to Pandas as 'pd', so there is no need to import it.
Please only output executable Python code with inline comments. It is absolutely critical there are no explanations after the code.

For example,
---
_Conversation History_
User: Do we have any data about when deliveries were made?
Agent: We have EstDeliveryTime and DeliveryStatus columns in the CustomerOrders table.
User: OK, can you move the delivery status to right before the order status?

_Supporting Details_
Table: CustomerOrders
Columns: OrderID (1), CustomerID (2), RestaurantID (3), DeliveryStatus (4), TotalAmount (5), DeliveryAddress (6), OrderStatus (7), EstDeliveryTime (8), SpecialInstructions (9)
Target Position: 7

_Output_
```python
columns = list(db.CustomerOrders.columns)
source_pos = columns.index('DeliveryStatus')
# source col occurs before target col, so -1 adjustment is needed to account for popping the column out
target_pos = columns.index('OrderStatus') - 1
columns.insert(target_pos, columns.pop(source_pos))
db.CustomerOrders = db.CustomerOrders[columns]
```

_Conversation History_
User: How many leads are we working with here?
Agent: There are 461 leads in the sales_leads table.
User: I'd like to see the first and last name columns up front, followed by email and phone.

_Supporting Details_
Table: sales_leads
Columns: lead_id (1), email (4), first_name (2), last_name (3), organization (5), lead_source (6), contact_date (7), phone_number (8), notes (9), follow_up_date (10)
Target Position: 1, 2, 3, 4

_Output_
```python
columns = list(db.sales_leads.columns)
# keep the primary key column at the beginning
desired_cols = ['lead_id', 'first_name', 'last_name', 'email', 'phone_number']
other_cols = [col for col in columns if col not in desired_cols]
db.sales_leads = db.sales_leads[desired_cols + other_cols]
```

_Conversation History_
User: Do we have any data about what spend was incurred for each campaign?
Agent: We have EstimatedAdSpend and PerClickFee columns in the PaidSearch table.
User: OK, can you move the per click fee to right before the vendor fee?

_Supporting Details_
Table: PaidSearch
Columns: CampaignID (1), CampaignName (2), StartDate (3), EndDate (4), VendorFee (5), EstimatedAdSpend (6), Clicks (7), Impressions (8), PerClickFee (9), SpecialInstructions (10)
Target Position: 5

_Output_
```python
columns = list(db.PaidSearch.columns)
source_pos = columns.index('PerClickFee')
# source col occurs after target col, so no adjustment is needed
target_pos = columns.index('VendorFee')
columns.insert(target_pos, columns.pop(source_pos))
db.PaidSearch = db.PaidSearch[columns]
```

_Conversation History_
User: What is the user_id about? Is that connected to anything?
Agent: The structure seems similar to the lead_id in the sales_leads table, but I'm not sure.
User: Anyways, can we move them to the end of the subs and activity?

_Supporting Details_
Table: subscriptions, user_activity
Columns: subscription_id (1), user_id (2), plan_name (3), sub_timestamp (4), billing_cycle (5), payment_status (6), renewal_notice (7)
activity_id (1), user_id (2), activity_type (3), timestamp (4), duration (5), data_source (6), outcome (7), error_log (8)
Target Position: -1, -1

_Output_
```python
for df in [db.subscriptions, db.user_activity]:
 cols = list(df.columns) 
 cols.append(cols.pop(cols.index('user_id')))
 df = df[cols]
```

_Conversation History_
User: Can you make a new column that combines the campaign name and the channel?
Agent: Sure, I have added a new CombinedChannel column. How does it look?
User: I want it right next to the current channel column

_Supporting Details_
Table: Campaigns
Columns: CampaignID (1), CampaignName (2), StartDate (3), EndDate (4), Channel (5), TargetAudience (6), Budget (7), ResponseRate (8), CombinedChannel (9)
Target Position: 5

_Output_
```python
columns = list(db.Campaigns.columns) 
source_pos = columns.index('CombinedChannel')
# source col occurs after target col, so no adjustment is needed
target_pos = columns.index('Channel') + 1
columns.insert(target_pos, columns.pop(source_pos))
db.Campaigns = db.Campaigns[columns]
```

_Conversation History_
User: Calculate total amount by multiplying the price and number of months, and also taking into account the discount.
Agent: Certainly, the total_amount column has been created.
User: Can you duplicate that column and move it to the end of the table?

_Supporting Details_
Table: salesforce2023June
Columns: SFID (1), subscription type (2), monthly price (3), number of months (4), is_discount (5), discount_rate (6), how_did_you_hear (7), referral_source (8), total_amount (9)
Target Position: -1

_Output_
```python
# Pandas automatically places the new column at the end
db.salesforce2023June['total_amount_2'] = db.salesforce2023June['total_amount']
```

_Conversation History_
User: Sort the promos by the discount rate
Agent: Done. Just so you know this can also be done directly in the table by clicking on the column header.
User: yea got it. Now swap the order of the discount rate and the applicable product columns

_Supporting Details_
Table: Promotions
Columns: PromoID (1), PromoName (2), LaunchDate (3), PromoChannel (4), DiscountPercentage (5), ApplicableProducts (6), PromoCode (7), RedemptionCount (8)
Target Position: 6, 5

_Output_
```python
cols = list(db.Promotions.columns)
i, j = cols.index('DiscountPercentage'), cols.index('ApplicableProducts')
# take advantage of tuple unpacking to swap the columns
cols[i], cols[j] = cols[j], cols[i]
db.Promotions = db.Promotions[cols]
```
---
Now it's your turn! Please output clear and concise Python code, ensuring that the columns are moved correctly.

_Conversation History_
{history}

_Supporting Details_
Table: {table}
Columns: {columns}
Target Position: {target}
{additional}
_Output_
"""

row_cell_move_prompt = """Given the conversation history and supporting details, please help the user move {task} to a different place in the spreadsheet.
To aid you in this task, the supporting details will include the relevant columns and their positions in the table.
In the real task, you will also be given a sample of values found in the source and target columns for more context.

Start by thinking about the best way to move the data and then generate well-formatted Python code to do so.
You can access dataframes as 'db' followed by a table name: `db.{table}`. You will also have access to Pandas as 'pd', so there is no need to import it.
Please only output executable Python code with inline comments. It is absolutely critical there are no explanations after the code.

For example,
#############
_Conversation History_
User: How many users to we have that are active and also have a Twitter account?
Agent: There are 6423 active users who also have a Twitter account.
User: Let's bring all customers who are active to the top of the table.

_Supporting Details_
Table: CustomerContact
Columns: CustomerID (1), CustName (2), FavCuisineType (3), ShippingAddress (4), ContactNumber (5), IsActive (6), Twitter (7), Instagram (8), Yelp (9)
Rows: IsActive == True

_Output_
```python
df = db.CustomerContact
db.CustomerContact = pd.concat([
  df[df['IsActive'] == True],
  df[df['IsActive'] == False]
])
```

_Conversation History_
User: Do you see anything wrong with the collorators?
Agent: I do not notice any issues with the collaborators column.
User: Well, I do. Johansson isn't a collaborator, those should be moved to Platform wherever it currently says JHarper

_Supporting Details_
Table: CanvasOutreach
Columns: OutreachID (1), CampaignName (2), TargetAudience (3), Platform (4), ResponseRate (5), Collaborators (6), OutreachDate (7), TrackingCode (8)
Rows: Collaborators == Johansson

_Output_
```python
target_mask = db.CanvasOutreach['Platform'] == 'JHarper'
db.CanvasOutreach.loc[target_mask, 'Platform'] = 'Johansson'
# Clear the collaborator field
source_mask = db.CanvasOutreach['Collaborators'] == 'Johansson'
db.CanvasOutreach.loc[source_mask, 'Collaborators'] = ''
```

_Conversation History_
User: We can also delete the shipping method since it's not needed
Agent: Got it, I have removed the ShippingMethod column.
User: Any purchases that have an empty shipping address should be moved to the bottom of the table

_Supporting Details_
Table: PurchaseHistory
Columns: ProductID (1), ShopifyID (2), SKU (3), Quantity (4), Price (5), Discount (6), TotalCost (7), PaymentMethod (8), ShippingAddress (9), OrderStatus (10)
Rows: ShippingAddress == null

_Output_
```python
df = db.PurchaseHistory
db.PurchaseHistory = pd.concat([
  df[df['ShippingAddress'].notna()],
  df[df['ShippingAddress'].isna()]
])
```

_Conversation History_
User: Now calculate the average cost per click.
Agent: You got it, I have added a column for the AvgCPC.
User: Can we reorder the campaigns so that ad groups that mention Waterproof speakers go right after the Bluetooth speakers?

_Supporting Details_
Table: adspend_week_19
Columns: CampaignName (1), AdGroupName (2), Impressions (3), Clicks (4), Cost (5), AvgCPC (6), Conversions (7), ConversionRate (8), CostPerConversion (9), Month (10), Day (11), Year (12)
Rows: Waterproof speakers in AdGroupName

_Output_
```python
df = db.adspend_week_19
# Find first Bluetooth row and all Waterproof rows
bt_idx = df[df['AdGroupName'].str.contains('Bluetooth', case=False)].index[0]
wp_mask = df['AdGroupName'].str.contains('Waterproof', case=False)

# Split, reorder and concat
top = df.iloc[:bt_idx + 1]
waterproof = df[wp_mask]
rest = df.iloc[bt_idx + 1:].drop(waterproof.index)
db.adspend_week_19 = pd.concat([top, waterproof, rest])
```

_Conversation History_
User: The values got mixed up, a bunch of the last names are actually companies.
Agent: Which rows are you referring to?
User: We should cut any rows mentioning 'Partners' and set them as the company name if the company row is blank.

_Supporting Details_
Table: Hubspot_revised
Columns: Contact ID (1), First Name (2), Last Name (3), Email (4), Phone Number (5), Company (6), Job Title (7), Industry (8), Lead Source (9), Lead Status (10)
Rows: Last Name contains 'Partners'

_Output_
```python
mask = db.Hubspot_revised['Last Name'].str.contains('Partners', case=False, na=False)
empty_company = db.Hubspot_revised['Company'].isna()
# Move "Partners" entries to empty Company fields
db.Hubspot_revised.loc[mask & empty_company, 'Company'] = db.Hubspot_revised.loc[mask & empty_company, 'Last Name']
db.Hubspot_revised.loc[mask & empty_company, 'Last Name'] = ''
```

_Conversation History_
User: First remove any text from Location that is within parentheses
Agent: Done, the Location column has been cleaned.
User: Ok next, swap any values in Survey type and location if the location ends with _CSAT

_Supporting Details_
Table: surveyMonkey
Columns: SurveyID (1), ContactEmail (2), Date (3), SatisfactionScore (4), FreeFormComments (5), SurveyType (6), Duration (7), Location (8), LikelyToRecommend (9)
Rows: Location ends with _CSAT

_Output_
```python
mask = db.surveyMonkey['Location'].str.endswith('_CSAT', na=False)
idx = db.surveyMonkey[mask].index
db.surveyMonkey.loc[idx, ['SurveyType', 'Location']] = db.surveyMonkey.loc[idx, ['Location', 'SurveyType']].values
```
#############
For additional context, we are also providing a sample of values from the relevant columns. Now, please output executable Python code with inline comments to move the {task} as requested.

_Conversation History_
{history}

_Supporting Details_
Table: {table}
Columns: {columns}
Rows: {rows}
Samples:
{additional}

_Output_
"""

ner_tag_prompt = """Given the conversation history and the candidate source columns, please decide what how the user is trying to join the two tables together.
Specifically, the different join options are:
  * PER - person; such as a customer or user, sometimes represented as a username, email, or phone number
  * DATE - date or time; including subtypes such as month, week, quarter, or timestamp
  * LOC - location; also known as geography, including subtypes such as city, state, country, or address
  * ORG - organization; such as a company or business, sometimes represented as a website or department
  * ID - identifier; choose this when we join based on an exact match or a direct lookup, such as a foreign key pointing to a primary key
  * O - other; any other type of data that does not fit into the above categories, or when the connection is unclear

While our categories closely resemble the CoNLL-2003 labeling scheme, our true task is joining tables, not named entity recognition (NER).
This is why an email or phone number is tagged as 'PER', and why any exact match is tagged as 'ID', even if the column type is not technically a unique identifier.
Remember that joining based on two primary keys does not make sense, so avoid picking 'ID' in this case.
Please start by examining the source columns from each table, and think carefully about what type of entity they represent.
Then generate well-formatted JSON output including your thoughts (string) and the entity type (token) for the join operation.

For example,
#############
_Conversation History_
User: We should combine them together so that we know who should get sent the survey.
Agent: Is there a specific column you want to join on?
User: Both tables have an email related column.

_Sample Data_
| company_name               | email                        | account_manager | last_contact | lead_quality_score |
|----------------------------|------------------------------|-----------------|--------------|--------------------|
| Datanami                   | N/A                          | Michael         | N/A          | 0                  |
| N/A                        | N/A                          | Emily           | 2024-01-30   | 73                 |
| N/A                        | <EMAIL> | Mike            | 2024-02-01   | 0                  |
| Allurance Federal Services | ask later                    | Michael         | 2024-01-15   | 42                 |
| Datanomi                   | <EMAIL>          | Michael         | 2024-02-15   | 94                 |
| McKinsey & Company         | <EMAIL>        | Emily           | 2023-06-30   | 65                 |
| Allurance                  | <EMAIL>            | Michael         | N/A          | 68                 |

Table 2: Constant_Contact
| email_address              |
|----------------------------|
| <EMAIL>    |
| <EMAIL>     |
| <EMAIL>         |
| <EMAIL>           |
| <EMAIL>        |
| <EMAIL>       |
| <EMAIL>          |
| <EMAIL>  |

_Output_
```json
{{
  "thought": "Both tables can be joined using the email related column, which represent individual people.",
  "type": "PER"
}}
```

_Conversation History_
User: Is there any correlation between the customer engagement and their subscription tier?
Agent: To get started, can you tell me how you would like to measure engagement?
User: Actually, let's do this, just join the customer interaction and account tables together.

_Sample Data_
Table 1: customerInteractions
| user_id    | action_type      | endpoint             | duration_s |
|------------|------------------|----------------------|------------|
| *********  | api_call         | /v2/process_data     | 2.34       |
| *********  | dashboard_view   | /dashboard/metrics   | 45.67      |
| *********  | api_call         | /v2/batch_analyze    | 1.89       |
| *********  | report_export    | /reports/custom      | 12.45      |
| *********  | api_call         | /v2/transform        | 3.21       |
| *********  | dashboard_view   | /dashboard/alerts    | 28.90      |
| *********  | report_export    | /reports/scheduled   | 8.76       |
| *********  | api_call         | /v2/validate         | 1.56       |

Table 2: consumerAccounts
| interaction_id | subscription_tier |
|----------------|-------------------|
| *********      | Pro               |
| *********      | Pro               |
| *********      | Basic             |
| *********      | N/A               |
| *********      | Pro               |
| *********      | Basic             |
| *********      | Pro               |
| *********      | Basic             |

_Output_
```json
{{
  "thought": "The interaction_id is likely a foreign key pointing to customerInteractions, rather than its own primary key. Also interaction_id and user_id have at least some shared values, so they can be used to join the tables.",
  "type": "ID"
}}
```

_Conversation History_
User: Any way to connect our Salesforce data with the ad spend?
Agent: I can help with that. How are you tracking Salesforce leads?
User: Manually. Sometimes the campaign names don't match exactly between systems, and sometimes the sales team forgets to mark leads as qualified.

_Sample Data_
Table 1: salesforce_leads
| created_date | lead_status |
|--------------|-------------|
| 2024-02-15   | SQL         |
| 2024-02-15   | Contacted   |
| 2024-02-15   | MQL         |
| 2024-02-16   | SQL         |
| 2024-02-15   | MQL         |
| 2024-02-17   | SQL         |
| 2024-02-16   | Unqualified |
| 2024-02-17   | New         |

Table 2: whitepaper_campaigns
| clicks | signups | event_date |
|--------|---------|------------|
| 2367   | 456     | 02/15/2024 |
| 2589   | 487     | 02/16/2024 |
| 2712   | 512     | 02/17/2024 |
| 3478   | 678     | 02/18/2024 |
| 2923   | 543     | 02/19/2024 |
| 4834   | 823     | 02/20/2024 |
| 5145   | 867     | 02/21/2024 |

_Output_
```json
{{
  "thought": "The two tables contain dates, which can be used to join them together.",
  "type": "DATE"
}}
```

_Conversation History_
User: Is there a way to see if store performance is correlated with local economic indicators?
Agent: What do you mean by local economic indicators?
User: Basically connect the sales data with things like unemployment rate or GDP

_Sample Data_
Table 1: quarterly_store_metrics_2024q1
| store_revenue_usd | location_city       | daily_foot_traffic |
|-------------------|---------------------|--------------------|
| 892,450           | Seattle             | 845                |
| 1,243,890         | Plano, TX           | 1,167              |
| 674,320           | Magnolia (seattle)  | 723                |
| 1,892,670         | Irvine              | 1,588              |
| 945,230           | LA                  | 892                |
| 1,123,450         | Dallas              | 1,245              |
| 789,340           | Boise               | 867                |
| 1,567,840         | scottsdale          | 1,434              |

Table 2: economic_indicators
| metro_city    | unemployment_pct | median_household_income |
|---------------|------------------|-------------------------|
| Bellevue      | 3.2              | 128,450                 |
| Los Angeles   | 4.2              | 98,450                  |
| Plano         | 4.1              | 89,670                  |
| Irvine        | 4.5              | 112,780                 |
| Austin        | 3.7              | 95,670                  |
| Boise         | 4.3              | 78,340                  |
| Scottsdale    | 3.9              | 106,780                 |
| Raleigh       | 3.8              | 92,340                  |

_Output_
```json
{{
  "thought": "We can join the two tables together based on the location of the stores.",
  "type": "LOC"
}}
```

_Conversation History_
User: The finance folks give us all this expense data, and we need to align it with our ad spend data.
Agent: It sound like you want to join the quarterly ad performance data with the marketing expenses, right?
User: Yea, please connect them based on the channel name and the vendor name

_Sample Data_
Table 1: q3_ad_performance
| Channel Name         | Impressions  | Clicks | Conversions |
|----------------------|--------------|--------|-------------|
| Meta Ads             | 2,456,789    | 34,567 | 892         |
| Google Search        | 1,893,456    | 42,789 | 1,234       |
| LinkedIn Advertising | 456,789      | 5,678  | 234         |
| TikTok Ads           | 4,567,890    | 67,890 | 789         |
| Promoted Pins        | 678,234      | 8,901  | 123         |

Table 2: marketing_expenses
| vendor_name                    | expense_amount |
|--------------------------------|----------------|
| FB/IG                          |  72,892.45     |
| Google ads/youtube             | 128,456.78     |
| Marketo License Q3             |  24,500.00     |
| LinkedIN                       |  12,345.67     |
| Tiktok                         |  45,678.90     |
| Canva Pro Team Annual          |   2,389.00     |
| SEMrush Enterprise             |   8,935.99     |
| Agency Retainer - CreativeMind |  45,004.00     |

_Output_
```json
{{
  "thought": "The channel names and vendor names both represent organizations.",
  "type": "ORG"
}}
```

_Conversation History_
User: yea got it. Now swap the order of the discount rate and the applicable product columns
Agent: No problem, I have swapped the columns.
User: now, can we combine these products with the conversions data?

_Sample Data_
Table 1: revenue_by_product
| product_id     | list_price | discount_rate |
|----------------|------------|---------------|
| CMPY-2024-387  | $129.99    | 15%           |
| CMPY-2024-452  | $79.99     | 20%           |
| CMPY-2024-511  | $299.99    | N/A           |
| CMPY-2024-388  | $49.99     | 25%           |
| CMPY-2024-391  | $39.99     | 10%           |
| CMPY-2024-498  | $89.99     | N/A           |
| CMPY-2024-523  | $199.99    | 15%           |
| CMPY-2024-392  | $39.99     | 20%           |

Table 2: invoice_rollup
| invoice_id      | quantity | total_amount |
|-----------------|----------|--------------|
| REF_387         | 2        | 220.98       |
| PID_452         | 1        | 63.99        |
| INV_511         | 3        | 899.97       |
| PQ1_388         | 5        | 187.46       |
| REF_467         | 2        | 287.98       |
| SPRING_498      | 4        | 359.96       |
| PROMO_523       | 1        | 169.99       |
| PID_392         | 3        | 95.97        |

_Output_
```json
{{
  "thought": "While both tables contain ID columns, the format of the columns aren't aligned so they cannot be used to join the tables. Unfortunately, none of the displayed columns are good matches either.",
  "type": "O"
}}
```

_Conversation History_
User: Please join the promo table with the survey monkey data.
Agent: There are a couple of options to join the tables together, should I use the StartDate and the send_date columns?
User: Apply the email_address columns from the promotions_results table and apply the ContactEmail column from the surveyMonkey table.

_Sample Data_
Table 1: promotions_results
| StartDate  | EndDate    | RedeemedBy | EmailAddress                    |
|------------|------------|------------|---------------------------------|
| 2024-01-15 | 2024-02-15 | 2024-01-23 | <EMAIL>           |
| 2024-01-15 | 2024-02-15 | 2024-02-02 | <EMAIL>          |
| 2024-02-01 | 2024-03-01 | 2024-02-14 | <EMAIL>    |
| 2024-02-01 | 2024-03-01 | 2024-02-08 | <EMAIL>     |
| 2024-02-15 | 2024-03-15 | 2024-02-28 | <EMAIL>           |
| 2024-02-15 | 2024-03-15 | null       | <EMAIL>    |
| 2024-03-01 | 2024-04-01 | 2024-03-05 | <EMAIL>           |
| 2024-03-01 | 2024-04-01 | null       | <EMAIL>       |

Table 2: surveyMonkey
| contact_email                 | send_date    | score |
|-------------------------------|---------------|-------|
| <EMAIL>        | 01/25/2024    | 4     |
| <EMAIL>      | 01/28/2024    | 3     |
| <EMAIL>         | 02/12/2024    | 5     |
| <EMAIL>          | 02/15/2024    | 4     |
| <EMAIL>       | 02/22/2024    | 2     |
| <EMAIL>        | 03/01/2024    | 5     |
| <EMAIL>         | 03/05/2024    | 4     |
| <EMAIL>  | 03/12/2024    | 3     |

_Output_
```json
{{
  "thought": "While the topic is about emails, the user is only looking for exact matches based on using the 'apply' template. This means this is 'ID' rather than 'PER'.",
  "type": "ID"
}}
```
#############
Now it's your turn! Please decide on how the user is trying to join the two tables together based on the conversation history and the source columns from each table.
Use the 'thought' field to think through your rationale. Do *not* include any text or explanations before or after the JSON output.
The user's utterance takes precedence over the sampled data, so rely on that whenever possible to decide the entity type.

_Conversation History_
{history}

_Sample Data_
Table 1: {table1}

Table 2: {table2}

_Output_
"""

prepare_join_by_date_prompt = """Based on the columns from two tables, we are trying to join the tables together based on date or time.
Given the conversation history and supporting details, please decide on the approach to prepare the data for joining the tables together.
If a clear foreign key relationship existed, we would obviously use this to connect the tables together. However, we are faced with the scenario where this is not the case.
As such, we must first transform the data in one (or both) tables to make the join easier. Specifically, the sixteen (16) valid methods of data preparation are:
  * casing - convert all characters to lowercase, occasionally used to convert to upper case or title case as well
  * remove - strip out some portion of the content, such as taking out stop words, punctuation, or other symbols
  * mapping - replace some of the content, often to standardize the spelling of some terms (eg. Mon, Tues, Wed -> Monday, Tuesday, Wednesday)
  * replace - change exactly one symbol or character with another; if there are multiple replacements, then use a 'mapping' instead
  * tokenize - divide the content into multiple tokens, such as splitting a sentence into words or splitting a time into hours and minutes
  * extract - find a subset of the content, such as extracting the year from a date or the domain from an email address
  * format - standardize the data within the column to conform to a specific format, such as MM/DD/YYYY or 24-hour time
  * stage - create a staging table that groups the data by a standard time frame and aggregates the remaining columns
  * validate - ensure all content belongs to a predefined set of values (e.g. only days of the week), great for dealing with categorical data
  * dedupe - remove duplicate entries, such as trimming down to unique users, accounts, or campaigns
  * merge - combine the data from multiple columns into a single column, such as merging day, month, and year into a single date
  * prune - filter out extraneous rows from a source table, commonly capturing rows that are empty or contain invalid data
  * embed - transform the data into a vector representation, such as converting text into word embeddings or dates into numerical values
  * measure - calculate a distance metric between two columns, often applied after embedding or tokenizing (e.g. cosine similarity, Jaccard index)
  * cross - using a third column to cross-reference the match, providing an additional layer of context beyond the datetime component  
  * other - any advanced form of data transformation that is not listed above, such as fuzzy matching or complex interpolation
  
When crafting your response, please keep the following in mind:
  * Choose the minimum number of steps required for aligning the columns to avoid over-complicating the process
  * Prefer to apply simpler functions (e.g. replace) over more complex ones (e.g. validate)
  * Use the data preview to determine what is necessary, and avoid pre-emptively adding steps that are not needed
  * The `strip()` function is already applied at the start and end of the data preparation process, so you don't need to include it
  * Keep the order of the steps in mind, as some transformations may depend on others being completed first

Please start by examining the sampled data from each source table, and think carefully about how they may be combined to form a target table.
The source tables are '{table1}' and '{table2}', which are referred to as 'left_df' and 'right_df' respectively.
Your entire response should be in well-formatted JSON including your thought (string) and methods (list), with no further explanations after the JSON output.
Each step in the preparation list should be a dict containing keys for name (token), table (left, right or both), and description (string).
The method names are chosen from the 16 valid forms of data preparation, while the description is a natural language explanation providing more details about the transformation.

For example,
#############
_Conversation History_
User: Can I join the email campaign data with the support ticket data?
Agent: I'm not seeing any shared ids, how did you want to join these tables together?
User: By date

_Source Samples_
Left table: EmailCampaigns
| open_rate | click_rate | campaign_theme                    | campaign_month |
|-----------|------------|-----------------------------------|----------------|
| 22.4      | 3.8        | New Year Product Launch           | 2024-01        |
| 19.7      | 2.9        | Valentine's Day Flash Sale        | 2024-02        |
| 21.2      | 3.2        | Spring Collection Preview         | 2024-03        |
| 18.9      | 2.7        | Winter Clearance Final Days       | 2024-01        |
| 20.8      | 3.4        | Loyalty Member Exclusive Access   | 2024-02        |
| 23.1      | 4.1        | March Madness Special             | 2024-03        |
| 20.3      | 3.6        | Spring Break Early Bird           | 2024-03        |

Right table: SupportTicketsDetail
| ticket_count | category          | ticket_date |
|--------------|-------------------|-------------|
| 342          | Account Access    | 2024-01-15  |
| 289          | Payment Issues    | 2024-02-08  |
| 456          | Product Inquiry   | 2024-03-22  |
| 378          | Returns           | 2024-01-29  |
| 412          | Shipping Status   | 2024-02-14  |
| 267          | Account Access    | 2024-03-07  |
| 521          | Payment Issues    | 2024-02-28  |
| 398          | Product Inquiry   | 2024-01-08  |

_Output_
```json
{{
  "thought": "The ticket date can be aligned with the campaign month by simply extracting the month and year.",
  "methods": [
    {{ "name": "extract", "table": "right", "description": "Convert to a datetime object to easily extract the month and year from the ticket date" }}
  ]
}}
```

_Conversation History_
User: Any way to connect our Salesforce data with the ad spend?
Agent: I can help with that. How are you tracking Salesforce leads?
User: Manually. Sometimes the campaign names don't match exactly between systems, and sometimes the sales team forgets to mark leads as qualified.

_Source Samples_
Left table: salesforce_leads
| created_date | lead_status |
|--------------|-------------|
| 2024-02-15   | SQL         |
| 2024-02-15   | Contacted   |
| 2024-02-15   | MQL         |
| 2024-02-16   | SQL         |
| 2024-02-15   | MQL         |
| 2024-02-17   | SQL         |
| 2024-02-16   | Unqualified |
| 2024-02-17   | New         |

Right table: whitepaper_campaigns
| clicks | signups | event_date |
|--------|---------|------------|
| 2367   | 456     | 02/15/2024 |
| 2589   | 487     | 02/16/2024 |
| 2712   | 512     | 02/17/2024 |
| 3478   | 678     | 02/18/2024 |
| 2923   | 543     | 02/19/2024 |
| 4834   | 823     | 02/20/2024 |
| 5145   | 867     | 02/21/2024 |

_Output_
```json
{{
  "thought": "We need to group the lead status by date and filter for just whitepaper campaigns to join with the event dates. This can be done through a staging table.",
  "methods": [
    {{ "name" : "stage", "table": "left", "description": "Group the lead status by date to join with the event dates" }}
    {{ "name" : "format", "table": "right", "description": "Convert the event dates from MM/DD/YYYY to YYYY-MM-DD" }}
    {{ "name" : "prune", "table": "left", "description": "Filter for just whitepaper campaigns to avoid over-counting leads" }}
  ]
}}
```

_Conversation History_
User: As an HR manager, I want to know how long after hiring employees typically complete their mandatory training. Can you help me with that?
Agent: Sure, I can pull the training completion data from the LMS. How should I join the data?
User: Just group things based on month they got hired.

_Source Samples_
Left table: training_completions
| month     | completed_training |
|-----------|--------------------|
| January   | 45                 |
| February  | 38                 |
| March     | 52                 |
| April     | 41                 |
| May       | 39                 |
| June      | 43                 |
| July      | 35                 |
| August    | 48                 |

Right table: monthly_hiring_stats
| month_num | new_hires |
|-----------|-----------|
| 1         | 50        |
| 2         | 42        |
| 3         | 55        |
| 4         | 45        |
| 5         | 48        |
| 6         | 52        |
| 7         | 40        |
| 8         | 53        |

_Output_
```json
{{
  "thought": "Convert the month names to month numbers in the training completions table to match the hiring data. I can use the 'shared' key to set the mapping.",
  "methods": [
    {{ "name": "mapping", "table": "left", "description": "Convert the month names to month numbers in the training completions table to match the hiring data" }}
  ]
}}
```

_Conversation History_
User: I want to understand the customer journey from website interactions to course enrollments, but the data is split across two systems.
Agent: I can help with that. How are you tracking website interactions and course enrollments?
User: We use Google Analytics for website interactions and a custom system for course enrollments.
Agent: Got it. We can potentially match the data based on timestamps where the interaction and purchase timestamps are close enough, perhaps within a 1-hour window.
User: Actually, maybe 30 minutes.

Source Samples_
Left table: visitor_interactions
| Interaction Time       | Page Category    | Interaction Type |
|------------------------|------------------|------------------|
| 2024-01-15 09:23:45    | Data Science     | Course Preview   |
| 2024-01-15 09:25:12    | Data Science     | Pricing View     |
| 2024-01-15 09:45:30    | Full Stack Dev   | Syllabus View    |
| 2024-01-15 10:15:22    | Data Science     | Course Preview   |
| 2024-01-15 10:17:45    | Full Stack Dev   | Pricing View     |
| 2024-01-15 10:30:15    | UX Design        | Course Preview   |
| 2024-01-15 10:45:30    | Full Stack Dev   | Syllabus View    |
| 2024-01-15 11:05:18    | UX Design        | Pricing View     |

Right table: course_purchases
| purchase_timestamp     | course_topic     | purchase_amt |
|------------------------|------------------|--------------|
| 2024-01-15 09:40:12    | data-science     | 199.99       |
| 2024-01-15 09:42:30    | data-science     | 199.99       |
| 2024-01-15 10:10:45    | web-development  | 149.99       |
| 2024-01-15 10:35:18    | data-science     | 199.99       |
| 2024-01-15 10:40:22    | web-development  | 149.99       |
| 2024-01-15 10:55:30    | web-design       | 179.99       |
| 2024-01-15 11:15:45    | web-development  | 149.99       |
| 2024-01-15 11:25:20    | web-design       | 179.99       |

_Output_
```json
{{
  "thought": "The interaction and purchase timestamps don't seem to line up directly. Given the conversation, we are in a scenario where a fuzzy match is appropriate.",
  "methods": [
    {{ "name": "other", "table": "both", "description": "Use Interaction Time to find the closest purchase_timestamp within a 30-minute window" }}
    {{ "name": "remove", "table": "right", "description": "Remove the dashes from the course_topic" }}
    {{ "name": "casing", "table": "right", "description": "Capitalize the first letter of each word in the course_topic" }}
    {{ "name": "replace", "table": "left", "description": "Change Dev to Development" }}
    {{ "name": "cross", "table": "both", "description": "Compare the Page Category to the course_topic as a supporting factor" }}
  ]
}}
```

_Conversation History_
User: I want to analyze how our daily inventory snapshots relate to actual sales transactions.
Agent: It sounds like you want to perform a Sales and Inventory Correlation analysis. Is that right?
User: Yes, that's right.
Agent: In order to perform this analysis, we need to join the sales_transactions and daily_inventory_snapshots tables.
User: Ok, how should we join them?

_Source Samples_
Left table: sales_transactions
| transaction_timestamp   | product_category | units_sold |
|-------------------------|------------------|------------|
| 2024-01-15 09:23:45     | Electronics      | 3          |
| 2024-01-15 14:17:32     | Electronics      | 2          |
| 2024-01-15 16:45:12     | Clothing         | 5          |
| 2024-01-16 10:30:00     | Electronics      | 1          |
| 2024-01-16 11:20:15     | Clothing         | 4          |
| 2024-01-16 15:45:22     | Electronics      | 2          |
| 2024-01-17 08:15:30     | Clothing         | 3          |
| 2024-01-17 13:40:18     | Electronics      | 4          |

Right table: daily_inventory_snapshots
| snapshotdate | product_category | units_in_stock |
|--------------|------------------|----------------|
| 2024-01-15   | Electronics      | 145            |
| 2024-01-15   | Clothing         | 232            |
| 2024-01-16   | Electronics      | 140            |
| 2024-01-16   | Clothing         | 223            |
| 2024-01-17   | Electronics      | 134            |
| 2024-01-17   | Clothing         | 216            |
| 2024-01-18   | Electronics      | 130            |
| 2024-01-18   | Clothing         | 213            |

_Output_
```json
{{
  "thought": "The transaction timestamps can be aligned with the inventory snapshots by limiting to just the date portion.",
  "methods": [
    {{ "name": "extract", "table": "left", "description": "Keep just the date portion from the transaction timestamp" }}
  ]
}}
```

_Conversation History_
User: Can you take a look at how website performance issues are related to customer support tickets?
Agent: Sure, I can help with that. I noticed that the performance logs and support tickets are recorded in different time zones.
User: That's right, the performance logs are in UTC, while the support tickets are in Asia somewhere, they need to be aligned.

_Source Samples_
Left table: performance_logs
| incident_timestamp    | service_area | response_time_ms |
|-----------------------|--------------|------------------|
| 2024-01-15 02:30:00   | API          | 850              |
| 2024-01-15 03:15:00   | Database     | 1200             |
| 2024-01-15 04:45:00   | Frontend     | 2500             |
| 2024-01-15 06:20:00   | API          | 950              |
| 2024-01-15 07:10:00   | Database     | 900              |
| 2024-01-15 08:45:00   | Frontend     | 1800             |
| 2024-01-15 10:30:00   | API          | 750              |
| 2024-01-15 11:15:00   | Database     | 850              |

Right table: support_tickets
| ticket_ts                  | ticket_count |
|----------------------------|--------------|
| 2024-01-15 10:30:00+08:00  | 5            |
| 2024-01-15 00:15:00-03:00  | 3            |
| 2024-01-15 14:45:00+10:00  | 8            |
| 2024-01-15 01:20:00-05:00  | 4            |
| 2024-01-15 16:10:00+09:00  | 2            |
| 2024-01-15 03:45:00-05:00  | 6            |
| 2024-01-15 19:30:00+09:00  | 3            |
| 2024-01-15 02:15:00-09:00  | 4            |

_Output_
```json
{{
  "thought": "Converting the ticket timestamps to UTC allows us to compare them directly with the incident timestamps. This can be done easily with `pd.to_datetime` and `tz_convert('UTC')`",
  "methods": [
    {{ "name": "extract", "table": "right", "description": "Convert the ticket timestamps into pandas datetime objects" }},
    {{ "name": "format", "table": "right", "description": "Reformat timestamps into UTC without timezone, which now align with incident times" }}
  ]
}}
```
#############
Now it's your turn! Please decide on the optimal methods to prepare the datetime columns for joining. For additional context, the full set of columns are:
{table1}: {columns1}
{table2}: {columns2}

_Conversation History_
{history}

_Source Samples_
Left table: {table1}
{samples1}

Right table: {table2}
{samples2}

_Output_
"""

prepare_join_by_loc_prompt = """Based on the columns from two tables, we are trying to join the tables together based on location or geography.
Given the conversation history and supporting details, please decide on the approach to prepare the data for joining the tables together.
If a clear foreign key relationship existed, we would obviously use this to connect the tables together. However, we are faced with the scenario where this is not the case.
As such, we must first transform the data in one (or both) tables to make the join easier. Specifically, the sixteen (16) valid forms of data preparation are:
  * casing - convert all characters to lowercase, occasionally used to convert to upper case or title case as well
  * remove - strip out some portion of the content, such as taking out stop words, punctuation, or other symbols
  * mapping - replace some of the content, often to standardize the spelling of some terms (eg. LA, SF, NY -> Los Angeles, San Francisco, New York)
  * replace - change exactly one symbol or character with another; if there are multiple replacements, then use a 'mapping' instead
  * tokenize - divide the content into multiple tokens, such as splitting a sentence into words or splitting an address into city, state, and zip code
  * extract - find a subset of the content, such as extracting the city from an address or the domain from an email address
  * format - standardize the data within the column to conform to a specific format, such as 'city, state' or 'county - region'
  * stage - create a staging table that groups the data by a standard location and aggregates the remaining columns
  * validate - ensure all content belongs to a predefined set of values (e.g. only cities in Florida), great for dealing with categorical data
  * dedupe - remove duplicate entries, such as trimming down to unique users, accounts, or campaigns
  * merge - combine the data from multiple columns into a single column, such as merging city, state, and country into a single address
  * prune - filter out extraneous rows from a source table, commonly capturing rows that are empty or contain invalid data
  * embed - transform the data into a vector representation, such as converting text into word embeddings or dates into numerical values
  * measure - calculate a distance metric between two columns, often applied after embedding or tokenizing (e.g. cosine similarity, Jaccard index)
  * cross - using a third column to cross-reference the match, providing an additional layer of context beyond the location component  
  * other - any advanced form of data transformation that is not listed above, such as fuzzy matching or complex interpolation
  
When crafting your response, please keep the following in mind:
  * Choose the minimum number of steps required for aligning the columns to avoid over-complicating the process
  * Prefer to apply simpler functions (e.g. replace) over more complex ones (e.g. validate)
  * Use the data preview to determine what is necessary, and avoid pre-emptively adding steps that are not needed
  * The `strip()` function is already applied at the start and end of the data preparation process, so you don't need to include it
  * Keep the order of the steps in mind, as some transformations may depend on others being completed first

Please start by examining the sampled data from each source table, and think carefully about how they may be combined to form a target table.
The source tables are '{table1}' and '{table2}', which are referred to as 'left_df' and 'right_df' respectively.
Your entire response should be in well-formatted JSON including your thought (string) and preparation (list), with no further explanations after the JSON output.
Each step in the preparation list should be a dict containing keys for name (token), table (left, right or both), and description (string).
The method names are chosen from the 16 valid forms of data preparation, while the description is a natural language explanation providing more details about the transformation.

For example,
#############
_Conversation History_
User: Is there a way to see if store performance is correlated with local economic indicators?
Agent: What do you mean by local economic indicators?
User: Basically connect the sales data with things like unemployment rate or GDP

_Source Samples_
Left table: quarterly_store_metrics_2024q1
| store_revenue_usd | location (city)  | location (state) | daily_foot_traffic |
|-------------------|------------------|------------------|--------------------|
| 892,450           | Seattle          | WA               | 845                |
| 1,243,890         | Plano            | TX               | 1,167              |
| 674,320           | Bellvue          | WA               | 723                |
| 1,892,670         | Irvine           | CA               | 1,588              |
| 945,230           | LA               | CA               | 892                |
| 1,123,450         | Dallas           | TX               | 1,245              |
| 789,340           | Boise            | ID               | 867                |
| 1,567,840         | Scottsdale       | AZ               | 1,434              |

Right table: economic_indicators
| metro_city      | unemployment_pct | median_household_income |
|-----------------|------------------|-------------------------|
| Bellevue, WA    | 3.2              | 128,450                 |
| Los Angeles, CA | 4.2              | 98,450                  |
| Plano, TX       | 4.1              | 89,670                  |
| San Diego, CA   | 4.3              | 78,340                  |
| Austin, TX      | 3.7              | 95,670                  |
| Tempe, AZ       | 4.3              | 78,340                  |
| Scottsdale, AZ  | 3.9              | 106,780                 |
| Raleigh, NC     | 3.8              | 92,340                  |

_Output_
```json
{{
  "thought": "Merging the location columns with a comma can create a new column that is better aligned with the metro_city column.",
  "methods": [
    {{ "name": "merge", "table": "left", "description": "Merge the location columns with a comma" }}
  ]
}}
```

_Conversation History_
User: Are there any patterns in customer satisfaction ratings based on the distance from the restaurant?
Agent: I can look into that. What kind of patterns are you looking for?
User: I'm looking for any patterns that might indicate if delivery satisfaction is correlated with distance from the restaurant.
Agent: Ok, I'll need to join the two tables together based on the addresses.
User: Ok, let's do that.

_Source Samples_
Left table: Customer Feedback
| deliveryDate | streetAddress         | satisfactionScore | deliveryTimeMins  |
|--------------|-----------------------|-------------------|-------------------|
| 2024-06-18   | 1232 oak street       | 4.5               | 25                |
| 2024-06-15   | 147 Jefferson Blvd    | 4.6               | 25                |
| 2024-06-16   | 789 Washington Blvd   | 3.5               | 45                |
| 2024-06-17   | 654 Market St         | 3.8               | 40                |
| 2024-06-15   | 456 Maple Ave         | 4.0               | 35                |
| 2024-06-17   | 987 Broadway Ave      | 4.2               | 30                |
| 2024-06-16   | 321 Pine Ridge Rd     | 4.8               | 20                |
| 2024-06-18   | 8811 Randolph Rd      | 3.9               | 35                |

Right table: restaurant_locations
| street_address          | cuisine_type |
|-------------------------|--------------|
| 1232 Oak Street         | American     |
| 456 Maple Avenue        | Chinese      |
| 789 Washington Blvd     | American     |
| 258 Lincoln Rd          | Mexican      |
| 321 Pine Ridge Road     | Italian      |
| 654 Market Street       | Japanese     |
| 987 broadway avenue     | American     |
| 147 Jefferson Boulevard | Thai         |

_Output_
```json
{{
  "thought": "Converting the street addresses from abbreviations to full names will make it easier to match. The capitalization is also inconsistent, so we should standardize that as well.",
  "methods": [
    {{ "name": "casing", "table": "both", "description": "Change all street names to lowercase" }}
    {{ "name": "mapping", "table": "both", "description": "Convert abbreviations such as St, Ave, Blvd, Rd to full names like Street, Avenue, Boulevard, and Road" }}
  ]
}}
```

_Conversation History_
User: I am helping a client merge their legacy system data with their new cloud-based sales database to avoid duplicate accounts.
Agent: That sounds like a complex task. How are the addresses formatted in the legacy system?
User: The addresses were entered inconsistently over the years in the legacy system, while the new system has attempted standardization.

_Source Samples_
Left table: legacy_customer_data
| customer_address                    | last_contact_date | account_status |
|-------------------------------------|-------------------|----------------|
| Suite 401, 555 California St. 94104 | 2023-12-15        | Active         |
| 185 BERRY STREET, APT 2B            | 2023-12-20        | Active         |
| 1 Market St Suite 300               | 2023-12-22        | Active         |
| <N/A>                               | 2023-12-25        | Inactive       |
| 140 New Montgomery St, Room 200     | 2023-12-28        | Active         |
| 345 Spear St #1000, San Francisco   | 2024-01-02        | Active         |
| 301 Howard Street                   | 2024-01-05        | Active         |
| 475 Brannan Street, Suite 505 94107 | 2024-01-08        | Active         |

Right table: new_system_accounts
| address                          | subscription_tier | last_renewal_date | current_mrr |
|----------------------------------|-------------------|-------------------|-------------|
| 525 University Avenue Suite 200  | Professional      | 2024-01-12        | 2200.00     |
| 101 California Street #3000      | Enterprise        | 2024-01-15        | 2000.00     |
| 140 New Montgomery Suite 200     | Enterprise        | 2024-01-12        | 2200.00     |
| 535 Mission St Suite 100         | Professional      | 2024-01-05        | 1500.00     |
| 301 Howard St                    | Standard          | 2024-01-01        | 800.00      |
| 555 California Street #401       | Enterprise        | 2024-01-10        | 2500.00     |
| 185 Berry St Suite 2B            | Professional      | 2024-01-08        | 1200.00     |
| 1 Market Street Suite 300        | Enterprise        | 2024-01-07        | 2800.00     |

_Output_
```json
{{
  "thought": "These all seem to be places in San Francisco, but the messy formatting means multiple steps are needed to align them. Thus, we are dealing with a fuzzy match scenario.",
  "methods": [
    {{ "name": "extract", "table": "both", "description": "Extract suite numbers which may be located at the start or end of the address" }},
    {{ "name": "remove", "table": "both", "description": "Drop the null address from both tables" }},
    {{ "name": "remove", "table": "left", "description": "Remove zip codes from the legacy_customer_data" }},
    {{ "name": "casing", "table": "both", "description": "Fix the capitalization of street names to be consistent" }},
    {{ "name": "mapping", "table": "both", "description": "Convert all street names to their full form, such as change 'St' to 'Street'" }},
    {{ "name": "measure", "table": "both", "description": "Consider matching based on geographic proximity" }}
  ]
}}
```

_Conversation History_
User: Are maintenance requests related to utility usage?
Agent: That's a good question. I'll need to join the two tables together based on the apartment numbers.
User: Ok, great. let's do that.

_Source Samples_
Left table: maintenance_requests
| apartment      | request_date | maintenance_type   |
|----------------|--------------|--------------------|
| APT 5A         | 2024-01-17   | Appliance Repair   |
| Apt. 4B        | 2024-01-15   | Plumbing           |
| Unit #2A       | 2024-01-16   | Electrical         |
| Apartment 3C   | 2024-01-16   | HVAC               |
| Unit 1B        | 2024-01-17   | Plumbing           |
| Apt #4C        | 2024-01-18   | Electrical         |
| Apartment 2B   | 2024-01-18   | HVAC               |
| APT. 3A        | 2024-01-19   | Appliance Repair   |

Right table: utility_usage
| apt_number | utility_type |
|------------|--------------|
| 4B         | Water        |
| 3C         | Electric     |
| 3A         | Water        |
| 5A         | Water        |
| 1B         | Water        |
| 4C         | Electric     |
| 2A         | Water        |
| 2B         | Electric     |

_Output_
```json
{{
  "thought": "I can align the apartment numbers by focusing on just the numeric portion at the end. No need to overthink it with regex.",
  "methods": [
    {{ "name": "remove", "table": "left", "description": "Take out the hash symbol or periods from the apartment column" }},
    {{ "name": "tokenize", "table": "left", "description": "Split the apartment column into multiple tokens" }},
    {{ "name": "extract", "table": "left", "description": "Get the apartment number by taking the last part" }}
  ]
}}
```

_Conversation History_
User: I need to know if our staffing levels are appropriate for our customer service volume.
Agent: I can help with that. How are you tracking customer service volume and staffing levels?
User: We have a detailed customer service log where each inquiry is a separate row, and we want to compare this against our staffing data which has one row per store.
Agent: Ok, I'll need to aggregate the customer service data by store before I can properly compare it with staffing levels.
User: That sounds right.

_Source Samples_
Left table: store_staffing
| store_location    | servicedesk_staff | peak_hours_staff |
|-------------------|-------------------|------------------|
| Seattle Downtown  | 4                 | 6                |
| Bellevue Square   | 3                 | 5                |
| Tacoma Mall       | 3                 | 4                |

Right table: customer_service_inquiries
| inquiry_date | store_location     | inquiry_type    |
|--------------|--------------------|-----------------|
| 2024-01-15   | Seattle Downtown   | Returns         |
| 2024-01-15   | Seattle Downtown   | Product Info    |
| 2024-01-15   | Seattle Downtown   | Returns         |
| 2024-01-15   | Bellevue Square    | Product Info    |
| 2024-01-16   | Tacoma Mall        | Complaints      |
| 2024-01-16   | Seattle Downtown   | Product Info    |
| 2024-01-16   | Bellevue Square    | Returns         |
| 2024-01-16   | Tacoma Mall        | Returns         |

_Output_
```json
{{
  "thought": "We should group the customer service inquiries to aggregate the stats per store location in a staging table.",
  "methods": [
    {{ "name": "stage", "table": "right", "description": "Group the customer service inquiries by store location, counting the number of inquiries" }}
  ]
}}
```

_Conversation History_
User: I have a sense that the local property tax rates affect our portfolio performance.
Agent: Interesting idea, how do you want to verify this?
User: We can start by joining our property data with tax rate data based on the county.

_Source Samples_
Left table: property_portfolio
| property_id | address                                       | monthly_rev | square_footage |
|-------------|-----------------------------------------------|-------------|----------------|
| RDU556      | 5001 Davis Dr, Morrisville, NC 27560          | 67,890      | 32,400         |
| CLT332      | 301 S College St, Charlotte, NC 28202         | 89,450      | 41,200         |
| GSO778      | 701 Green Valley Rd, Greensboro, NC 27408     | 45,780      | 22,300         |
| RDU445      | 2445 Meridian Pkwy, Durham, NC 27713          | 56,780      | 28,900         |
| CLT667      | 13860 Ballantyne Corp Pl, Charlotte, NC 28277 | 78,900      | 35,600         |
| AVL223      | 1 Town Square Blvd, Asheville, NC 28803       | 34,560      | 18,400         |
| RDU889      | 4721 Emperor Blvd, Durham, NC 27703           | 45,670      | 24,500         |
| WLM445      | 1908 Eastwood Rd, Wilmington, NC 28403        | 43,210      | 21,800         |

Right table: county_tax_rates
| county_name | state | property_tax_rate |
|-------------|-------|-------------------|
| Wake        | NC    | 0.0256            |
| Mecklenburg | NC    | 0.0248            |
| Guilford    | NC    | 0.0234            |
| Durham      | NC    | 0.0262            |
| Fulton      | GA    | 0.0394            |
| Buncombe    | NC    | 0.0229            |
| New Hanover | NC    | 0.0237            |
| Fairfax     | VA    | 0.0341            |

_Output_
```json
{{
  "thought": "Aligning the property portfolio with the county tax rates requires a multi-step process that connects the zip code to the county.",
  "methods": [
    {{ "name": "extract", "table": "left", "description": "Extract the zip code from the address" }},
    {{ "name": "stage", "table": "left", "description": "Group the property portfolio by zip code" }},
    {{ "name": "other", "table": "left", "description": "Transform those zip codes to a county in another new column" }},
    {{ "name": "extract", "table": "right", "description": "Lookup the tax rate from the county_tax_rates to join into the new staging table" }},
    {{ "name": "cross", "table": "right", "description": "Check the state column against the property address as a supporting factor" }}
  ]
}}
```
#############
Now it's your turn! Please decide on the best method to simplify the process of joining the two source tables together. For additional context, the full set of columns are:
{table1}: {columns1}
{table2}: {columns2}

_Conversation History_
{history}

_Source Samples_
Left table: {table1}
{samples1}

Right table: {table2}
{samples2}

_Output_
"""

prepare_join_by_org_prompt = """Based on the columns from two tables, we are trying to join the tables together based on organization or business.
Given the conversation history and supporting details, please decide on the approach to prepare the data for joining the tables together.
If a clear foreign key relationship existed, we would obviously use this to connect the tables together. However, we are faced with the scenario where this is not the case.
As such, we must first transform the data in one (or both) tables to make the join easier. Specifically, the sixteen (16) valid forms of data preparation are:
  * casing - convert all characters to lowercase, occasionally used to convert to upper case or title case as well
  * remove - strip out some portion of the content, such as taking out stop words, punctuation, or other symbols
  * mapping - replace some of the content, often to standardize the spelling of some terms (eg. FB, X, LI -> Facebook, Twitter, LinkedIn)
  * replace - change exactly one symbol or character with another; if there are multiple replacements, then use a 'mapping' instead
  * tokenize - divide the content into multiple tokens, such as splitting a sentence into words or splitting a company name into subwords
  * extract - find a subset of the content, such as extracting the last token from a company name or the domain from an email address
  * format - standardize the data within the column to conform to a specific format, such as 'department - division' or 'company (region)'
  * stage - create a staging table that groups the data by a standard organization and aggregates the remaining columns
  * validate - ensure all content belongs to a predefined set of values (e.g. only ticker symbols), great for dealing with categorical data
  * dedupe - remove duplicate entries, such as trimming down to unique businesses, accounts, or campaigns
  * merge - combine the data from multiple columns into a single column, such as concatenating a department and division into a full org name
  * prune - filter out extraneous rows from a source table, commonly capturing rows that are empty or contain invalid data
  * embed - transform the data into a vector representation, such as converting text into word embeddings or dates into numerical values
  * measure - calculate a distance metric between two columns, often applied after embedding or tokenizing (e.g. cosine similarity, Jaccard index)
  * cross - using a third column to cross-reference the match, providing an additional layer of context beyond the organization component
  * other - any advanced form of data transformation that is not listed above, such as fuzzy matching or complex interpolation
  
When crafting your response, please keep the following in mind:
  * Choose the minimum number of steps required for aligning the columns to avoid over-complicating the process
  * Prefer to apply simpler functions (e.g. replace) over more complex ones (e.g. validate)
  * Use the data preview to determine what is necessary, and avoid pre-emptively adding steps that are not needed
  * The `strip()` function is already applied at the start and end of the data preparation process, so you don't need to include it
  * Keep the order of the steps in mind, as some transformations may depend on others being completed first

Please start by examining the sampled data from each source table, and think carefully about how they may be combined to form a target table.
The source tables are '{table1}' and '{table2}', which are referred to as 'left_df' and 'right_df' respectively.
Your entire response should be in well-formatted JSON including your thought (string) and preparation (list), with no further explanations after the JSON output.
Each step in the preparation list should be a dict containing keys for name (token), table (left, right or both), and description (string).
The method names are chosen from the 16 valid forms of data preparation, while the description is a natural language explanation providing more details about the transformation.

For example,
#############
_Conversation History_
User: Do our target accounts match our sales contacts?
Agent: I can help with that. However, I see that the CRM contains multiple contacts per company.
User: That's right. We need to ensure we don't double-count opportunities where we're talking to multiple people at the same company.

_Source Samples_
Left table: sales_contacts
| company_name  | contact_role       | last_meeting_date | deal_size  |
|---------------|--------------------|-------------------|------------|
| Salesforce    | VP Engineering     | 2024-01-15        | 250000     |
| Oracle        | CTO                | 2024-01-17        | 180000     |
| Adobe         | VP Product         | 2024-01-18        | 120000     |
| Adobe         | Engineering Lead   | 2024-01-15        | 120000     |
| Workday       | Engineer Manager   | 2024-01-19        | 200000     |
| Salesforce    | Eng, Director      | 2024-01-12        | 250000     |
| Salesforce    | CTO                | 2024-01-16        | 250000     |
| Oracle        | VP Engineering     | 2024-01-20        | 180000     |

Right table: target_accounts
| company_name    | target_deal_size | industry        |
|-----------------|------------------|-----------------|
| Salesforce      | 300000          | Software        |
| Oracle          | 250000          | Software        |
| Adobe           | 200000          | Software        |
| Workday         | 225000          | Software        |
| ServiceNow      | 175000          | Software        |
| VMware          | 150000          | Software        |
| Intuit          | 125000          | Software        |
| DocuSign        | 100000          | Software        |

_Output_
```json
{{
  "thought": "Company names are repeated in the sales contacts table, so we should create a staging table that groups the data by each company before joining with target accounts.",
  "methods": [
    {{ "name": "stage", "table": "left", "description": "Group the sales contacts by company name, and aggregate by the max of deal size (not the sum to avoid double-counting)" }}
  ]
}}
```

_Conversation History_
User: I want to combine vendor payment data with vendor risk assessment data from our compliance system.
Agent: Ok, I see the vendor payments table and the vendor risk assessments table. Is that right?
User: Yes, that's right. Join them based on the vendor name.

_Source Samples_
Left table: vendor_payments
| vendor_name         | payment_date| payment_amount | payment_terms |
|---------------------|-------------|----------------|---------------|
| McDonald's          | 2024-01-15  | 25,450.00      | Net 30        |
| AT&T Global         | 2024-01-16  | 18,320.00      | Net 45        |
| Sysco-Foods Ltd     | 2024-01-16  | 42,150.00      | Net 30        |
| Johnson & Johnson   | 2024-01-17  | 65,800.00      | Net 60        |
| Costco Wholesale    | 2024-01-17  | 33,240.00      | Net 30        |
| U.S. Bank, Inc.     | 2024-01-18  | 12,780.00      | Net 45        |
| Ben & Jerry's       | 2024-01-18  | 8,920.00       | Net 30        |
| Brother-Tech        | 2024-01-19  | 15,640.00      | Net 30        |

Right table: vendor_risk_assessments
| company_name       | risk_score | last_audit_date | industry_category |
|--------------------|------------|-----------------|-------------------|
| US Bank Inc        | 82         | 2023-12-15      | Financial         |
| McDonalds          | 78         | 2023-12-20      | Food/Beverage     |
| Brother Tech       | 75         | 2023-12-22      | Technology        |
| Wells Fargo & Co   | 85         | 2023-12-28      | Financial         |
| Sysco Foods        | 80         | 2024-01-02      | Distribution      |
| Ben and Jerrys     | 77         | 2024-01-05      | Food/Beverage     |
| Adobe Inc          | 79         | 2024-01-08      | Technology        |
| ATT Global         | 83         | 2024-01-10      | Telecommunications|

_Output_
```json
{{
  "thought": "Converting to lowercase, and then removing all punctuation and stop words should be sufficient to align the vendor names.",
  "methods": [
    {{ "name": "remove", "table": "both", "description": "Remove punctuation characters from both tables" }},
    {{ "name": "remove", "table": "both", "description": "Drop common stop words such as 'and', 'inc', 'ltd', and 'co'" }},
    {{ "name": "casing", "table": "both", "description": "Convert all vendor names to lowercase" }}
  ]
}}
```

_Conversation History_
User: The finance folks give us all this expense data, and we need to align it with our ad spend data.
Agent: It sound like you want to join the quarterly ad performance data with the marketing expenses, right?
User: Yea, please connect them based on the channel name and the vendor name

_Source Samples_
Left table: q3_ad_performance
| Channel Name         | Impressions  | Clicks | Conversions |
|----------------------|--------------|--------|-------------|
| Meta Ads             | 2,456,789    | 34,567 | 892         |
| Google Search        | 1,893,456    | 42,789 | 1,234       |
| LinkedIn Advertising | 456,789      | 5,678  | 234         |
| TikTok Ads           | 4,567,890    | 67,890 | 789         |
| Promoted Pins        | 678,234      | 8,901  | 123         |

Right table: marketing_expenses
| vendor_name                    | expense_amount |
|--------------------------------|----------------|
| FB/IG                          |  72,892.45     |
| Google ads/youtube             | 128,456.78     |
| Marketo License Q3             |  24,500.00     |
| LinkedIN                       |  12,345.67     |
| Tiktok                         |  45,678.90     |
| Canva Pro Team Annual          |   2,389.00     |
| SEMrush Enterprise             |   8,935.99     |
| Agency Retainer - CreativeMind |  45,004.00     |

_Output_
```json
{{
  "thought": "The channel names and vendor names are related, but have no direct conversions. We are dealing with a fuzzy match scenario.",
  "methods": [
    {{ "name": "remove", "table": "both", "description": "Remove punctuation characters such as slashes and dashes" }},
    {{ "name": "tokenize", "table": "both", "description": "Split the channel and vendor names into multiple tokens" }},
    {{ "name": "measure", "table": "both", "description": "Look for Jaccard similarity between the tokens" }},
    {{ "name": "embed", "table": "both", "description": "Convert the tokens into word embeddings" }},
    {{ "name": "measure", "table": "both", "description": "Calculate cosine similarity between the word embeddings" }}
  ]
}}
```

_Conversation History_
User: I need to match our trading records against a list of sanctioned companies, but it was scanned from a PDF using OCR, so the company names are broken up. Can you help?
Agent: Hmm, I can take a look. Are you trying to join the two tables together?
User: Yes, please join them together if you can.


_Source Samples_
Left table: TradingRecords_2024Q1
| company_name         | transaction_date | trade_value  |
|----------------------|------------------|--------------|
| Ace Global Trading   | 2024-01-15       | 1250000.00   |
| Phoenix Exports      | 2024-01-16       | 780000.00    |
| Crown Shipping Ltd   | 2024-01-16       | 1680000.00   |
| Silverstone Mining   | 2024-01-17       | 945000.00    |
| Meridian Resources   | 2024-01-17       | 1150000.00   |
| Quantum Industries   | 2024-01-18       | 1460000.00   |
| GT Intelligence      | 2024-01-18       | 998000.00    |
| Harbor Logistics     | 2024-01-19       | 1420000.00   |

Right table: SanctionedCompanies
| entity_name           | risk_level | jurisdiction |
|-----------------------|------------|--------------|
| Me ridan  Resources   | High       | International|
| Silv erst one Mining  | Medium     | Regional     |
| Quantum Industr ies   | High       | International|
| Red Dragon Trading    | Medium     | Regional     |
| Ace Glo bal Trading   | High       | International|
| Atlas  Petrole um     | Low        | Regional     |
| Crown Shipping  Ltd   | Medium     | Regional     |
| Sunburst En ergy      | High       | Regional     |

_Output_
```json
{{
  "thought": "The OCR artifacts in the sanctioned companies can be cleaned up by removing extra spaces. This breaks readability, but that's not a concern for comparison purposes. We should also lowercase to allow for exact matches.",
  "methods": [
    {{ "name": "remove", "table": "both", "description": "Remove extra spaces to clean up OCR artifacts" }},
    {{ "name": "casing", "table": "both", "description": "Convert all company names to lowercase" }}
  ]
}}
```

_Conversation History_
User: Our client wants to see how ESG ratings correlate with stock performance. This data is so messy though.
Agent: I can help with that. Can you tell me more about the data?
User: We have ESG ratings for companies by name, but our stock performance data is by ticker symbol. We have a reference table that could help.

_Source Samples_
Left table: esg_ratings
| company_name                | esg_score | environmental_score | social_score |
|-----------------------------|-----------|---------------------|--------------|
| Microsoft Corporation       | 85        | 82                  | 88           |
| Tesla Motors Inc            | 72        | 89                  | 65           |
| JPMorgan Chase & Co         | 68        | 71                  | 65           |
| Walmart Stores Inc          | 65        | 62                  | 68           |
| Alphabet Inc                | 82        | 78                  | 85           |
| ExxonMobil Corporation      | 52        | 45                  | 58           |
| United Healthcare Group     | 71        | 68                  | 74           |
| Procter & Gamble Company    | 78        | 75                  | 81           |

Right table: stock_performance
| ticker | current_price | ytd_return | market_cap   |
|--------|---------------|------------|--------------|
| MSFT   | 405.12        | 8.5        | 2.95T        |
| GOOGL  | 142.65        | 5.2        | 1.85T        |
| WMT    | 165.45        | -2.1       | 450B         |
| XOM    | 102.35        | 1.8        | 425B         |
| PG     | 158.78        | 3.2        | 380B         |
| UNH    | 515.25        | -4.5       | 475B         |
| NVDA   | 525.80        | 12.4       | 1.25T        |
| AMZN   | 155.32        | 7.8        | 1.65T        |

_Output_
```json
{{
  "thought": "We can use the reference table to create a new column in the esg_ratings table. This effectively extracts ticker symbols from the company names.",
  "methods": [
    {{ "name": "extract", "table": "left", "description": "Create a new column in the esg_ratings table by extracting ticker symbols from the company names using the reference table" }},
    {{ "name": "validate", "table": "left", "description": "Ensure all company names have corresponding ticker symbols" }}
  ]
}}
```

_Conversation History_
User: We're looking to see how our research grants lead to publications.
Agent: I see project data in one database and faculty publication data in another, but university names are written differently in each system.
User: Yes, we need to match these up to analyze joint grant opportunities.

_Source Samples_
Left table: research_projects
| institution_name     | city          | project_count | total_funding |
|----------------------|---------------|---------------|---------------|
| Caltech              | Pasadena      | 185           | 28000000      |
| Cal                  | Berkeley      | 325           | 45000000      |
| Stanford             | Palo Alto     | 290           | 42000000      |
| San Jose State       | San Jose      | 95            | 8000000       |
| UCLA                 | Los Angeles   | 280           | 38000000      |
| UCSF                 | San Francisco | 245           | 32000000      |
| UC Davis             | Davis         | 180           | 22000000      |
| U.S.C.               | Los Angeles   | 210           | 25000000      |

Right table: faculty_publications
| university                         | publication_count| citation_impact |
|------------------------------------|------------------|-----------------|
| UC Los Angeles                     | 2400             | 185.1           |
| Stanford University                | 3000             | 175.5           |
| California Institute of Technology | 1950             | 195.6           |
| University of Southern California  | 2100             | 165.3           |
| UC Berkeley                        | 2850             | 185.2           |
| UC San Francisco                   | 2300             | 188.9           |
| UC Santa Cruz                      | 950              | 142.7           |
| CSU San Jose                       | 880              | 125.4           |

_Output_
```json
{{
  "thought": "The tables have overlapping schools, but the names are spelled differently. A simple conversion does not seem apparent, so we are dealing with a fuzzy match scenario.",
  "methods": [
    {{ "name": "remove", "table": "both", "description": "Remove punctuation characters from both tables" }},
    {{ "name": "lowercase", "table": "both", "description": "Convert all institution names to lowercase" }},
    {{ "name": "tokenize", "table": "both", "description": "Break up the institution names into multiple tokens" }},
    {{ "name": "other", "table": "both", "description": "include the initials of the institution name as a token for comparison" }},
    {{ "name": "measure", "table": "both", "description": "Look for Jaccard similarity or Levenshtein distance between the tokens" }},
    {{ "name": "cross", "table": "both", "description": "Compare the city names to further validate the matches" }}
  ]
}}
```
#############
Now it's your turn! Please decide on the best method to simplify the process of joining the two source tables together. For additional context, the full set of columns are:
{table1}: {columns1}
{table2}: {columns2}

_Conversation History_
{history}

_Source Samples_
Left table: {table1}
{samples1}

Right table: {table2}
{samples2}

_Output_
"""

prepare_join_by_per_prompt = """Based on the columns from two tables, we are trying to join the tables together based on person or user.
Given the conversation history and supporting details, please decide on the approach to prepare the data for joining the tables together.
If a clear foreign key relationship existed, we would obviously use this to connect the tables together. However, we are faced with the scenario where this is not the case.
As such, we must first transform the data in one (or both) tables to make the join easier. Specifically, the sixteen (16) valid forms of data preparation are:
  * casing - convert all characters to lowercase, occasionally used to convert to upper case or title case as well
  * remove - strip out some portion of the content, such as taking out stop words, punctuation, or other symbols
  * mapping - replace some of the content, often to standardize the spelling of some terms (eg. Kate, Kathryn, Katie -> Katherine)
  * replace - change exactly one symbol or character with another; if there are multiple replacements, then use a 'mapping' instead
  * tokenize - divide the content into multiple tokens, such as splitting a sentence into words or splitting a name into first and last
  * extract - find a subset of the content, such as extracting the salutation from a name or the domain from an email address
  * format - standardize the data within the column to conform to a specific format, such as 'last name, first name' or 'full name (title)'
  * stage - create a staging table that groups the data by a standard username and aggregates the remaining columns
  * validate - ensure all content belongs to a predefined set of values (e.g. only employees of the company), great for dealing with categorical data
  * dedupe - remove duplicate entries, such as trimming down to unique users, accounts, or campaigns
  * merge - combine the data from multiple columns into a single column, such as merging first and last names into a full name
  * prune - filter out extraneous rows from a source table, commonly capturing rows that are empty or contain invalid data
  * embed - transform the data into a vector representation, such as converting text into word embeddings or dates into numerical values
  * measure - calculate a distance metric between two columns, often applied after embedding or tokenizing (e.g. cosine similarity, Jaccard index)
  * cross - using a third column to cross-reference the match, providing an additional layer of context beyond the person component
  * other - any advanced form of data transformation that is not listed above, such as fuzzy matching or complex interpolation
  
When crafting your response, please keep the following in mind:
  * Choose the minimum number of steps required for aligning the columns to avoid over-complicating the process
  * Prefer to apply simpler functions (e.g. replace) over more complex ones (e.g. validate)
  * Use the data preview to determine what is necessary, and avoid pre-emptively adding steps that are not needed
  * The `strip()` function is already applied at the start and end of the data preparation process, so you don't need to include it
  * Keep the order of the steps in mind, as some transformations may depend on others being completed first

Please start by examining the sampled data from each source table, and think carefully about how they may be combined to form a target table.
The source tables are '{table1}' and '{table2}', which are referred to as 'left_df' and 'right_df' respectively.
Your entire response should be in well-formatted JSON including your thought (string) and preparation (list), with no further explanations after the JSON output.
Each step in the preparation list should be a dict containing keys for name (token), table (left, right or both), and description (string).
The method names are chosen from the 16 valid forms of data preparation, while the description is a natural language explanation providing more details about the transformation.

For example,
#############
_Conversation History_
User: Can you join the two tables together based on the customer?
Agent: I can compare based on the customer names, does that sound right?
User: Yea, go for it

_Source Samples_
Left table: hubspot_ready
| FirstName | LastName      |
|-----------|---------------|
| Wei       | Zhang         |
| Jessica   | Vasquez       |
| Aditya    | Patel         |
| Sarah     | O'Connor      |
| Marcus    | Washington    |
| Elena     | Popov         |
| David     | Nguyen        |
| Fatima    | Al-Sayed      |

Right table: customer_orders
| full_name           |
|---------------------|
| Priya Sharma        |
| Maria Rodriguez     |
| Marcus Washington   |
| Elena Popov         |
| Miguel Torres       |
| David Nguyen        |
| Wei Zhang           |
| Aditya Patel        |

_Output_
```json
{{
  "thought": "Merging the first and last names together creates a new column that is better aligned with the full_name column.",
  "methods": [
    {{ "name": "merge", "table": "left", "description": "Combine the first and last names together to create a new column" }}
  ]
}}
```

_Conversation History_
User: We should combine them together so that we know who should get sent the patient satisfaction survey.
Agent: Is there a specific column you want to join on?
User: Both tables have patient names.

_Source Samples_
Left table: clinical_records
| patient_name         | visit_date  | diagnosis           | treatment_plan    |
|----------------------|-------------|---------------------|-------------------|
| Mr. James Wilson     | 2024-01-15  | Hypertension        | Diet & medication |
| Mrs. Sarah Chen      | 2024-01-16  | Type 2 Diabetes     | Insulin therapy   |
| Mr. Paolo Rossi      | 2024-01-16  | Arthritis           | Physical therapy  |
| Ms. Emily Parker     | 2024-01-17  | Asthma              | Inhaler regimen   |
| Mrs. Diana Lee       | 2024-01-17  | Migraine            | Preventive meds   |
| Mr. Thomas Grant     | 2024-01-18  | High Cholesterol    | Statins           |
| Dr. Patricia Wu      | 2024-01-18  | Hypothyroidism      | Hormone therapy   |
| Ms. Lauren Brooks    | 2024-01-19  | Anxiety             | SSRI medication   |

Right table: medication_adherence
| patient_fullname     | prescription      | adherence_rate |
|----------------------|-------------------|----------------|
| Mr James Wilson      | Lisinopril        | 92%            |
| Dr Patricia Wu       | Levothyroxine     | 95%            |
| Ms Lauren Brooks     | Sertraline        | 88%            |
| Mrs Diana Lee        | Sumatriptan       | 85%            |
| Mr Michael Voss      | Atorvastatin      | 83%            |
| Mr Kevin Zhang       | Metformin         | 78%            |
| Mr Robert Evans      | Meloxicam         | 90%            |
| Mrs Sarah Chen       | Metformin         | 87%            |

_Output_
```json
{{
  "thought": "The patient names are quite similar, simply removing the periods from the salutations should be sufficient to align them.",
  "methods": [
    {{ "name": "remove", "table": "left", "description": "Remove the periods from the patient_name column" }}
  ]
}}
```

_Conversation History_
User: We should combine them together so that we know who should get sent the survey.
Agent: Is there a specific column you want to join on?
User: Both tables have an email related column.

_Source Samples_
Left table: Pardot_Accounts
| company_name               | email                        | account_manager | last_contact | lead_quality_score |
|----------------------------|------------------------------|-----------------|--------------|--------------------|
| Datanami                   | N/A                          | Michael         | N/A          | 0                  |
| N/A                        | N/A                          | Emily           | 2024-01-30   | 73                 |
| N/A                        | <EMAIL> | Mike            | 2024-02-01   | 0                  |
| Allurance Federal Services | ask later                    | Michael         | 2024-01-15   | 42                 |
| Datanomi                   | <EMAIL>          | Michael         | 2024-02-15   | 94                 |
| McKinsey & Company         | <EMAIL>        | Emily           | 2023-06-30   | 65                 |
| Allurance                  | <EMAIL>            | Michael         | N/A          | 68                 |

Right table: Constant_Contact
| Email Address              | Twitter Handles |
|----------------------------|-----------------|
| <EMAIL>    | <N/A>           |
| <EMAIL>     | <N/A>           |
| <EMAIL>         | dataflowtech    |
| <EMAIL>           | <N/A>           |
| <EMAIL>        | <N/A>           |
| <EMAIL>       | InnovationSys   |
| <EMAIL>          | <N/A>           |
| <EMAIL>  | ComputingQStar  |

_Output_
```json
{{
  "thought": "Emails in the Pardot table are empty or placeholders. We will have to cross-reference against the company name or Twitter handles to perform a fuzzy match.",
  "methods": [
    {{ "name": "merge", "table": "left", "description": "Combine the company name and email columns together to create a new column" }},
    {{ "name": "merge", "table": "right", "description": "Combine the email address and Twitter handles together to create a new column" }},
    {{ "name": "tokenize", "table": "both", "description": "Break up the combined columns into multiple tokens based on spaces and punctuation" }},
    {{ "name": "measure", "table": "both", "description": "Calculate token similarity between the two new columns" }}
  ]
}}
```

_Conversation History_
User: Is there any correlation between the customer engagement and their subscription tier?
Agent: To get started, can you tell me how you would like to measure engagement?
User: Actually, let's do this, just join the customer interaction and account tables together.

_Source Samples_
Left table: customerInteractions
| username         | action_type      | endpoint             | duration_s |
|------------------|------------------|----------------------|------------|
| alana_m          | api_call         | /v2/process_data     | 2.34       |
| k.zhang2009      | dashboard_view   | /dashboard/metrics   | 45.67      |
| k.zhang2009      | api_call         | /v2/batch_analyze    | 1.89       |
| k.zhang2009      | report_export    | /reports/custom      | 12.45      |
| techLead_pat     | api_call         | /v2/transform        | 3.21       |
| alana_m          | dashboard_view   | /dashboard/alerts    | 28.90      |
| js_anderson      | report_export    | /reports/scheduled   | 8.76       |
| js_anderson      | api_call         | /v2/validate         | 1.56       |

Right table: consumerAccounts
| username         | subscription_tier |
|------------------|-------------------|
| dev.amit         | Pro               |
| jwheeler         | Pro               |
| mark.stevens     | Basic             |
| techLead_pat     | N/A               |
| ana.garcia       | Pro               |
| robert_k         | Basic             |
| deepa.s          | Pro               |
| peter.chen       | Basic             |

_Output_
```json
{{
  "thought": "Grouping the users in the interactions table by username will create a staging table that can be more easily joined with the accounts table.",
  "methods": [
    {{ "name": "stage", "table": "left", "description": "Group the users in the interactions table by username, averaging the duration_s to get a sense of engagement time" }}
  ]
}}
```

_Conversation History_
User: Our survey data is a bit messy, but can you connect it to our loyalty program data?
Agent: Certainly, I'd be happy to help! What specific aspects would you like to focus on?
User: We're mainly interested in understanding product preferences.

_Source Samples_
Left table: survey_responses
| respondentName     | productRating  | feedback                  | purchaseIntent |
|--------------------|----------------|---------------------------|----------------|
| John Smith         | 4              | Good quality for price    | Likely         |
| Maria Rodriguez    | 5              | Perfect for daily use     | Very Likely    |
| Jane Doe           | 3              | Could be improved         | Neutral        |
|                    | 4              | Great customer service    | Likely         |
| Anonymous          | 2              | Too expensive             | Unlikely       |
| David Chen         | 5              | Exceeds expectations      | Very Likely    |
| Test User          | 4              | Works as advertised       | Likely         |
| Sarah Martinez     | 3              | Decent but pricey         | Neutral        |

Right table: loyalty_program
| customer_name     | membership_tier | points_balance | last_purchase |
|-------------------|-----------------|----------------|---------------|
| Maria Rodriguez   | Gold            | 2500           | 2024-01-10    |
| David Chen        | Silver          | 1200           | 2024-01-15    |
| Sarah Martinez    | Bronze          | 800            | 2024-01-12    |
| Michael Patel     | Gold            | 3200           | 2024-01-08    |
| Lisa Thompson     | Silver          | 1500           | 2024-01-14    |
| James Wilson      | Bronze          | 600            | 2024-01-16    |
| Karen Lee         | Gold            | 2800           | 2024-01-11    |
| Robert Garcia     | Silver          | 950            | 2024-01-13    |

_Output_
```json
{{
  "thought": "The survey responses contain placeholder names and anonymous entries that need to be filtered out before joining with the loyalty program data.",
  "methods": [
    {{ "name": "prune", "table": "left", "description": "Remove rows with empty respondent names and anonymous entries" }},
    {{ "name": "prune", "table": "left", "description": "Ignore rows with placeholder names like 'John Smith' or 'Test User'" }}
  ]
}}
```

_Conversation History_
User: I've got a bunch of contacts in our CRM that I want to enrich with LinkedIn data. 
Agent: Ok, I can combine the two tables based on first and last name. How does that sound?
User: yea, that's right, we just need to make sure to be smart about it since sometimes the names aren't spelled out exactly the same.

_Source Samples_
Left table: crm_contacts
| first_name | last_name   | last_contacted | deal_stage    |
|------------|-------------|----------------|---------------|
| Mike       | Johnson     | 2024-01-15     | Discovery     |
| Bob        |             | 2024-01-16     | Prospecting   |
| Liz        | Zhang       | 2024-01-16     | Evaluation    |
| Jennifer   | K           | 2024-01-18     | Prospecting   |
| Alvaro     | Rodriguez   | 2024-01-17     | Negotiation   |
| Sam        | Christie    | 2024-01-17     | Discovery     |
| Travis     |             | 2024-01-18     | Discovery     |
| Katie      | M           | 2024-01-19     | Evaluation    |

Right table: linkedin_prospects
| firstname    | lastname    | title                    | company        |
|--------------|-------------|--------------------------|----------------|
| Robert       | Sanders     | CTO                      | DataSystems    |
| Alvaro       | Rodriguez   | Engineering Manager      | InnovateInc    |
| Samuel       | Thomas      | Principal Architect      | DevStack       |
| Elizabeth    | Zhang       | Director of Product      | CloudScale     |
| Jennifer     | Kumar       | VP of Operations         | PlatformX      |
| Christopher  | Williams    | Technical Lead           | CodeWorks      |
| Michael      | Johnson     | VP of Engineering        | TechCorp       |
| Kathryn      | Martinez    | Senior Product Manager   | AppDynamics    |

_Output_
```json
{{
  "thought": "The CRM contacts has inconsistent naming conventions compared to LinkedIn. Furthermore, we need to merge the columns before joining. Since there are multiple steps involved, this is an advanced scenario.",
  "methods": [
    {{ "name": "mapping", "table": "both", "description": "Change nicknames to full names where applicable" }},
    {{ "name": "merge", "table": "both", "description": "Combine the first and last names together" }},
    {{ "name": "extract", "table": "right", "description": "Add a truncated version of the lastname" }},
    {{ "name": "cross", "table": "both", "description": "Consider if the company names can be used to further verify the records" }}
  ]
}}
```
#############
Now it's your turn! Please decide on the best method to simplify the process of joining the two source tables together. For additional context, the full set of columns are:
{table1}: {columns1}
{table2}: {columns2}

_Conversation History_
{history}

_Source Samples_
Left table: {table1}
{samples1}

Right table: {table2}
{samples2}

_Output_
"""

basic_preparation_prompt = """Given the conversation history and supporting details, our goal is to prepare the data for joining the two tables together.
We have already decided on the approach, so now need to generate the Python code for doing so.
The function should follow the signature: `def prepare_func(left_df, right_df):` where left_df and right_df refer to the '{table1}' and '{table2}' tables respectively.
Your entire response should be directly executable Python code with inline comments, with no explanations before or after the code.

For example,
#############
_Conversation History_
User: Can I join the email campaign data with the support ticket data?
Agent: I'm not seeing any shared ids, how did you want to join these tables together?
User: By date

_Source Samples_
Left table: EmailCampaigns
| open_rate | click_rate | campaign_theme                    | campaign_month |
|-----------|------------|-----------------------------------|----------------|
| 22.4      | 3.8        | New Year Product Launch           | 2024-01        |
| 19.7      | 2.9        | Valentine's Day Flash Sale        | 2024-02        |
| 21.2      | 3.2        | Spring Collection Preview         | 2024-03        |
| 18.9      | 2.7        | Winter Clearance Final Days       | 2024-01        |
| 20.8      | 3.4        | Loyalty Member Exclusive Access   | 2024-02        |
| 23.1      | 4.1        | March Madness Special             | 2024-03        |
| 20.3      | 3.6        | Spring Break Early Bird           | 2024-03        |

Right table: SupportTicketsDetail
| ticket_count | category          | ticket_date |
|--------------|-------------------|-------------|
| 342          | Account Access    | 2024-01-15  |
| 289          | Payment Issues    | 2024-02-08  |
| 456          | Product Inquiry   | 2024-03-22  |
| 378          | Returns           | 2024-01-29  |
| 412          | Shipping Status   | 2024-02-14  |
| 267          | Account Access    | 2024-03-07  |
| 521          | Payment Issues    | 2024-02-28  |
| 398          | Product Inquiry   | 2024-01-08  |

Preparation Steps:
1. Extract (right) - convert to a datetime object to easily extract the month and year from the ticket date

_Output_
```python
def prepare_func(left_df, right_df):
  # Convert ticket_date to match campaign_month format
  right_df['ticket_month'] = pd.to_datetime(right_df['ticket_date']).dt.strftime('%Y-%m')
  return left_df, right_df
```

_Conversation History_
User: I need to match our trading records against a list of sanctioned companies, but it was scanned from a PDF using OCR, so the company names are broken up. Can you help?
Agent: Hmm, I can take a look. Are you trying to join the two tables together?
User: Yes, please join them together if you can.

_Source Samples_
Left table: TradingRecords_2024Q1
| company_name         | transaction_date | trade_value  |
|----------------------|------------------|--------------|
| Ace Global Trading   | 2024-01-15       | 1250000.00   |
| Phoenix Exports      | 2024-01-16       | 780000.00    |
| Crown Shipping Ltd   | 2024-01-16       | 1680000.00   |
| Silverstone Mining   | 2024-01-17       | 945000.00    |
| Meridian Resources   | 2024-01-17       | 1150000.00   |
| Quantum Industries   | 2024-01-18       | 1460000.00   |
| GT Intelligence      | 2024-01-18       | 998000.00    |
| Harbor Logistics     | 2024-01-19       | 1420000.00   |

Right table: SanctionedCompanies
| entity_name           | risk_level | jurisdiction |
|-----------------------|------------|--------------|
| Me ridan  Resources   | High       | International|
| Silv erst one Mining  | Medium     | Regional     |
| Quantum Industr ies   | High       | International|
| Red Dragon Trading    | Medium     | Regional     |
| Ace Glo bal Trading   | High       | International|
| Atlas  Petrole um     | Low        | Regional     |
| Crown Shipping  Ltd   | Medium     | Regional     |
| Sunburst En ergy      | High       | Regional     |

Preparation Steps:
1. Remove (both) - remove extra spaces from the company names
2. Casing (both) - convert all company names to lowercase

_Output_
```python
def prepare_func(left_df, right_df):
  # Clean trading records and sanctioned companies
  left_df['company_name'] = left_df['company_name'].str.replace(r'\\s+', '', regex=True).str.lower()
  right_df['entity_name'] = right_df['entity_name'].str.replace(r'\\s+', '', regex=True).str.lower()
  return left_df, right_df
```

_Conversation History_
User: Are there any patterns in customer satisfaction ratings based on the distance from the restaurant?
Agent: I can look into that. What kind of patterns are you looking for?
User: I'm looking for any patterns that might indicate if delivery satisfaction is correlated with distance from the restaurant.
Agent: Ok, I'll need to join the two tables together based on the addresses.
User: Ok, let's do that.

_Source Samples_
Left table: Customer Feedback
| deliveryDate | streetAddress         | satisfactionScore | deliveryTimeMins  |
|--------------|-----------------------|-------------------|-------------------|
| 2024-06-18   | 123 Oak Street        | 4.5               | 25                |
| 2024-06-15   | 456 Maple Ave         | 4.0               | 35                |
| 2024-06-16   | 789 Washington Blvd   | 3.5               | 45                |
| 2024-06-16   | 321 Pine Ridge Rd     | 4.8               | 20                |
| 2024-06-17   | 654 Market St         | 3.8               | 40                |
| 2024-06-17   | 987 Broadway Ave      | 4.2               | 30                |
| 2024-06-15   | 147 Jefferson Blvd    | 4.6               | 25                |
| 2024-06-18   | 258 Lincoln Road      | 3.9               | 35                |

Right table: restaurant_locations
| street_address          | cuisine_type |
|-------------------------|--------------|
| 123 Oak Street          | American     |
| 456 Maple Avenue        | Chinese      |
| 789 Washington Blvd     | American     |
| 321 Pine Ridge Road     | Italian      |
| 654 Market Street       | Japanese     |
| 987 Broadway Avenue     | American     |
| 147 Jefferson Boulevard | Thai         |
| 258 Lincoln Rd          | Mexican      |

Preparation Steps:
1. Mapping (both) - convert abbreviations such as St, Ave, Blvd, Rd to full names like Street, Avenue, Boulevard, and Road
2. Casing (both) - change all street names to lowercase

_Output_
```python
def prepare_func(left_df, right_df):
  abbrev_map = {{'St': 'Street', 'Ave': 'Avenue', 'Blvd': 'Boulevard', 'Rd': 'Road'}}
  # Standardize addresses
  left_df['streetAddress'] = left_df['streetAddress'].replace(abbrev_map, regex=True).str.lower()
  right_df['street_address'] = right_df['street_address'].replace(abbrev_map, regex=True).str.lower()
  return left_df, right_df
```

_Conversation History_
User: Are maintenance requests related to utility usage?
Agent: That's a good question. I'll need to join the two tables together based on the apartment numbers.
User: Ok, great. let's do that.

_Source Samples_
Left table: maintenance_requests
| apartment      | request_date | maintenance_type   |
|----------------|--------------|--------------------|
| APT 5A         | 2024-01-17   | Appliance Repair   |
| Apt. 4B        | 2024-01-15   | Plumbing           |
| Unit #2A       | 2024-01-16   | Electrical         |
| Apartment 3C   | 2024-01-16   | HVAC               |
| Unit 1B        | 2024-01-17   | Plumbing           |
| Apt #4C        | 2024-01-18   | Electrical         |
| Apartment 2B   | 2024-01-18   | HVAC               |
| APT. 3A        | 2024-01-19   | Appliance Repair   |

Right table: utility_usage
| apt_number | utility_type |
|------------|--------------|
| 4B         | Water        |
| 3C         | Electric     |
| 3A         | Water        |
| 5A         | Water        |
| 1B         | Water        |
| 4C         | Electric     |
| 2A         | Water        |
| 2B         | Electric     |

Preparation Steps:
1. Remove (left) - take out the hash symbol or periods from the apartment column
2. Tokenize (left) - split the apartment column into multiple tokens
3. Extract (left) - get the apartment number by taking the last part

_Output_
```python
def prepare_func(left_df, right_df):
  # Align apartment numbers
  left_df['apartment'] = left_df['apartment'].str.replace('#', '').str.split().str[-1]
  return left_df, right_df
```

_Conversation History_
User: I want to analyze how our daily inventory snapshots relate to actual sales transactions.
Agent: It sounds like you want to perform a Sales and Inventory Correlation analysis. Is that right?
User: Yes, that's right.
Agent: In order to perform this analysis, we need to join the sales_transactions and daily_inventory_snapshots tables.
User: Ok, how should we join them?

_Source Samples_
Left table: sales_transactions
| transaction_timestamp   | product_category | units_sold |
|-------------------------|------------------|------------|
| 2024-01-15 09:23:45     | Electronics      | 3          |
| 2024-01-15 14:17:32     | Electronics      | 2          |
| 2024-01-15 16:45:12     | Clothing         | 5          |
| 2024-01-16 10:30:00     | Electronics      | 1          |
| 2024-01-16 11:20:15     | Clothing         | 4          |
| 2024-01-16 15:45:22     | Electronics      | 2          |
| 2024-01-17 08:15:30     | Clothing         | 3          |
| 2024-01-17 13:40:18     | Electronics      | 4          |

Right table: daily_inventory_snapshots
| snapshotdate | product_category | inventory_id |
|--------------|------------------|--------------|
| 2024-01-15   | Electronics      | 14545725     |
| 2024-01-15   | Clothing         | 14523882     |
| 2024-01-16   | Electronics      | 14514044     |
| 2024-01-16   | Clothing         | 14522393     |
| 2024-01-17   | Electronics      | 14513410     |
| 2024-01-17   | Clothing         | 14522316     |
| 2024-01-18   | Electronics      | 14518330     |
| 2024-01-18   | Clothing         | 14562313     |

Preparation Steps:
1. Extract (left) - Keep just the date portion from the transaction timestamp

_Output_
```python
def prepare_func(left_df, right_df):
  # Align transaction dates
  left_df['transaction_date'] = pd.to_datetime(left_df['transaction_timestamp']).dt.date
  return left_df, right_df
```

_Conversation History_
User: We should combine them together so that we know who should get sent the patient satisfaction survey.
Agent: Is there a specific column you want to join on?
User: Both tables have patient names.

_Source Samples_
Left table: medication_adherence
| patient_fullname     | prescription      | adherence_rate |
|----------------------|-------------------|----------------|
| Mr James Wilson      | Lisinopril        | 92%            |
| Dr Patricia Wu       | Levothyroxine     | 95%            |
| Ms Lauren Brooks     | Sertraline        | 88%            |
| Mrs Diana Lee        | Sumatriptan       | 85%            |
| Mr Michael Voss      | Atorvastatin      | 83%            |
| Mr Kevin Zhang       | Metformin         | 78%            |
| Mr Robert Evans      | Meloxicam         | 90%            |
| Mrs Sarah Chen       | Metformin         | 87%            |

Right table: clinical_records
| patient_name         | visit_date  | diagnosis           | treatment_plan    |
|----------------------|-------------|---------------------|-------------------|
| Mr. James Wilson     | 2024-01-15  | Hypertension        | Diet & medication |
| Mrs. Sarah Chen      | 2024-01-16  | Type 2 Diabetes     | Insulin therapy   |
| Mr. Paolo Rossi      | 2024-01-16  | Arthritis           | Physical therapy  |
| Ms. Emily Parker     | 2024-01-17  | Asthma              | Inhaler regimen   |
| Mrs. Diana Lee       | 2024-01-17  | Migraine            | Preventive meds   |
| Mr. Thomas Grant     | 2024-01-18  | High Cholesterol    | Statins           |
| Dr. Patricia Wu      | 2024-01-18  | Hypothyroidism      | Hormone therapy   |
| Ms. Lauren Brooks    | 2024-01-19  | Anxiety             | SSRI medication   |

Preparation Steps:
1. Extract (left) - remove the periods from the patient_name column

_Output_
```python
def prepare_func(left_df, right_df):
  # Standardize patient_name by removing periods, the patient_fullname already lacks them so no action is needed
  left_df['patient_name'] = left_df['patient_name'].str.replace('.', '')
  return left_df, right_df
```

_Conversation History_
User: I want to combine vendor payment data with vendor risk assessment data from our compliance system.
Agent: Ok, I see the vendor payments table and the vendor risk assessments table. Is that right?
User: Yes, that's right. Join them based on the vendor name.

_Source Samples_
Left table: vendor_payments
| vendor_name         | payment_date | payment_amount | payment_terms |
|---------------------|-------------|----------------|---------------|
| McDonald's          | 2024-01-15  | 25,450.00      | Net 30        |
| AT&T Global         | 2024-01-16  | 18,320.00      | Net 45        |
| Sysco-Foods Ltd     | 2024-01-16  | 42,150.00      | Net 30        |
| Johnson & Johnson   | 2024-01-17  | 65,800.00      | Net 60        |
| Costco Wholesale    | 2024-01-17  | 33,240.00      | Net 30        |
| U.S. Bank, Inc.     | 2024-01-18  | 12,780.00      | Net 45        |
| Ben & Jerry's       | 2024-01-18  | 8,920.00       | Net 30        |
| Brother-Tech        | 2024-01-19  | 15,640.00      | Net 30        |

Right table: vendor_risk_assessments
| company_name       | risk_score | last_audit_date | industry_category |
|--------------------|------------|-----------------|-------------------|
| US Bank Inc        | 82         | 2023-12-15      | Financial         |
| McDonalds          | 78         | 2023-12-20      | Food/Beverage     |
| Brother Tech       | 75         | 2023-12-22      | Technology        |
| Wells Fargo & Co   | 85         | 2023-12-28      | Financial         |
| Sysco Foods        | 80         | 2024-01-02      | Distribution      |
| Ben and Jerrys     | 77         | 2024-01-05      | Food/Beverage     |
| Adobe Inc          | 79         | 2024-01-08      | Technology        |
| ATT Global         | 83         | 2024-01-10      | Telecommunications|

Preparation Steps:
1. Extract (both) - remove punctuation characters from both tables
2. Extract (both) - drop common stop words such as 'and', 'inc', 'ltd', and 'co'
3. Casing (both) - convert all vendor names to lowercase

_Output_
```python
def prepare_func(left_df, right_df):
  # Define patterns to remove and standardize company names
  removals = '\'|\.|\,|\&|\-|corp|inc|ltd|co|and'
  # Clean both company name columns
  left_df['vendor_name'] = left_df['vendor_name'].str.lower().str.replace(removals, '', regex=True).str.strip()
  right_df['company_name'] = right_df['company_name'].str.lower().str.replace(removals, '', regex=True).str.strip()
  return left_df, right_df
```
#############
Now it's your turn! Please output executable Python code with inline comments to prepare the data for joining the two tables together.

_Conversation History_
{history}

_Source Samples_
Left table: {table1}
{samples1}

Right table: {table2}
{samples2}

Preparation Steps:
{steps}

_Output_
"""

split_style_prompt = """Given the conversation history and supporting details, think briefly about the best way to split a single column into multiple columns, and then generate the Pandas code for doing so.
Supporting details includes the head of the source column and the resulting target columns. Sometimes, the target columns may be unknown, in which case you should use your best judgement to name them.

You are given access to the source dataframe in form of `db.{tab_name}`, which means you can access the columns directly.
Please only output executable Python code and inline comments. If a request requires multiple steps, write each step on a new line. It is absolutely critical that there are no explanations after the code.

For example,
#############
_Conversation History_
User: Can you split the address column into separate columns for street, city, state, and zip code?
Agent: Sure, I have split the address column into separate columns for street, city, state, and zip code.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9)

_Output_
```python
db.CustomerInfo[['street', 'city', 'state', 'zip_code']] = db.CustomerInfo['Address'].str.split(', ', expand=True)
```

_Conversation History_
User: Can you split the address column into separate columns for street, city, state, and zip code?
Agent: Sure, I have split the address column into separate columns for street, city, state, and zip code.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9)

_Output_
```python
db.CustomerInfo[['street', 'city', 'state', 'zip_code']] = db.CustomerInfo['Address'].str.split(', ', expand=True)
```
#############
Now it's your turn! Please output executable Python code with inline comments to split the column as requested.

_Conversation History_
{history}

_Supporting Details_
Table: {table}
Columns: {columns}
Rows: {rows}
Samples:
{additional}

_Output_
"""

transpose_prompt = """Given the conversation history and supporting details, think briefly about the best way to transpose the data, and then generate the Pandas code for doing so.
Supporting details includes the head of the source column and the resulting target columns. Sometimes, the target columns may be unknown, in which case you should use your best judgement to name them.

You are given access to the source dataframe in form of `db.{tab_name}`, which means you can access the columns directly.
Please only output executable Python code and inline comments. If a request requires multiple steps, write each step on a new line. It is absolutely critical that there are no explanations after the code.

For example,
#############
_Conversation History_
User: Can you transpose the table so that the rows become columns and the columns become rows?
Agent: Sure, I have transposed the table so that the rows become columns and the columns become rows.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9)

_Output_
```python
db.CustomerInfo = db.CustomerInfo.transpose()
```

_Conversation History_
User: Can you transpose the table so that the rows become columns and the columns become rows?
Agent: Sure, I have transposed the table so that the rows become columns and the columns become rows.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9)

_Output_
```python
db.CustomerInfo = db.CustomerInfo.transpose()
```
#############
Now it's your turn! Please output executable Python code with inline comments to transpose the data as requested.

_Conversation History_
{history}

_Supporting Details_
Table: {table}
Columns: {columns}
Rows: {rows}
Samples:
{additional}

_Output_
"""

transform_issues_prompt = """Given the conversation history and supporting details, think briefly about the best way to interpolate the missing values, and then generate the Pandas code for doing so.
Supporting details includes the head of the source column and the resulting target columns. Sometimes, the target columns may be unknown, in which case you should use your best judgement to name them.

You are given access to the source dataframe in form of `db.{tab_name}`, which means you can access the columns directly.
Please only output executable Python code and inline comments. If a request requires multiple steps, write each step on a new line. It is absolutely critical that there are no explanations after the code.

For example,
---
_Conversation History_
User: Can you fill in the missing values in the 'age' column with the average age of the customers?
Agent: Sure, I have filled in the missing values in the 'age' column with the average age of the customers.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9), Age (10)
Rows: Age is null
Samples:
|     |  CustomerID  |  FirstName  |  LastName  |  Email  |  Phone  |  Address  |  City  |  State  |  ZipCode  |  Age  |
|----:|:------------|:------------|:-----------|:--------|:--------|:----------|:--------|:---------|:----------|:-------|
|  0  |  1          |  John       |  Doe       |  <EMAIL>  |  1234567890  |  123 Main St  |  New York  |  NY  |  10001  |  30  |
|  1  |  2          |  Jane       |  Smith     |  <EMAIL>  |  0987654321  |  456 Elm St  |  Los Angeles  |  CA  |  90001  |  25  |
|  2  |  3          |  Bob        |  Johnson   |  <EMAIL>  |  1122334455  |  789 Oak St  |  Chicago  |  IL  |  60601  |  40  |
|  3  |  4          |  Alice      |  Brown     |  <EMAIL>  |  5555555555  |  321 Pine St  |  Houston  |  TX  |  77001  |  28  |
|  4  |  5          |  Tom        |  Davis     |  <EMAIL>  |  9998887777  |  654 Maple St  |  Phoenix  |  AZ  |  85001  |  35  |

_Output_
```python
db.CustomerInfo['Age'].fillna(db.CustomerInfo['Age'].mean(), inplace=True)
```

_Conversation History_
User: Can you fill in the missing values in the 'age' column with the average age of the customers?
Agent: Sure, I have filled in the missing values in the 'age' column with the average age of the customers.

_Supporting Details_
Table: CustomerInfo
Columns: CustomerID (1), FirstName (2), LastName (3), Email (4), Phone (5), Address (6), City (7), State (8), ZipCode (9), Age (10)
Rows: Age is null
Samples:
|     |  CustomerID |  FirstName  |  LastName  |  Email  |  Phone  |
|----:|:------------|:------------|:-----------|:--------|:--------|
|  0  |  1          |  John       |  Doe       |  <EMAIL>  |  1234567890  |
|  1  |  2          |  Jane       |  Smith     |  <EMAIL>  |  0987654321  |
|  2  |  3          |  Bob        |  Johnson   |  <EMAIL>  |  1122334455  |

_Output_
```python
db.CustomerInfo['Age'].fillna(db.CustomerInfo['Age'].mean(), inplace=True)
```
---
Now it's your turn! Please output executable Python code with inline comments to interpolate the missing values as requested.

_Conversation History_
{convo_history}

_Supporting Details_
{df_description}
Rows: {issues}

_Output_
"""

append_alignment_prompt = """Your task is to determine if columns between the target tables are aligned for row appending and to verify the order of the tables.
To aid you in this task, you are given the conversation history along with supporting details including the name of each table, followed by their row count.
Beneath each table name, you are also provided with a preview of the first few rows in markdown format.

Please start by thinking carefully about whether appended columns can logically function as a single extended dataset. Specifically, evaluate alignment based on:
  * Column names - names should be exact matches or contain a high degree of similarity
  * Data type - must be the same, which can be either all numeric, all text, all locations, or all datetimes
  * Content - must be compatible; content examination is the definitive source of truth for alignment

Additionally, this is also our chance to review the order of the tables, and whether the order should be changed. Namely, evaluate order based on:
  1. User requests - the highest priority is always to obey any explicit instructions from the user
  2. Timely progression - when there is a clear temporal relationship from one table to another, then the earlier table should come first, while the later table should come last
  3. Table content - suppose there is a column that shows names sorted alphabetically, then the table with names starting with 'A' should come first, and names starting with 'Z' should come last
  4. Length - if no clear progression is apparent, then the longer table with more rows should ordered first, while the shorter table(s) should come afterwards
  5. Alphabetical - if the table sizes are roughly equal, then you can consider the alphabetical order of the table names
  6. Default - In the absence of any relationships, just list the tables in the order they were mentioned in the supporting details.

Your entire response should be in well-formatted JSON including your thought (string), alignment (boolean), and ordered tables (list), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Can you delete all the rows with 0 clicks?
Agent: No problem, I have removed them. There are now 17 rows left for August.
User: OK, now let's join all the data together into one table.

_Supporting Details_
Table 1: august_emails (17 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Summer Sale               | 1500        | 351         | 254          | 20                 | 10            |
|  2        | Limited Time Offer        | 1800        | 402         | 301          | 25                 | 12            |
|  3        | Clearance Sale            | 1200        | 282         | 202          | 18                 | 8             |
|  4        | Perfect Beach Vacation    | 1100        | 254         | 184          | 15                 | 7             |

Table 2: september_emails (14 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Back to School Sale       | 1000        | 259         | 158          | <N/A>              | 5             |
|  2        | Fall Fashion Collection   | 1200        | 312         | 209          | <N/A>              | 7             |
|  3        | Holiday Special           | 800         | 188         | 120          | <N/A>              | 4             |
|  4        | Exclusive Offer           | 900         | 229         | 167          | <N/A>              | 6             |

Table 3: october_emails (16 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Halloween Special         | 1300        | 300         | 229          | 15                 | 10            |
|  2        | Cyber Monday Sale         | 1100        | 275         | 182          | 12                 | 8             |
|  3        | Fall into Savings         | 1400        | 350         | 263          | 18                 | 12            |
|  4        | Black Friday Deal         | 1000        | 250         | 171          | 11                 | 7             |

_Output_
```json
{{
  "thought": "The total unsubscribes is missing from the September table, but all the other content is perfectly aligned. In terms of ordering, there is clear chronological order from August to October.",
  "alignment": true,
  "ordered_tables": ["august_emails", "september_emails", "october_emails"]
}}
```

_Conversation History_
User: Oh wait, I see there's actually multiple tables with customer support tickets.
Agent: Yes, which one would you like to use?
User: We should connect the latest tickets to all the previous ones before calculating the average response time.

_Table Details_
Table 1: support_desk_tickets (407 rows)
| ticket_id  | customer_id | agent_assigned | priority_level | ticket_status | date_created  | time_to_respond | type_of_issue      | date_resolved   | cust_satisfaction | ticket_source |
|------------|-------------|----------------|----------------|---------------|---------------|-----------------|--------------------|-----------------|-------------------|---------------|
| 0923314    | CUST901     | alex.m         | High           | Open          | 2023-08-19    | 25              | Login Issue        | <N/A>           | <N/A>             | web           |
| 0923315    | CUST234     | emma.r         | Medium         | In Progress   | 2023-08-12    | 40              | Payment Issue      | <N/A>           | <N/A>             | email         |
| 0923316    | CUST567     | david.p        | Low            | Resolved      | 2023-08-16    | 120             | Feature Request    | 2023-01-18      | 4.0               | chat          |
| 0923317    | CUST890     | <N/A>          | High           | Pending       | 2023-08-18    | <N/A>           | Service Down       | <N/A>           | <N/A>             | phone         |

Table 2: customer_service_requests (1360 rows)
| request_id | customer_id | assigned_agent | priority_level | request_status | creation_date | response_time | issue_type         | resolution_date | csat_score | request_source |
|------------|-------------|----------------|----------------|----------------|---------------|---------------|--------------------|-----------------|------------|----------------|
| CSR-892    | CUST456     | sarah.k        | HIGH           | RESOLVED       | 2023-08-17    | 45            | Login Failed       | 2023-08-16      | 4.5        | email          |
| CSR-893    | CUST990     | mike.t         | MEDIUM         | RESOLVED       | 2023-08-15    | 180           | Billing Question   | 2023-08-17      | 3.0        | chat           |
| CSR-894    | CUST234     | NULL           | LOW            | CANCELLED      | 2023-08-16    | NULL          | Feature Request    | NULL            | NULL       | web            |
| CSR-895    | CUST567     | john.d         | HIGH           | RESOLVED       | 2023-08-16    | 15            | Service Outage     | 2023-08-16      | 5.0        | phone          |

_Output_
```json
{{
  "thought": "The column names are not the same, but they are exactly aligned in meaning, so we are appending is still possible. The times are all in August 2023, so that does not provide an order. Instead, we will rely on length - since the customer_service_requests table has significantly more rows, it should go first.",
  "alignment": true,
  "ordered_tables": ["customer_service_requests", "support_desk_tickets"]
}}
```

_Conversation History_
User: Let's rename it to just 'impressions' to keep it simple.
Agent: OK, I have renamed the column to 'impressions'. How does this look?
User: Cool, now can I merge the two campaigns together?

_Supporting Details_
Table 1: FamilyProducts (7 rows)
| uuid  | campaign_name   | ad_sets                 | impressions | likes        | comments       | ad_spend | conversions  |
|-------|-----------------|-------------------------|-------------|--------------|----------------|----------|--------------|
| 41436 | Family Products | Family Mealtime         | 113         |  5           |  8             | $91.12   |  57          |
| 41437 | Family Products | Kid-tested Mom-approved | 23          |  0           |  12            | $20.33   |  19          |
| 41438 | Family Products | Child Solutions         | 69          |  0           |  5             | $48.21   |  32          |
| 41439 | Family Products | Family-friendly Tips    | 39          |  3           |  17            | $21.87   |  10          |

Table 2: SeasonalPromotions (5 rows)
| uuid  | campaign_name   | ad_group                  | impressions | interactions | allowed_budget |  spent   | total_clicks |
|-------|-----------------|---------------------------|-------------|--------------|----------------|----------|--------------|
| 2     | Seasonal Promos | New Year, New Habits      | 178         | 7            | $150           | $132.45  | 89           |
| 3     | Seasonal Promos | Holiday Gift Sets         | 256         | 12           | $200           | $187.93  | 124          |
| 4     | Seasonal Promos | Winter Warm Beverages     | 142         | 4            | $800           | $76.29   | 51           |
| 5     | Seasonal Promos | Spring Cleaning Specials  | 195         | 9            | $120           | $109.78  | 73           |

_Output_
```json
{{
  "thought": "Based on 'ad sets' vs 'ad group', it seems the Family Product table covers campaigns from Facebook, while the other is from Google Ads. The difference in events (interactions vs likes) and spend (ad_spend vs spent) is not a big deal. You could even make the argument that clicks and conversions are aligned, but 'comments' and 'allowed_budget' clearly are not because they are different data types.",
  "alignment": false,
  "ordered_tables": ["FamilyProducts", "SeasonalPromotions"]
}}
```

_Conversation History_
User: let's also drop any rows where the EBITDA is empty
Agent: You got it, I have removed all rows with null values in the EBITDA column.
User: Can you append the one from last year to the one from this year?

_Supporting Details_
Table 1: CONV_ANNUAL_REVIEW (12 rows)
| Month    | Revenue  | COGS ($) | Gross Profit | Operating Expenses | EBITDA ($) | Depreciation ($) | Interest ($) | Taxes ($) | Net Income (000s) | Profit Margin (%) |
|:---------|----------|----------|--------------|--------------------|------------|------------------|--------------|-----------|-------------------|------------------:|
| January  | 245,000  | 98,000   | 147,000      | 85,000             | 62,000     | 8,500            | 3,200        | 12,575    | 37,725            | 15.4              |
| February | 268,500  | 107,400  | 161,100      | 88,500             | 72,600     | 8,500            | 3,100        | 15,250    | 45,750            | 17.0              |
| March    | 298,500  | 119,400  | 179,100      | 91,500             | 87,600     | 8,500            | 2,950        | 19,038    | 57,113            | 19.1              |
| April    | 325,000  | 130,000  | 195,000      | 93,500             | 101,500    | 8,500            | 2,850        | 22,538    | 67,613            | 20.8              |

Table 2: CONV_YTD (5 rows)
| Month    | Revenue  | COGS     | Gross Profit | Operating Expenses | EBITDA   | Depreciation | Interest | Taxes  | Net Income | Profit Margin |
|:---------|----------|----------|--------------|--------------------|----------|--------------|----------|--------|------------|--------------:|
| January  | 287,500  | 115,000  | 172,500      | 92,000             | 80,500   | 9,200        | 2,800    | 17,125 | 51,375     | 17.9          |
| February | 342,000  | 136,800  | 205,200      | 96,500             | 108,700  | 9,200        | 2,650    | 24,213 | 72,638     | 21.2          |
| March    | 356,500  | 142,600  | 213,900      | 97,000             | 116,900  | 9,200        | 2,700    | 26,250 | 78,750     | 22.1          |
| April    | 378,000  | 151,200  | 226,800      | 98,000             | 128,800  | 9,200        | 2,600    | 29,250 | 87,750     | 23.2          |

_Output_
```json
{{
  "thought": "The YTD table contains the same columns as the Annual Review table, but has simply removed the formatting in parentheses, so they are still aligned. For ordering, YTD table represents the current year, while Annual Review represents the past year, so Annual Review should come first.",
  "alignment": true,
  "ordered_tables": ["CONV_ANNUAL_REVIEW", "CONV_YTD"]
}}
```

_Conversation History_
User: So I just uploaded the data from the AI Developer Conference
Agent: Yes, I see that. What would you like to do with it?
User: I'd like to combine all the data from each day into one table. Is that possible?

_Supporting Details_
Table 1: GPU_Computing_Day1 (192 rows)
| name               | company          | role           | email address           | interest level | main product line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Priya Sharma       | NeuralTech       | GPU Architect  | <EMAIL>  | High           | Cost optimization           |
| David Chen         | ByteWorks        | CIO            | <EMAIL> |                | Workflow integration        |
| Lisa Taylor        | Insight AI       | ML Engineer    | <EMAIL>  |                | Compute resources           |
| Michael Brown      | N/A              | Data Manager   | <EMAIL>  |                | Production deployment tools |
| Olivia Garcia      | DataSphere       | ML Operations  | <EMAIL>   | High           | Cost optimization           |

Table 2: Model_Evaluation_Day2 (211 rows)
| name               | company          | role           | email address           | interest level | main product line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Ming Yang          | ByteDance        | Researcher     | <EMAIL> | High           | Compute resources           |
| Kevin Sanders      | MultiOne         | ML Engineer    | <EMAIL>   | High           | Cost optimization           |
| Denise Sims        | Quadratic AI     | ML Consultant  | <EMAIL>     | Medium         | Workflow integration        |
| Tyrone Hill        | N/A              | Engineer       | <EMAIL>     |                | Just exploring options      |
| Andrea Madotto     | Vazy Data        | AI Engineer    | <EMAIL>   | Low            | Just exploring options      |

Table 3: ML_Ops_Day3 (138 rows)
| Name               | Company          | Role           | Email Address           | Interest Level | Main Product Line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Alex Johnson       | techscale        | ML Engineer    | <EMAIL>    | High           | Production deployment tools |
| Maya Patel         | DataStream       | CTO            | <EMAIL>    | Medium         | Workflow integration        |
| Chris Wong         | Self-employed    | ML Consultant  | <EMAIL>  | High           | Workflow integration        |
| Sarah Miller       | CloudNine        | Data Scientist | <EMAIL> | Low            | Just exploring options      |
| James Rodriguez    | quantum analytics| Eng Manager    | <EMAIL>   | High           | Compute resources           |

_Output_
```json
{{
  "thought": "The column headers from ML OPs Day 3 are capitalized, but the content is still aligned with the other tables, so this is fine. We should follow the temporal progression from Day 1 to Day 3 for ordering.",
  "alignment": true,
  "ordered_tables": ["GPU_Computing_Day1", "Model_Evaluation_Day2", "ML_Ops_Day3"]
}}
```
---
Now it's your turn! Please output executable Python code with inline comments to decide on column alignment and table order.

_Conversation History_
{history}

_Table Details_
{table_details}

_Output_
"""

append_target_prompt = """As seen in the conversation history, the user is trying to append data from the {source_tabs} tables together into a new table.
As a result, we will need to generate a name for the joint table that reflects the meaning of the combined data.
We also need to decide whether to insert a column to distinguish the data from each of the tables, and if so, what to name this column.
To aid you in this task, the supporting details will provide the name of each table, followed by the total number of rows.
Beneath each table name, will be a preview of the first few rows in markdown format.

Please start by identifying the distinguishing factor between the {count} tables, and then generate a relevant column name that matches the naming conventions of the other columns that already exist.
Concretely, consider the following in your thoughts:
  * What is the formatting convention based on the existing columns: underscores, spaces, or camelCase? lowercase or uppercase?
  * If the columns in the table contain multiple formats, pick the format that occurs most frequently.
  * Do the other columns follow some sort of pattern? If so, what is the common theme? Perhaps, they tend to use abbreviations or acronyms?
  * Prefer concise names that align with the column's purpose (ie. month) rather than long names describing the contents (ie. month_extracted_from_date).
  * Keep the column name as short as possible. Do not add extra parts, such as '_column' or '_data', if it is not necessary.
  * Of course, you should come up with a unique column name that does not conflict with any existing ones.

In addition, please generate a relevant name for the joint table that matches the naming conventions of the other tables in the spreadsheet.
Follow the same guidelines as above in regards to keeping the name as short as possible. Drop any unnecessary parts, such as 'table' or 'data'.

Your entire response should be in well-formatted JSON including keys for thought (string), column (string), and table (string), with no further explanations after the JSON output.
If there is no clear distinguishing factor between the two tables, then set the column name as 'unsure'. Sometimes, the distinguishing factor can already be found in an existing column, in which case you can reuse that column name.
If the joint table name is ambiguous, then just default to the name of the first table in the list.

For example,
---
_Conversation History_
User: Can you delete all the rows with 0 clicks?
Agent: No problem, I have removed them. There are now 17 rows left for August.
User: OK, now let's join all the data together into one table.

_Supporting Details_
Table 1: august_emails (17 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Summer Sale               | 1500        | 351         | 254          | 20                 | 10            |
|  2        | Limited Time Offer        | 1800        | 402         | 301          | 25                 | 12            |
|  3        | Clearance Sale            | 1200        | 282         | 202          | 18                 | 8             |
|  4        | Perfect Beach Vacation    | 1100        | 254         | 184          | 15                 | 7             |

Table 2: september_emails (14 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Back to School Sale       | 1000        | 259         | 158          | 10                 | 5             |
|  2        | Fall Fashion Collection   | 1200        | 312         | 209          | 15                 | 7             |
|  3        | Holiday Special           | 800         | 188         | 120          | 8                  | 4             |
|  4        | Exclusive Offer           | 900         | 229         | 167          | 12                 | 6             |

Table 3: october_emails (16 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Halloween Special         | 1300        | 300         | 229          | 15                 | 10            |
|  2        | Cyber Monday Sale         | 1100        | 275         | 182          | 12                 | 8             |
|  3        | Fall into Savings         | 1400        | 350         | 263          | 18                 | 12            |
|  4        | Black Friday Deal         | 1000        | 250         | 171          | 11                 | 7             |

_Output_
```json
{{
  "thought": "The tables represent different months, so the new column should indicate the month using spaces. This should be capitalized to match the other columns. Table naming uses underscores, but we want to keep it short, so 'emails' is better than 'august_september_october_emails'.",
  "column": "Month",
  "table": "emails"
}}
```

_Conversation History_
User: So I just uploaded the data from the AI Developer Conference
Agent: Yes, I see that. What would you like to do with it?
User: I'd like to combine all the data from each day into one table. Is that possible?

_Supporting Details_
Table 1: GPU_Computing_Day1 (192 rows)
| name               | company          | role           | email address           | interest level | main product line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Priya Sharma       | NeuralTech       | GPU Architect  | <EMAIL>  | High           | Cost optimization           |
| David Chen         | ByteWorks        | CIO            | <EMAIL> |                | Workflow integration        |
| Lisa Taylor        | Insight AI       | ML Engineer    | <EMAIL>  |                | Compute resources           |
| Michael Brown      | N/A              | Data Manager   | <EMAIL>  |                | Production deployment tools |
| Olivia Garcia      | DataSphere       | ML Operations  | <EMAIL>   | High           | Cost optimization           |

Table 2: Model_Evaluation_Day2 (211 rows)
| name               | company          | role           | email address           | interest level | main product line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Ming Yang          | ByteWorks        | CTO            | <EMAIL> | High           | Compute resources           |
| Kevin Sanders      | MultiOne         | ML Engineer    | <EMAIL>   | High           | Cost optimization           |
| Denise Sims        | Quadratic AI     | ML Consultant  | <EMAIL>     | Medium         | Workflow integration        |
| Tyrone Hill        | N/A              | Engineer       | <EMAIL>     |                | Just exploring options      |
| Andrea Madotto     | Vazy Data        | AI Engineer    | <EMAIL>   | Low            | Just exploring options      |

Table 3: ML_Ops_Day3 (138 rows)
| name               | company          | role           | email address           | interest level | main product line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Alex Johnson       | techscale        | ML Engineer    | <EMAIL>    | High           | Production deployment tools |
| Maya Patel         | DataStream       | CTO            | <EMAIL>    | Medium         | Workflow integration        |
| Chris Wong         | Self-employed    | ML Consultant  | <EMAIL>  | High           | Workflow integration        |
| Sarah Miller       | CloudNine        | Data Scientist | <EMAIL> | Low            | Just exploring options      |
| James Rodriguez    | quantum analytics| Eng Manager    | <EMAIL>   | High           | Compute resources           |

_Output_
```json
{{
  "thought": "The tables represent customers from different days at the conference, so the new column should indicate the theme of each day. This should be lowercase to match the other columns. Table naming is proper case with underscores, so we can use 'Conference_Prospects'.",
  "column": "theme",
  "table": "Conference_Prospects"
}}
```

_Conversation History_
User: Let's rename it to just 'impressions' to keep it simple.
Agent: OK, I have renamed the column to 'impressions'. How does this look?
User: Cool, now can I merge the two campaigns together?

_Supporting Details_
Table 1: FamilyProducts (7 rows)
| fp_id | campaign_name   | ad_group                | impressions | interactions | allowed_budget |  spent   | total_clicks |
|-------|-----------------|-------------------------|-------------|--------------|----------------|----------|--------------|
| 41436 | Family Products | Family Mealtime         | 113         |  5           |  100           | $91.12   |  57          |
| 41437 | Family Products | Kid-tested Mom-approved | 23          |  0           |  20            | $20.33   |  19          |
| 41438 | Family Products | Child Solutions         | 69          |  0           |  50            | $48.21   |  32          |
| 41439 | Family Products | Family-friendly Tips    | 39          |  3           |  20            | $21.87   |  10          |

Table 2: SeasonalPromotions (5 rows)
| sp_id | campaign_name   | ad_group                  | impressions | interactions | allowed_budget |  spent   | total_clicks |
|-------|-----------------|---------------------------|-------------|--------------|----------------|----------|--------------|
| 2     | Seasonal Promos | New Year, New Habits      | 178         | 7            | 150            | $132.45  | 89           |
| 3     | Seasonal Promos | Holiday Gift Sets         | 256         | 12           | 200            | $187.93  | 124          |
| 4     | Seasonal Promos | Winter Warm Beverages     | 142         | 4            | 800            | $76.29   | 51           |
| 5     | Seasonal Promos | Spring Cleaning Specials  | 195         | 9            | 120            | $109.78  | 73           |

_Output_
```json
{{
  "thought": "The tables represent different camapaigns, but the campaign column already exists, so we will just use that. The table names are uppercase with no spaces, so we could go with 'JointCampaigns', but even better is a shorter name like 'Campaigns'. There is no discernable order between the tables, so we will just list them alphabetically.",
  "column": "campaign_name",
  "table": "Campaigns"
}}
```

_Conversation History_
User: let's also drop any rows where the EBITDA is empty
Agent: You got it, I have removed all rows with null values in the EBITDA column.
User: Can you append the one from last year to the one from this year?

_Supporting Details_
Table 1: CONV_ANNUAL_REVIEW (12 rows)
| Month    | Revenue  | COGS ($) | Gross Profit | Operating Expenses | EBITDA ($) | Depreciation ($) | Interest ($) | Taxes ($) | Net Income (000s) | Profit Margin (%) |
|:---------|----------|----------|--------------|--------------------|------------|------------------|--------------|-----------|-------------------|------------------:|
| January  | 245,000  | 98,000   | 147,000      | 85,000             | 62,000     | 8,500            | 3,200        | 12,575    | 37,725            | 15.4              |
| February | 268,500  | 107,400  | 161,100      | 88,500             | 72,600     | 8,500            | 3,100        | 15,250    | 45,750            | 17.0              |
| March    | 298,500  | 119,400  | 179,100      | 91,500             | 87,600     | 8,500            | 2,950        | 19,038    | 57,113            | 19.1              |
| April    | 325,000  | 130,000  | 195,000      | 93,500             | 101,500    | 8,500            | 2,850        | 22,538    | 67,613            | 20.8              |

Table 2: CONV_YTD (5 rows)
| Month    | Revenue  | COGS ($) | Gross Profit | Operating Expenses | EBITDA ($) | Depreciation ($) | Interest ($) | Taxes ($) | Net Income (000s) | Profit Margin (%) |
|:---------|----------|----------|--------------|--------------------|------------|------------------|--------------|-----------|-------------------|------------------:|
| January  | 287,500  | 115,000  | 172,500      | 92,000             | 80,500     | 9,200            | 2,800        | 17,125    | 51,375            | 17.9              |
| February | 342,000  | 136,800  | 205,200      | 96,500             | 108,700    | 9,200            | 2,650        | 24,213    | 72,638            | 21.2              |
| March    | 356,500  | 142,600  | 213,900      | 97,000             | 116,900    | 9,200            | 2,700        | 26,250    | 78,750            | 22.1              |
| April    | 378,000  | 151,200  | 226,800      | 98,000             | 128,800    | 9,200            | 2,600        | 29,250    | 87,750            | 23.2              |

_Output_
```json
{{
  "thought": "The tables represent finances the previous year and current year, so the new column should indicate the year. This should be capitalized to match the other columns. Table naming is capitalized with underscores, so 'CONV_FINANCIALS' is a good name.",
  "column": "Year",
  "table": "CONV_FINANCIALS"
}}
```

_Conversation History_
User: I just got a bunch of opps from the BDR team, which we should connect to the leads from the AEs
Agent: There are multiple tables with to choose from, which ones would you like to use?
User: Ok, let's go with the qualified leads and the CRM data

_Supporting Details_
Table 1: Qualified_Leads (407 rows)
| Email_Address           | First_Name | Last_Name   | Company_Name  | Job_Title             | Phone_Number  | Lead_Source | Last_Contact_Date | Lead_Score | Status     | Lead_Owner  |
|-------------------------|------------|-------------|---------------|-----------------------|---------------|-------------|-------------------|------------|------------|-------------|
| <EMAIL>      | Tom        | Reed        | Amazon        | Product Director      | ************  | Website     | 2024-02-01        | 82         | New        | k.patel     |
| <EMAIL> | Jenny      | Richards    | Uber          | Business Analyst      | ************  | Website     | 2024-02-03        | 75         | New        | m.wilson    |
| <EMAIL>  | Mike       | Roberts     |               | Engineering Manager   | ************  | Conference  | 2024-02-05        | 90         | Interested | m.myers     |
| <EMAIL>   | Sarah      | Shaw        | Google        |                       | NULL          | Webinar     | 2024-02-07        | NULL       | Pending    | m.wilson    |

Table 2: MS_Dynamics_All (1360 rows)
| email_address            | first_name | last_name   | company_name    | job_title          | phone_number  | lead_source | last_contact_date | lead_score | status     | lead_owner  |
|:-------------------------|------------|-------------|-----------------|--------------------|---------------|-------------|-------------------|------------|------------|-------------|
| <EMAIL>       | Peter      | Chan        | Zoom            |                    | NULL          | email       | 2024-01-15        | 85         | contacted  | p.johannson |
| <EMAIL> | Maria      | Gonzales    | Tesla           | Marketing Director |               | linkedin    | 2024-01-18        | 92         | interested | p.johannson |
| <EMAIL>  | Alex       | Chen        | Microsoft       |                    | NULL          | email       | 2024-01-20        | 78         | new        | e.bowen     |
| <EMAIL>     | Diana      | Clark       | Adobe           |                    | NULL          | email       | 2024-01-23        | 65         | contacted  | p.johannson |

_Output_
```json
{{
  "thought": "The tables represent leads from different sources, so the new column should indicate the source. Given the slang of 'opps', a good name could be 'Opportunities'. Since the MS Dynamics table is longer, it should go first. Since that will be the main table, the column naming should be lower case.",
  "column": "source",
  "table": "Opportunities"
}}
```
---
Now it's your turn! Please think about what each table represents and generate a thought followed by a JSON-formatted output to indicate the column and joint table name.

_Conversation History_
{history}

_Supporting Details_
{table_details}

_Output_
"""

append_rows_prompt = """As seen in the conversation history, the user is trying to append rows from one table to another.
Given the table details, your job is to decide which table should be appended to which.
The table details includes the table name, the number of rows, and a preview of the first few rows in markdown format.
In particular, we define:
  * source - the table where the data comes from, and will be removed afterwards
  * target - the table where the data is appended to

In general, the shorter table with fewer rows should serve as the source, while the longer table should be the target.
This behavior may be overriden when the contents of the tables imply a different arrangement.
Please start by examining the sampled data from each table, and think carefully about how they may be combined.
Your entire response should be in well-formatted JSON including your thoughts, source, and target tables; with no further explanations after the JSON output.

For example,
#############
_Conversation History_
User: Oh wait, I see there's actually multiple tables with customer support tickets.
Agent: Yes, which one would you like to use?
User: We should connect the latest tickets to all the previous ones before calculating the average response time.

_Table Details_
Table1: customer_service_requests (6014 rows)
| request_id | customer_id | assigned_agent | priority_level | request_status | creation_date | response_time | issue_type         | resolution_date | csat_score | request_source |
|------------|-------------|----------------|----------------|----------------|---------------|---------------|--------------------|-----------------|------------|----------------|
| CSR-892    | CUST456     | sarah.k        | HIGH           | RESOLVED       | 2023-08-15    | 45            | Login Failed       | 2023-08-16      | 4.5        | email          |
| CSR-893    | CUST990     | mike.t         | MEDIUM         | RESOLVED       | 2023-08-15    | 180           | Billing Question   | 2023-08-17      | 3.0        | chat           |
| CSR-894    | CUST234     | NULL           | LOW            | CANCELLED      | 2023-08-16    | NULL          | Feature Request    | NULL            | NULL       | web            |
| CSR-895    | CUST567     | john.d         | HIGH           | RESOLVED       | 2023-08-16    | 15            | Service Outage     | 2023-08-16      | 5.0        | phone          |
| CSR-896    | CUST890     | sarah.k        | MEDIUM         | RESOLVED       | 2023-08-17    | 120           | Password Reset     | 2023-08-18      | 4.0        | email          |
| CSR-897    | CUST613     | mike.t         | urgent         | RESOLVED       | 2023-08-17    | 30            | Payment Issue      | 2023-08-17      | 2.5        | chat           |
| CSR-898    | CUST345     | john.d         | LOW            | OPEN           | 2023-08-18    | 240           | Account Question   | NULL            | NULL       | web            |
| CSR-899    | CUST227     | sarah.k        | MEDIUM         | RESOLVED       | 2023-08-18    | 60            | Upgrade Request    | 2023-08-19      | 4.5        | phone          |

Table2: support_desk_tickets (162 rows)
| ticketID   | customerID  | agentAssigned  | priorityLevel  | ticketStatus  | dateCreated   | timeToRespond | typeOfIssue        | dateResolved    | custSatisfaction | ticketSource  |
|------------|-------------|----------------|----------------|---------------|---------------|---------------|--------------------|-----------------|------------------|---------------|
| 0923314    | CUST901     | alex.m         | High           | Open          | 2024-01-15    | 25            | Login Issue        | <N/A>           | <N/A>            | web           |
| 0923315    | CUST234     | emma.r         | Medium         | In Progress   | 2024-01-15    | 40            | Payment Issue      | <N/A>           | <N/A>            | email         |
| 0923316    | CUST567     | david.p        | Low            | Resolved      | 2024-01-16    | 120           | Feature Request    | 2024-01-18      | 4.0              | chat          |
| 0923317    | CUST890     | <N/A>          | High           | Pending       | 2024-01-16    | <N/A>         | Service Down       | <N/A>           | <N/A>            | phone         |
| 0923318    | CUST123     | alex.m         | urgent         | Resolved      | 2024-01-17    | 15            | Security Alert     | 2024-01-17      | 5.0              | web           |
| 0923319    | CUST456     | emma.r         | Medium         | Open          | 2024-01-17    | 55            | Account Access     | <N/A>           | <N/A>            | email         |
| 0923320    | CUST789     | david.p        | Low            | Resolved      | 2024-01-18    | 180           | Password Reset     | 2024-01-20      | 3.5              | chat          |
| 0923321    | CUST012     | alex.m         | Medium         | In Progress   | 2024-01-18    | 90            | Payment Issue      | <N/A>           | <N/A>            | web           |

_Output_
```json
{{
  "thought": "I cannot discern any special pattern from the content, so I will default to setting the shorter support desk table as the source.",
  "source": "support_desk_tickets",
  "target": "customer_service_requests"
}}
```

_Conversation History_
User: We can delete the predicted clicks and location related columns from the meta ads table.
Agent: No problem, I have removed the columns for you.
User: Great, now let's merge those metrics into FB insights

_Table Details_
Table1: Meta Ads Final (22 rows)
| startDate  | endDate    | campaignName      | campaignBudget | campaignSpend | campaignReach | campaignImpressions | campaignClicks | campaignConversions | predictedROAS |
|------------|------------|-------------------|----------------|---------------|---------------|---------------------|----------------|---------------------|---------------|
| 2024-01-02 | 2024-01-08 | Winter_Sale_2024  | 5000.00        | 4107.32       | 45892         | 98654               | 3421           | 89                  | 2.34          |
| 2024-01-09 | 2024-01-15 | NewYear_Promo     | 3500.00        | 3500.00       | 32445         | 67832               | 2876           | 65                  | 1.86          |
| 2024-01-16 | 2024-01-22 | Product_Launch_24 | 7500.00        | 6998.45       | 0             | 145632              | 5534           | 167                 | 3.21          |
| 2024-01-23 | 2024-01-29 | Flash_Deal_Jan    | 2000.00        | 1276.23       | 28776         | 54387               | 1987           | 45                  | 1.92          |
| 2024-01-30 | 2024-02-05 | Feb_Welcome       | 4000.00        | 4011.67       | 56734         | 123456              | 4532           | 123                 | 2.78          |
| 2024-02-06 | 2024-02-12 | ValentineDay_24   | 6000.00        | 5887.34       | 67823         | 156732              | 6754           | 0                   | 0             |
| 2024-02-13 | 2024-02-19 | CrossSell_Q1      | 3000.00        | 3298.87       | 34521         | 78654               | 2987           | 78                  | 2.12          |
| 2024-02-20 | 2024-02-26 | Retargeting_Feb   | 4500.00        | 4323.56       | 45678         | 98765               | 3876           | 98                  | 2.45          |

Table2: FB Insights (63 rows)
| starting_date | ending_date | campaign_name     | campaign_budget | campaign_spend | page_views | impressions | clicks | conversions | projected_profit |
|---------------|-------------|-------------------|-----------------|----------------|------------|-------------|--------|-------------|------------------|
| 2023-09-05    | 2023-09-11  | Fall_Collection   | 4000.00         | 3876.54        | 34567      | 87654       | 2345   | 56          | 1.87             |
| 2023-09-12    | 2023-09-18  | BackToSchool      | 5500.00         | 5498.76        | 45678      | 98765       | 3456   | 87          | 2.13             |
| 2023-09-19    | 2023-09-25  | EarlyBird_Fall    | 3000.00         | 2987.65        | 0          | 67543       | 1876   | 34          | 1.45             |
| 2023-09-26    | 2023-10-02  | October_Preview   | 6000.00         | 5876.43        | 56789      | 123456      | 4567   | 132         | 2.87             |
| 2023-10-03    | 2023-10-09  | Halloween_Promo   | 4500.00         | 4033.76        | 43567      | 89765       | 3234   | 0           | 0                |
| 2023-10-10    | 2023-10-16  | Fall_Sale_23      | 7000.00         | 7198.87        | 67890      | 145678      | 5678   | 165         | 3.12             |
| 2023-10-17    | 2023-10-23  | MidSeason_Sale    | 3500.00         | 3456.78        | 32456      | 76543       | 2345   | 54          | 1.76             |
| 2023-10-24    | 2023-10-30  | Halloween_Last    | 5000.00         | 5187.65        | 54678      | 112345      | 4123   | 123         | 2.65             |

_Output_
```json
{{
  "thought": "Looking at the data, we can tell that the tables are ordered by date from oldest to newest. Since the Meta Ads activity is more recent, it should be appended to bottom of FB Insights.",
  "source": "Meta Ads Final",
  "target": "FB Insights"
}}
```

_Conversation History_
User: Now let's add a column which signifies whenever the number of clicks is greater than 1000.
Agent: Sure, do you know what you would like to name this column?
User: We can call it 'high_clicks'
Agent: OK, I have added the column.
User: I'd like to append the paid data to the social media report now.

_Table Details_
Table1: Weekly Report (social) (19 rows)
report_date, total_engagement, total_impressions, total_clicks, high_clicks, platform_spend, conversion_rate, follower_growth, content_count

| report_date | total_engagement | total_impressions | total_clicks | high_clicks | platform_spend | conversion_rate | follower_growth | content_count |
|-------------|------------------|-------------------|--------------|-------------|----------------|-----------------|-----------------|---------------|
| 2025-09-01  | 15432            | 89765             | 2345         | true        | 875.43         | 2.34            | 234             | 12            |
| 2025-09-08  | 12567            | 76543             | 1876         | true        | 654.32         | 1.98            | 156             | 8             |
| 2025-09-15  | 9876             | 54321             | 987          | false       | 432.10         | 2.45            | 98              | 15            |
| 2025-10-22  | 18765            | 98234             | 3456         | true        | 987.65         | 3.21            | 345             | 18            |
| 2025-10-29  | 8765             | 45678             | 876          | false       | 345.67         | 1.87            | NULL            | 7             |
| 2025-12-05  | 13456            | 87654             | 1567         | true        | 765.43         | 2.76            | 167             | 14            |
| 2025-08-12  | NULL             | 65432             | 1234         | true        | 543.21         | NULL            | 145             | 11            |
| 2025-08-19  | 11234            | 67890             | 1789         | true        | 678.90         | 2.12            | 189             | 16            |

Table2: Weekly Report (paid) (23 rows)
| report_date | total_engagement | total_impressions | total_clicks | platform_spend | conversion_rate | follower_growth | content_count |
|-------------|------------------|-------------------|--------------|----------------|-----------------|-----------------|---------------|
| 2025-12-04  | 23456            | 156789            | 4567         | 1234.56        | 3.45            | 456             | 23            |
| 2025-12-11  | 19876            | 134567            | 3987         | 987.65         | 2.98            | 345             | 19            |
| 2025-10-18  | 15678            | 98765             | 2876         | 876.54         | 2.65            | 234             | 15            |
| 2025-12-25  | NULL             | 78654             | 1987         | 654.32         | NULL            | NULL            | 12            |
| 2025-07-01  | 21345            | 145678            | 4123         | 1098.76        | 3.21            | 398             | 21            |
| 2025-07-08  | 18765            | 123456            | 3567         | 987.65         | 2.87            | 287             | 18            |
| 2025-11-15  | 16543            | 112345            | 2987         | 876.54         | 2.54            | 245             | 16            |
| 2025-07-22  | 14321            | 98765             | 2345         | 765.43         | 2.32            | 198             | 14            |

_Output_
```json
{{
  "thought": "Even though the social data is shorter, the user explicitly designated it as the target table.",
  "source": "Weekly Report (paid)",
  "target": "Weekly Report (social)"
}}
```

_Conversation History_
User: I need to full list of emails so I can blast all them with our latest offer, can you pull that for me?
Agent: There are multiple tables with email addresses, which one would you like?
User: Ok, let's start by extending the CRM with the latest batch of leads

_Table Details_
Table: cold_outreach_opportunities (1805 rows)
| email_address            | first_name | last_name   | company_name    | job_title          | phone_number  | lead_source | last_contact_date | lead_score | status     | lead_owner  |
|--------------------------|------------|-------------|-----------------|--------------------|---------------|-------------|-------------------|------------|------------|-------------|
| <EMAIL>       | Peter      | Chan        | Zoom            | Product Manager    | ************  | website     | 2024-01-15        | 85         | contacted  | p.johannson |
| <EMAIL>      | Mary       | Chen        | Tesla           | Marketing Director | ************  | linkedin    | 2024-01-18        | 92         | interested | m.wilson    |
| <EMAIL>  | Alex       | Chen        | Microsoft       | Software Engineer  | ************  | referral    | 2024-01-20        | 78         | new        | e.bowen     |
| <EMAIL>     | Diana      | Clark       | Adobe           | Technical Lead     | NULL          | webinar     | 2024-01-23        | 65         | contacted  | p.johannson |
| <EMAIL>   | James      | Cohen       | Salesforce      | Account Executive  | 206-555-0369  | linkedin    | 2024-01-25        | 88         | interested | m.wilson    |
| <EMAIL>   | Lisa       | Cooper      | Netflix         | Program Manager    | ************  | website     | NULL              | 72         | new        | e.bowen     |
| <EMAIL>       | Kevin      | Cox         | Meta            | Senior Developer   | ************  | referral    | 2024-01-28        | 95         | contacted  | p.johannson |

Table2: qualified_leads (336 rows)
| Email_Address           | First_Name | Last_Name   | Company_Name  | Job_Title             | Phone_Number  | Lead_Source | Last_Contact_Date | Lead_Score | Status     | Lead_Owner  |
|-------------------------|------------|-------------|---------------|-----------------------|---------------|-------------|-------------------|------------|------------|-------------|
| <EMAIL>      | Tom        | Reed        | Amazon        | Product Director      | ************  | Website     | 2024-02-01        | 82         | New        | p.johannson |
| <EMAIL> | Jenny      | Richards    | Uber          | Business Analyst      | ************  | LinkedIn    | 2024-02-03        | 75         | Contacted  | m.wilson    |
| <EMAIL>  | Mike       | Roberts     | Apple         | Engineering Manager   | ************  | Conference  | 2024-02-05        | 90         | Interested | e.bowen     |
| <EMAIL>   | Sarah      | Shaw        | Google        | VP of Sales           | NULL          | Webinar     | 2024-02-07        | NULL       | Pending    | p.johannson |
| <EMAIL>   | David      | Smith       | Oracle        | Product Manager       | 206-555-3456  | Referral    | NULL              | 88         | New        | p.johannson |
| <EMAIL>     | Amy        | Smith       | Cisco         | Marketing Manager     | ************  | Website     | 2024-02-10        | 95         | Interested | p.johannson |
| <EMAIL>   | Chris      | Stewart     | Dell          | Technical Director    | ************  | LinkedIn    | 2024-02-12        | 70         | Contacted  | e.bowen     |
| <EMAIL>  | Karen      | Sullivan    | IBM           | Senior Data Scientist | ************  | Conference  | 2024-02-15        | 85         | New        | l.lyles     |

_Output_
```json
{{
  "thought": "By observing the data, we can tell that the tables are ordered by customer last name. Since the names in the opportunities table are later in the alphabet, it should be appended to the bottom of the leads table.",
  "source": "cold_outreach_opportunities",
  "target": "qualified_leads"
}}
```
#############
Now it's your turn! Please decide which of the two tables should be the source and which should be the target.

_Conversation History_
{history}

_Table Details_
Table: {table1} ({rows1} rows)
{samples1}

Table: {table2} ({rows2} rows)
{samples2}

_Output_
"""

merge_target_prompt = """We are trying to merge some columns together to create a new target column.
In particular, we will be merging the {columns} columns in the {table} table using the {merge_style} method.
Given the conversation history and supporting information, please generate a relevant name for the new target column.

_Conversation_
{history}

Your primary source of inspiration should be what the user has stated or implied in the conversation history (and not the source columns).
Just answer with the name of short and descriptive target column, without any extra text or explanations.

Target: """

name_staging_col_prompt = """Given the conversation history and supporting details, your task is to determine what staging column(s) to insert and how to populate its contents.
Inserting the column is a step within a larger plan to analyze the data, where the current step will be provided as part of the supporting details.
Other details include the current table, followed by a list of relevant column names, which should be useful context for formatting the new column.

Start by thinking about the reason for creating the new column and whether its clear how the column can be filled by a Pandas operation.
Based on this thought, generate an appropriate column name for insertion, along with the table in which it resides. When deciding on a name, consider the following:
  * What is the formatting convention based on the existing columns: underscores, spaces, or camelCase? lowercase or uppercase?
  * Do the other columns follow some sort of pattern? If so, what is the common theme? Perhaps, they tend to use abbreviations or acronyms?
  * Prefer intuitive names that hint at the column's purpose (ie. is_promising_lead) rather than literal names describing the contents (ie. score_greater_than_70).
  * If the user has declared a specific column name, then use that as the target. Otherwise, generate a new name matching the style of the existing columns.

If the target column is unclear, then just try your best at making an educated guess. If the target table is unclear, then you can default to 'current'.
Your entire response should be in well-formatted JSON, including keys for thought (string), table (string), and columns (list) with no further explanations after the JSON output.
In most cases, you will only be creating a single column, but the return format is a list to allow for multiple columns if necessary.

For example,
---
## Creating Customer Cohorts
_Conversation History_
User: As a part of the product team, I want to see how different groups of customers engage with product features over time.
Agent: No problem, how do you want to segment the customers? Also, how do you want to measure engagement?
User: Let's create monthly cohorts based on when they signed up, and then measure engagement by checking if they clicked on anything.

_Supporting Details_
Step in plan: Create a column for month by extracting it from signup_timestamp
Related columns: signup_time column in product_adoption_tracking

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses underscores and lowercase. To assign customers to monthly cohorts, a good name could be 'signup_month'.",
  "table": "product_adoption_tracking",
  "columns": ["signup_month"]
}}
```

## Calculating a Metric
_Conversation History_
User: The head of finance wants to know the lifetime value of our customers. Can you help me?
Agent: No problem, how do you want to calculate the lifetime value?
User: To keep it simple, let's assume that 5k runners are worth $200, 10k runners are worth $150, half marathon runners are worth $250, and full marathon runners are worth $400.

_Supporting Details_
Step in plan: Create separate LTV columns for each race type based on predefined values ($200, $150, $250, $400).
Related columns: EventsAttended column in RaceRegistration

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses capital letter and no spaces. We want columns for each product, so we can go with the event followed by LTV.",
  "table": "RaceRegistration",
  "columns": ["5k_LTV", "10k_LTV", "HalfMarathonLTV", "FullMarathonLTV"]
}}
```

## Interpolating Missing Values
_Conversation History_
User: Our customer data has inconsistent completion of city fields, causing errors in loyalty calculations. Fix this for us.
Agent: I noticed there are two types of city fields: primary and secondary, which one are you interested in? Also, how do you want to fill in the missing values?
User: Let's actually make a column, which is derived from the primary address. If primary is null, then use the secondary address city. If that is null, then use the state and zip to infer the city.
Agent: You got it. I will first check the primary and secondary address cities. Otherwise, I will check other rows that have the same state and zip code. Should I proceed?
User: Yea, go ahead.

_Supporting Details_
Step in plan: Create new column using primary address, falling back to secondary address when primary is null.
Related columns: Primary Address (City), Primary Address (State), Primary Address (Zip), Secondary Address (City), Secondary Address (State), and Secondary Address (Zip) column in Loyalty Enrichment

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses spaces and capitalization. A good name for the new column could be 'Inferred City'.",
  "table": "Loyalty Enrichment",
  "columns": ["Inferred City"]
}}
```

## Flagging Binary Values
_Conversation History_
User: The compliance team wants to know if any of the products at the distribution center may cause allergic reactions. Can you check?
Agent: Sure, is there a specific allergy you are referring to? Also, any ideas on which columns to check?
User: Yes, I am referring to nut allergies. You can check by looking through the ingredients list.

_Supporting Details_
Step in plan: Make nut allergy boolean column by checking for nut-related terms in the ingredients.
Related columns: Ingredients_List column in FoodComplianceReport

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses underscores and uppercase. An appropriate column name for a boolean column could be 'Contains_Nuts'.",
  "table": "SafetyComplianceReport",
  "columns": ["Contains_Nuts"]
}}
```

## Measuring Distances or Durations
_Conversation History_
User: I am planning a re-engagement campaign for dormant customers to tailor messaging and offers
Agent: It sounds like you want to identify dormant customers. How do you want to determine who is dormant?
User: Anyone who hasn't made a purchase in the last 6 months can be considered dormant.

_Supporting Details_
Step in plan: Calculate days since last purchase by subtracting lastPurchaseDate from current date.
Related columns: lastPurchaseDate columnin Members Download

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses camelCase and without spaces. We are measuring the number of days since the last purchase, so a good name could be 'daysSinceLastPurchase'.",
  "table": "Members Download",
  "columns": ["daysSinceLastPurchase"]
}}
```

## Segmenting Values into Buckets
_Conversation History_
User: Can you help me assess the health of the sales pipeline by checking the distribution of upcoming deals?
Agent: Great idea, this will help to prioritize which deals to focus on. How do you define if a deal is healthy? Also is there a time frame you want to focus on?
User: Let's use the following categories: small is anything under $10k, large is anything over $50k, and medium is anything in between.
Agent: No problem, I will run the analysis based on the deal value. However, I noticed there are many deals without any estimated value, what should I do there?
User: Let's just put them in a separate category called 'missing'.

_Supporting Details_
Step in plan: Create deal_size_category by mapping deal_value to predefined buckets of small, medium, large, and missing.
Related columns: deal value column in pipeline forecast

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses spaces and lowercase. We are segmenting deal values into size buckets, so a good name could be 'deal size'.",
  "table": "pipeline forecast",
  "columns": ["deal size"]
}}
```

## Normalizing Text
_Conversation History_
User: I am compiling info a for a whitepaper on employee behavior in the new administration for my consulting firm. However, there are so many different titles that are spelled slightly differently, such as Vice President, VP, and Vice Prez
Agent: No problem, how do you want to normalize the job titles?
User: Let's put them into one of these categories: Associate, Manager, Director, Vice President, or Executive

_Supporting Details_
Step in plan: Map various job title spellings to standardized_title categories.
Related columns: job_title column in Employee Org Chart

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses lowercase with underscores. We want to standardize jobs into 1 of 5 categories, so a good name could be 'standardized_title'.",
  "table": "Employee Org Chart",
  "columns": ["standardized_title"]
}}
```
---
## Current Scenario
Now, let's apply this logic to our current scenario. Please think about the reason for creating the new column and the format of the existing columns in the table.
When in conflict, the user utterance in the conversation history takes precedence over other supporting information. As reference for naming style, the other tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Supporting Details_
Step in plan: {plan}
Related columns: {related_cols}

_Output_
"""

complete_append_prompt = """Given the conversation history and supporting details, your job is to append data from the {source_tabs} tables together.
Occasionally, you should also insert a column to distinguish the data from each of the source tables, when such a column does not already exist.
In order to access the source dataframes, you can use the format `db.tab_name`, or alternatively `db['table name']` if the table name contains spaces.
To aid you in this task, the supporting details will provide the name of each table, followed by a preview of the first few rows in markdown format.
The supporting details will also include the name of the joint table{segment_desc}

Please write directly executable Python code, which may contain comments to help with reasoning. Your code should focus on:
  1. Aligning the column names within the sources tables so they match each other exactly, by renaming or adding columns
  2. Insert a column to distinguish the data from each of the source tables, if needed
  3. Append the data from the source tables together to form the joint table

Be sure to append the tables together in exactly the order they are listed in the supporting details.
When writing code, keep things simple! Do not create copies or add extra clean-up steps unless absolutely necessary.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

For example,
---
_Conversation History_
User: Can you delete all the rows with 0 clicks?
Agent: No problem, I have removed them. There are now 17 rows left for August.
User: OK, now let's join all the data together into one table.

_Supporting Details_
Table 1: august_emails (17 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Summer Sale               | 1500        | 351         | 254          | 20                 | 10            |
|  2        | Limited Time Offer        | 1800        | 402         | 301          | 25                 | 12            |
|  3        | Clearance Sale            | 1200        | 282         | 202          | 18                 | 8             |
|  4        | Perfect Beach Vacation    | 1100        | 254         | 184          | 15                 | 7             |

Table 2: september_emails (14 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Back to School Sale       | 1000        | 259         | 158          | 10                 | 5             |
|  2        | Fall Fashion Collection   | 1200        | 312         | 209          | 15                 | 7             |
|  3        | Holiday Special           | 800         | 188         | 120          | 8                  | 4             |
|  4        | Exclusive Offer           | 900         | 229         | 167          | 12                 | 6             |

Table 3: october_emails (16 rows)
| Email ID  | Campaign Name             | Total Sends | Total Opens | Total Clicks | Total Unsubscribes | Total Bounces |
|-----------|---------------------------|-------------|-------------|--------------|--------------------|---------------|
|  1        | Halloween Special         | 1300        | 300         | 229          | 15                 | 10            |
|  2        | Cyber Monday Sale         | 1100        | 275         | 182          | 12                 | 8             |
|  3        | Fall into Savings         | 1400        | 350         | 263          | 18                 | 12            |
|  4        | Black Friday Deal         | 1000        | 250         | 171          | 11                 | 7             |

Joint table: emails
Segmentation column: Month

_Output_
```python
# Insert a column to indicate the month of each table
db.august_emails['Month'] = 'August'
db.september_emails['Month'] = 'September'
db.october_emails['Month'] = 'October'
# Append the tables together
db.emails = pd.concat([db.august_emails, db.september_emails, db.october_emails], ignore_index=True)
```

_Conversation History_
User: Oh wait, I see there's actually multiple tables with customer support tickets.
Agent: Yes, which one would you like to use?
User: We should connect the latest tickets to all the previous ones before calculating the average response time.

_Table Details_
Table 1: customer_service_requests (1360 rows)
| request_id | customer_id | assigned_agent | priority_level | request_status | creation_date | response_time | issue_type         | resolution_date | csat_score | request_source |
|------------|-------------|----------------|----------------|----------------|---------------|---------------|--------------------|-----------------|------------|----------------|
| CSR-892    | CUST456     | sarah.k        | HIGH           | RESOLVED       | 2023-08-17    | 45            | Login Failed       | 2023-08-16      | 4.5        | email          |
| CSR-893    | CUST990     | mike.t         | MEDIUM         | RESOLVED       | 2023-08-15    | 180           | Billing Question   | 2023-08-17      | 3.0        | chat           |
| CSR-894    | CUST234     | NULL           | LOW            | CANCELLED      | 2023-08-16    | NULL          | Feature Request    | NULL            | NULL       | web            |
| CSR-895    | CUST567     | john.d         | HIGH           | RESOLVED       | 2023-08-16    | 15            | Service Outage     | 2023-08-16      | 5.0        | phone          |

Table 2: support_desk_tickets (407 rows)
| ticket_id  | customer_id | agent_assigned | priority_level | ticket_status | date_created  | time_to_respond | type_of_issue      | date_resolved   | cust_satisfaction | ticket_source |
|------------|-------------|----------------|----------------|---------------|---------------|-----------------|--------------------|-----------------|-------------------|---------------|
| 0923314    | CUST901     | alex.m         | High           | Open          | 2023-08-19    | 25              | Login Issue        | <N/A>           | <N/A>             | web           |
| 0923315    | CUST234     | emma.r         | Medium         | In Progress   | 2023-08-12    | 40              | Payment Issue      | <N/A>           | <N/A>             | email         |
| 0923316    | CUST567     | david.p        | Low            | Resolved      | 2023-08-16    | 120             | Feature Request    | 2023-01-18      | 4.0               | chat          |
| 0923317    | CUST890     | <N/A>          | High           | Pending       | 2023-08-18    | <N/A>           | Service Down       | <N/A>           | <N/A>             | phone         |

Joint table: customer_service_requests
Segmentation column: source

_Output_
```python
# Despite occassionally reversing the token order, the column names are aligned, so we can perform a simple rename
support_columns = list(db.support_desk_tickets.columns)
csr_columns = list(db.customer_service_requests.columns)
column_mapping = {{ source_col: target_col for source_col, target_col in zip(support_columns, csr_columns) }}
db.support_desk_tickets.rename(columns=column_mapping, inplace=True)
# Create a column to indicate the source of each table
db.support_desk_tickets['source'] = 'support_desk'
db.customer_service_requests['source'] = 'customer_service'
# Append the tables together and set it back to the original table
db.customer_service_requests = pd.concat([db.customer_service_requests, db.support_desk_tickets], ignore_index=True)
```

_Conversation History_
User: let's also drop any rows where the EBITDA is empty
Agent: You got it, I have removed all rows with null values in the EBITDA column.
User: Can you append the one from last year to the one from this year?

_Supporting Details_
Table 1: CONV ANNUAL REVIEW (12 rows)
| Month    | Revenue  | COGS ($) | Gross Profit | Operating Expenses | EBITDA ($) | Depreciation ($) | Interest ($) | Taxes ($) | Net Income (000s) | Profit Margin (%) |
|:---------|----------|----------|--------------|--------------------|------------|------------------|--------------|-----------|-------------------|------------------:|
| January  | 245,000  | 98,000   | 147,000      | 85,000             | 62,000     | 8,500            | 3,200        | 12,575    | 37,725            | 15.4              |
| February | 268,500  | 107,400  | 161,100      | 88,500             | 72,600     | 8,500            | 3,100        | 15,250    | 45,750            | 17.0              |
| March    | 298,500  | 119,400  | 179,100      | 91,500             | 87,600     | 8,500            | 2,950        | 19,038    | 57,113            | 19.1              |
| April    | 325,000  | 130,000  | 195,000      | 93,500             | 101,500    | 8,500            | 2,850        | 22,538    | 67,613            | 20.8              |

Table 2: CONV 2024 YTD (5 rows)
| Month    | Revenue  | COGS     | Gross Profit | Operating Expenses | EBITDA   | Depreciation | Interest | Taxes  | Net Income | Profit Margin |
|:---------|----------|----------|--------------|--------------------|----------|--------------|----------|--------|------------|--------------:|
| January  | 287,500  | 115,000  | 172,500      | 92,000             | 80,500   | 9,200        | 2,800    | 17,125 | 51,375     | 17.9          |
| February | 342,000  | 136,800  | 205,200      | 96,500             | 108,700  | 9,200        | 2,650    | 24,213 | 72,638     | 21.2          |
| March    | 356,500  | 142,600  | 213,900      | 97,000             | 116,900  | 9,200        | 2,700    | 26,250 | 78,750     | 22.1          |
| April    | 378,000  | 151,200  | 226,800      | 98,000             | 128,800  | 9,200        | 2,600    | 29,250 | 87,750     | 23.2          |

Joint table: CONV FINANCIALS
Segmentation column: Year

_Output_
```python
# Update the Annual Review columns to match the YTD table
annual_cols = list(db.['CONV ANNUAL REVIEW'].columns)
ytd_cols = list(db.['CONV YTD'].columns)
col_mapping = {{source_col: target_col for source_col, target_col in zip(annual_cols, ytd_cols)}}
db.['CONV ANNUAL REVIEW'].rename(columns=col_mapping, inplace=True)
# Insert a column to indicate the year of each table
db.['CONV ANNUAL REVIEW']['Year'] = '2023'
db.['CONV YTD']['Year'] = '2024'
# Append the tables together
db.['CONV FINANCIALS'] = pd.concat([db.['CONV ANNUAL REVIEW'], db.['CONV YTD']], ignore_index=True)
```

_Conversation History_
User: So I just uploaded the data from the AI Developer Conference
Agent: Yes, I see that. What would you like to do with it?
User: I'd like to combine all the data from each day into one table. Is that possible?

_Supporting Details_
Table 1: GPU_Computing_Day1 (192 rows)
| Name               | Company          | Role           | Email Address           | Interest Level | Main Product Line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Priya Sharma       | NeuralTech       | GPU Architect  | <EMAIL>  | High           | Cost optimization           |
| David Chen         | ByteWorks        | CIO            | <EMAIL> |                | Workflow integration        |
| Lisa Taylor        | Insight AI       | ML Engineer    | <EMAIL>  |                | Compute resources           |
| Michael Brown      | N/A              | Data Manager   | <EMAIL>  |                | Production deployment tools |
| Olivia Garcia      | DataSphere       | ML Operations  | <EMAIL>   | High           | Cost optimization           |

Table 2: Model_Evaluation_Day2 (211 rows)
| name               | company          | role           | email_address           | interest_level | main_product_line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Ming Yang          | ByteDance        | Researcher     | <EMAIL> | High           | Compute resources           |
| Kevin Sanders      | MultiOne         | ML Engineer    | <EMAIL>   | High           | Cost optimization           |
| Denise Sims        | Quadratic AI     | ML Consultant  | <EMAIL>     | Medium         | Workflow integration        |
| Tyrone Hill        | N/A              | Engineer       | <EMAIL>     |                | Just exploring options      |
| Andrea Madotto     | Vazy Data        | AI Engineer    | <EMAIL>   | Low            | Just exploring options      |

Table 3: ML_Ops_Day3 (138 rows)
| Name               | Company          | Role           | Email Address           | Interest Level | Main Product Line           |
|--------------------|------------------|----------------|-------------------------|--------------- |-----------------------------|
| Alex Johnson       | techscale        | ML Engineer    | <EMAIL>    | High           | Production deployment tools |
| Maya Patel         | DataStream       | CTO            | <EMAIL>    | Medium         | Workflow integration        |
| Chris Wong         | Self-employed    | ML Consultant  | <EMAIL>  | High           | Workflow integration        |
| Sarah Miller       | CloudNine        | Data Scientist | <EMAIL> | Low            | Just exploring options      |
| James Rodriguez    | quantum analytics| Eng Manager    | <EMAIL>   | High           | Compute resources           |

Joint table: Conference_Prospects
Segmentation column: Day

_Output_
```python
# Convert columns from Day 2 to match Day 1
db.Model_Evaluation_Day2.rename(columns=lambda x: x.replace('_', ' ').title(), inplace=True)
# Insert a column to indicate the day of each table
db.GPU_Computing_Day1['Day'] = '1'
db.Model_Evaluation_Day2['Day'] = '2'
db.ML_Ops_Day3['Day'] = '3'
# Append the tables together
db.Conference_Prospects = pd.concat([db.GPU_Computing_Day1, db.Model_Evaluation_Day2, db.ML_Ops_Day3], ignore_index=True)
```

_Conversation History_
User: Now let's add a column which signifies whenever the number of clicks is greater than 1000.
Agent: Sure, do you know what you would like to name this column?
User: We can call it 'high_clicks'
Agent: OK, I have added the column.
User: I'd like to append the paid data to the social media report now.

_Table Details_
Table 1: PaidWeeklyReport
| report_date | total_engagement | total_impressions | total_clicks | platform_spend | conversion_rate | follower_growth | content_count |
|-------------|------------------|-------------------|--------------|----------------|-----------------|-----------------|---------------|
| 2025-12-04  | 23456            | 156789            | 4567         | 1234.56        | 3.45            | 456             | 23            |
| 2025-12-11  | 19876            | 134567            | 3987         | 987.65         | 2.98            | 345             | 19            |
| 2025-10-18  | 15678            | 98765             | 2876         | 876.54         | 2.65            | 234             | 15            |
| 2025-12-25  | NULL             | 78654             | 1987         | 654.32         | NULL            | NULL            | 12            |

Table 2: SocialWeeklyReport
| report_date | total_engagement | total_impressions | total_clicks | high_clicks | platform_spend | conversion_rate | follower_growth | content_count |
|-------------|------------------|-------------------|--------------|-------------|----------------|-----------------|-----------------|---------------|
| 2025-09-01  | 15432            | 89765             | 2345         | true        | 875.43         | 2.34            | 234             | 12            |
| 2025-09-08  | 12567            | 76543             | 1876         | true        | 654.32         | 1.98            | 156             | 8             |
| 2025-09-15  | 9876             | 54321             | 987          | false       | 432.10         | 2.45            | 98              | 15            |
| 2025-10-22  | 18765            | 98234             | 3456         | true        | 987.65         | 3.21            | 345             | 18            |

Joint table: WeeklyReport
Segmentation column: channel

_Output_
```python
# Add the high clicks column to the paid report to match the social report
db.PaidWeeklyReport['high_clicks'] = db.PaidWeeklyReport['total_clicks'] > 1000
# Insert a column to indicate the channel of each table
db.PaidWeeklyReport['channel'] = 'paid'
db.SocialWeeklyReport['channel'] = 'social'
# Append the tables together
db.WeeklyReport = pd.concat([db.PaidWeeklyReport, db.SocialWeeklyReport], ignore_index=True)
```
---
Now it's your turn! Please output executable Python code with inline comments to align the table columns, and add a segmentation column if necessary. Then, append the tables together.

_Conversation History_
{history}

_Supporting Details_
{table_details}

Joint table: {joint_tab}
Segmentation column: {segment_col}

_Output_
"""


insert_staging_prompt = """Given the proposed plan and supporting details, follow the current step in the plan to insert or add data using Pandas code.
Supporting details include the name of the column(s) to add, as well as current step that describes how we will populate the column.
For the final example, you will also be given the first few rows of the table to help you understand the data.
The existing dataframes you have are {df_tables}.

Focus on writing directly executable Python, which may contain comments to help with reasoning. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

For example,
---
## Creating Customer Cohorts
_Conversation History_
User: As a part of the product team, I want to see how different groups of customers engage with product features over time
Agent: No problem, how do you want to segment the customers?
User: Let's create monthly cohorts based on when they signed up

_Supporting Details_
Step in plan:
transform: Create signup_cohort_month column by extracting year-month from signup_timestamp.
query: Group customers by signup_cohort_month and analyze feature engagement over time.
Related columns: signup_time in product_adoption_tracking

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses underscores and lowercase. Since the goal is to assign customers to monthly cohorts, a good name could be 'signup_month'.",
  "table": "product_adoption_tracking",
  "columns": ["signup_month"]
}}
```

## Calculating a Metric
_Conversation History_
User: As part of the finance team, I want to evaluate the lifetime value of customers for each of the different product offerings
Agent: No problem, how do you want to calculate the lifetime value?
User: To keep it simple, assume that 5k runners are worth $100, 10k runners are worth $200, half marathon runners are worth $300, and full marathon runners are worth $400

_Supporting Details_
Step in plan:
transform: Create separate LTV columns for each race type based on predefined values ($100, $200, $300, $400).
derive: Calculate total_event_ltv by summing the values from all race-specific LTV columns.
query: Group results by acquisition_channel to evaluate efficiency.
Related columns: PurchasedItems, AcquisitionChannel in RaceRegistrationSurvey

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses capital letter and no spaces. We are staging intermediate columns for each product's LTV, so good names could be '5k_LTV', '10k_LTV', 'HalfMarathonLTV', and 'FullMarathonLTV'.",
  "table": "RaceRegistrationSurvey",
  "columns": ["5k_LTV", "10k_LTV", "HalfMarathonLTV", "FullMarathonLTV"]
}}
```

## Interpolating Missing Values
_Conversation History_
  - Your customer data has inconsistent completion of "Primary Address (city)" fields, causing errors in loyalty calculations
  - By creating a staging column that replaces null cities with some reasonable default based on other values in the table
  - The supporting columns are "Primary Address (state)", "Primary Address (zip code)", "Secondary Address (city)"
  - If the secondary address city is not null, then use that as the default
  - Otherwise, if the zip code is not null, look at other rows with the same state and zip code to infer the city

_Supporting Details_
Step in plan:
peek: Analyze format of zip codes to determine how to match them properly.
transform: Create inferred_primary_city column using secondary_address_city where primary is null.
query: For remaining nulls, find most common city for each zip and state combination.
transform: Update remaining nulls with inferred cities and track the data source.
Related columns: Primary Address (City), Primary Address (State), Primary Address (Zip), Secondary Address (City), Secondary Address (State), Secondary Address (Zip) in Loyalty Enrichment

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses spaces and capitalization. A good name for the new column could be 'Inferred City'.",
  "table": "Loyalty Enrichment",
  "columns": ["Inferred City"]
}}
```

## Flagging Binary Values
_Conversation History_
User: As a distribution center insurance team, I need to know whether any of our products may cause allergic reactions
Agent: Sure, is there a specific allergy you are referring to?
User: Yes, I am referring to nut allergies.

_Supporting Details_
Step in plan:
peek: Examine customer_allergies values to identify various terms referring to nut allergies.
transform: Create has_nut_allergy boolean column by checking for nut-related terms.
query: Segment customers by plan_tier and has_nut_allergy to identify high-risk accounts.
Related columns: Ingredients_List in FoodComplianceReport

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses underscores and uppercase. We are tracking whether customers have a nut allergy, so a good name could be 'Contains_Nuts'.",
  "table": "SafetyComplianceReport",
  "columns": ["Contains_Nuts"]
}}
```

## Measuring Distances or Durations
_Conversation History_
User: I am planning a re-engagement campaign for dormant customers to tailor messaging and offers
Agent: No problem, how do you want to measure the duration?
User: I want to measure the number of days since their last purchase

_Supporting Details_
Step in plan:
transform: Calculate days_since_last_purchase from last_purchase_date to current date.
transform: Assign customers to recency_bucket categories based on days_since_last_purchase.
query: Group customers by recency_bucket for re-engagement campaign planning.
Related columns: lastPurchaseDate in Members Download

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses camelCase and without spaces. We are calculating the number of days since the last purchase, so a good name could be 'daysSinceLastPurchase'.",
  "table": "Members Download",
  "columns": ["daysSinceLastPurchase"]
}}
```

## Segmenting Values into Buckets
_Conversation History_
User: As a part of the sales team, I want to understand the distribution of upcoming deals to assess pipeline health
Agent: Great idea, this will help to prioritize which deals to focus on. How do you want to segment the deals?
User: Let's use the following categories: small ($0-$10k), medium ($10k-$50k), large ($50k+), and missing (null values)

_Supporting Details_
Step in plan:
transform: Create deal_size_category by mapping deal_value to predefined buckets.
query: Group and analyze pipeline by deal_size_category.
Related columns: deal value in pipeline forecast

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses spaces and lowercase. We are segmenting deal sizes into buckets, so a good name could be 'deal size'.",
  "table": "pipeline forecast",
  "columns": ["deal size"]
}}
```

## Normalizing Text
_Conversation History_
User: I am compiling info a for a whitepaper on employee behavior in the new administration for my consulting firm. However, there are so many different titles that are spelled slightly differently, such as Vice President, VP, and Vice Prez
Agent: No problem, how do you want to normalize the job titles?
User: Let's put them into one of these categories: Associate, Manager, Director, Vice President, or Executive

_Supporting Details_
Step in plan:
peek: Examine job_title variations to build a comprehensive mapping dictionary.
transform: Map various job title spellings to standardized_title categories.
query: Group employee data by standardized_title for analysis.
Related columns: job_title in Employee Org Chart

_Output_
```json
{{
  "thought": "Based on the existing columns, the format uses lowercase with underscores. The goal is to standardize jobs into 5 categories, so a good name could be 'standardized_title'.",
  "table": "Employee Org Chart",
  "columns": ["standardized_title"]
}}
```
---
## Current Scenario
Now, let's apply this logic to our current scenario. Please think about the reason for creating the new column and the format of the existing columns in the table.
When in doubt, the step in the Conversation History takes precedence over any other supporting information. As reference for naming style, the other tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Supporting Details_
Step in plan: {plan}
Related columns: {prior_state}

_Output_
"""