#!/bin/bash
# <PERSON><PERSON>t to sync production database to staging

# Navigate to the directory containing the Python script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Set the path to your Python script - make sure this path is correct
PYTHON_SCRIPT="$SCRIPT_DIR/sync_prod_to_staging_weekly.py"

# Activate virtual environment if you're using one
# source /path/to/your/venv/bin/activate

# Run the Python script with -y flag to skip confirmation
python "$PYTHON_SCRIPT" -y

# Log the completion
echo "$(date): Database sync completed" >> "$SCRIPT_DIR/sync_logs.txt"