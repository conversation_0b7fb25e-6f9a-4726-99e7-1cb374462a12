# OAuth provider configurations
PROVIDER_CONFIG = {
  "drive": {
    "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
    "token_url": "https://oauth2.googleapis.com/token",
    "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
    "scopes": [
      "https://www.googleapis.com/auth/drive.readonly",
      "https://www.googleapis.com/auth/spreadsheets.readonly",
      "https://www.googleapis.com/auth/userinfo.email"
    ],
    "id_field": "id",
    "token_request": {
      "url": "https://oauth2.googleapis.com/token",
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  },
  "ga4": {
    "auth_url": "https://accounts.google.com/o/oauth2/auth",
    "token_url": "https://oauth2.googleapis.com/token",
    "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
    "scopes": [
      "https://www.googleapis.com/auth/analytics.readonly",
      "https://www.googleapis.com/auth/userinfo.email"
    ],
    "id_field": "id",
    "token_request": {
      "url": "https://oauth2.googleapis.com/token", 
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  },
  "google": {
    "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
    "token_url": "https://oauth2.googleapis.com/token",
    "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
    "scopes": [
      "https://www.googleapis.com/auth/adwords",
      "https://www.googleapis.com/auth/userinfo.email"
    ],
    "id_field": "id",
    "token_request": {
      "url": "https://oauth2.googleapis.com/token",
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  },
  "hubspot": {
    "auth_url": "https://app.hubspot.com/oauth/authorize",
    "token_url": "https://api.hubapi.com/oauth/v1/token",
    "user_info_url": "https://api.hubapi.com/oauth/v1/access-tokens/{access_token}",
    "scopes": [
      "crm.objects.contacts.read",
      "crm.objects.companies.read",
      "crm.objects.deals.read",
      "settings.users.read"
    ],
    "id_field": "user_id",
    "token_request": {
      "url": "https://api.hubapi.com/oauth/v1/token",
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  },
  "facebook": {
    "auth_url": "https://www.facebook.com/v18.0/dialog/oauth",
    "token_url": "https://graph.facebook.com/v18.0/oauth/access_token",
    "user_info_url": "https://graph.facebook.com/v18.0/me",
    "scopes": [
      "ads_read"
    ],
    "id_field": "id",
    "token_request": {
      "url": "https://graph.facebook.com/v18.0/oauth/access_token",
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  },
  "salesforce": {
    "auth_url": "https://login.salesforce.com/services/oauth2/authorize",
    "token_url": "https://login.salesforce.com/services/oauth2/token",
    "user_info_url": "https://login.salesforce.com/services/oauth2/userinfo",
    "scopes": [
      "api",
      "refresh_token"
    ],
    "id_field": "user_id",
    "token_request": {
      "url": "https://login.salesforce.com/services/oauth2/token",
      "headers": {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      "payload_template": {
        "grant_type": "authorization_code",
        "redirect_uri": "{redirect_uri}",
        "code": "{code}"
      }
    }
  }
}