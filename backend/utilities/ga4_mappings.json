{"DIMENSION_MAP": {"Achievement ID": "achievementId", "Ad format": "adFormat", "Ad source": "adSourceName", "Ad unit": "adUnitName", "App version": "appVersion", "Audience name": "audienceName", "Audience type": "audienceType", "Browser": "browser", "Campaign ID": "campaignId", "Campaign name": "campaignName", "City": "city", "City ID": "cityId", "Content ID": "contentId", "Content name": "contentName", "Country": "country", "Country ID": "countryId", "Currency": "currency", "Date": "date", "Day of week": "dayOfWeek", "Device category": "deviceCategory", "Event name": "eventName", "Event ID": "eventId", "Event label": "eventLabel", "Event parameter key": "eventParameterKey", "Event parameter value": "eventParameterValue", "Event source": "eventSource", "Hour": "hour", "Interests": "brandingInterest", "Item ID": "itemId", "Item name": "itemName", "Language": "language", "Minute": "minute", "Month": "month", "Operating system": "operatingSystem", "Operating system with version": "operatingSystemWithVersion", "Origin": "origin", "OS with version": "osWithVersion", "Platform": "platform", "Quarter": "quarter", "Region": "region", "Region ID": "regionId", "Screen class": "screenClass", "Screen name": "screenName", "Session count": "sessionCount", "Source": "source", "Source / Medium": "sourceMedium", "Sub-continent": "subContinent", "User engagement level": "userEngagementLevel", "User first touch timestamp": "userFirstTouchTimestamp", "User LTV revenue": "userLtvRevenue", "User LTV segment": "userLtvSegment", "User property": "userProperty", "User property value": "userPropertyValue", "User type": "userType", "Year": "year", "Year month": "yearMonth", "Year week": "yearWeek"}, "METRIC_MAP": {"1-day active users": "active1DayUsers", "28-day active users": "active28DayUsers", "7-day active users": "active7DayUsers", "Active users": "activeUsers", "Ad unit exposure": "adUnitExposure", "Ad unit exposure frequency": "adUnitExposureFrequency", "Ad unit exposure rate": "adUnitExposureRate", "Ad unit exposure time": "adUnitExposureTime", "Ad unit fill rate": "adUnitFillRate", "Ad unit impressions": "adUnitImpressions", "Ad unit impressions per session": "adUnitImpressionsPerSession", "Ad unit impressions rate": "adUnitImpressionsRate", "Ad unit impressions RPM": "adUnitImpressionsRpm", "Ad unit impressions RPM (hourly)": "adUnitImpressionsRpmHourly", "Ad unit impressions RPM (session)": "adUnitImpressionsRpmSession", "Ad unit impressions RPM (user)": "adUnitImpressionsRpmUser", "Ad unit match rate": "adUnitMatchRate", "Ad unit matched requests": "adUnitMatchedRequests", "Ad unit measured impressions": "adUnitMeasuredImpressions", "Ad unit measured impressions rate": "adUnitMeasuredImpressionsRate", "Ad unit revenue": "adUnitRevenue", "Ad unit revenue (hourly)": "adUnitRevenueHourly", "Ad unit revenue (session)": "adUnitRevenueSession", "Ad unit revenue (user)": "adUnitRevenueUser", "Ad unit show rate": "adUnitShowRate", "Ad unit viewability rate": "adUnitViewabilityRate", "Ad unit visible impressions": "adUnitVisibleImpressions", "Ad unit visible impressions rate": "adUnitVisibleImpressionsRate", "Audience active users": "audienceActiveUsers", "Audience overlap": "audienceOverlap", "Average user engagement": "averageUserEngagement", "Bounce rate": "bounceRate", "Click-through rate": "clickThroughRate", "Conversion rate": "conversionRate", "Custom event count": "customEventCount", "Custom event count per user": "customEventCountPerUser", "E-commerce conversion rate": "ecommerceConversionRate", "Event count": "eventCount", "Event count per user": "eventCountPerUser", "Event value": "eventValue", "Events per session": "eventsPerSession", "First session date": "firstSessionDate", "Goal value": "goalValue", "Item revenue": "itemRevenue", "LTV revenue": "ltvRevenue", "LTV revenue per user": "ltvRevenuePerUser", "New users": "newUsers", "Return on ad spend": "returnOnAdSpend", "Average session duration": "AverageSessionDuration", "Sessions": "sessions", "Sessions per user": "<PERSON><PERSON>er<PERSON>ser", "Transactions": "transactions", "Transactions per user": "transactionsPerUser", "User engagement": "userEngagement", "User engagement rate": "userEngagementRate", "Users": "users"}}