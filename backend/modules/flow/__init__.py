from backend.modules.flow.ext_flows import *
from backend.modules.flow.int_flows import *

flow_selection = {
  # Analyze Flows
  '001': Query<PERSON>low,     '01A': PivotFlow,    '002': MeasureFlow,   '02D': Segment<PERSON>low, '014': Des<PERSON><PERSON><PERSON>,
  '14C': Exist<PERSON><PERSON>,     '248': Inform<PERSON>low,   '268': <PERSON><PERSON><PERSON><PERSON>,
  # Visualize Flows
  '003': <PERSON>lot<PERSON><PERSON>,      '023': TrendFlow,    '038': Explain<PERSON>low,   '23D': Report<PERSON>low,  '38A': SaveFlow,
  '136': DesignFlow,    '13A': StyleFlow,
  # Clean Flows
  '006': UpdateFlow,    '36D': Validate<PERSON>low, '36F': FormatFlow,    '0BD': Pat<PERSON><PERSON><PERSON>, '068': PersistFlow,
  '06B': ImputeFlow,    '06E': DataTypeFlow, '06F': UndoFlow,      '7BD': Dedupe<PERSON><PERSON>,
  # Transform Flows
  '005': Insert<PERSON>low,    '007': Delete<PERSON>low,   '056': Transpose<PERSON><PERSON>, '057': Move<PERSON>low,    '5CD': <PERSON><PERSON><PERSON>,
  '05A': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,   '05B': <PERSON><PERSON><PERSON><PERSON><PERSON>,   '05C': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,  '456': Call<PERSON>IFlow, '58A': MaterializeFlow,
  # Detect Flows
  '46B': BlankFlow,     '46C': ConcernFlow,  '46D': ConnectFlow,   '46E': TypoFlow,    '46F': ProblemFlow,
  '468': ResolveFlow,   '146': InsightFlow,
  # Internal Flows
  '089': ThinkFlow,     '39B': PeekFlow,     '129': ComputeFlow,   '149': SearchFlow,  '19A': StageFlow,
  '489': ConsiderFlow,  '9DF': UncertainFlow
}