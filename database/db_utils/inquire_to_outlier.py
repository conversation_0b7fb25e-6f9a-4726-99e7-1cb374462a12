import json
import pdb

conversations = json.load(open('conversations.json', 'r'))
key_phrases = ["remove these rows", "outlier", "anomal", "date issue", "potential concern"]

for convo in conversations:
  has_inquire = False
  for turn in convo['turns']:
    if 'dax' in turn and turn['dax'] == '00F':
      has_inquire = True

  matched = False
  if has_inquire:
    for turn in convo['turns']:
      for kp in key_phrases:
        if kp in turn['text']:
          matched = True

      if 'dax' in turn and turn['dax'] == '00F' and matched:
        turn['intent'] = 'manipulate'
        turn['dact'] = 'analyze + clean'
        turn['dax'] = '057'
        print(turn['text'])

json.dump(conversations, open('conversations.json', 'w'), indent=4)