"""Adding theme column to the conversations

Revision ID: fa34dd62ea09
Revises: b089702fdfff
Create Date: 2024-07-02 10:43:17.179821

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa34dd62ea09'
down_revision = 'b089702fdfff'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('states',
    sa.Column('state_id', sa.Integer(), nullable=False),
    sa.Column('table_name', sa.String(), nullable=True),
    sa.Column('table_cols', sa.String(), nullable=True),
    sa.Column('campaigns', sa.String(), nullable=True),
    sa.Column('channels', sa.String(), nullable=True),
    sa.Column('metric', sa.Float(), nullable=True),
    sa.PrimaryKeyConstraint('state_id')
    )
    op.add_column('conversations', sa.Column('subject', sa.String(length=128), nullable=True))
    op.drop_index('conversations_ivfflat_idx', table_name='conversations')
    op.alter_column('credentials', 'status',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_index(op.f('ix_credentials_id'), 'credentials', ['id'], unique=False)
    op.create_foreign_key(None, 'credentials', 'users', ['user_id'], ['user_id'])
    op.alter_column('utterances', 'utt_type',
               existing_type=sa.VARCHAR(length=32),
               nullable=True)
    op.alter_column('utterances', 'convo_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('utterances', 'convo_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('utterances', 'utt_type',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.drop_constraint('credentials_user_id_fkey', 'credentials', type_='foreignkey')
    op.drop_index(op.f('ix_credentials_id'), table_name='credentials')
    op.alter_column('credentials', 'status',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.create_index('conversations_ivfflat_idx', 'conversations', ['short_embed'], unique=False)
    op.drop_column('conversations', 'subject')
    op.drop_table('states')
    # ### end Alembic commands ###
