## Local Dev Setup

### Prerequisites

set up Python=3.11 `brew install python@3.11`

and a [conda](https://docs.anaconda.com/free/anaconda/install/index.html) environment `brew install --cask anaconda`

and NPM `pip install npm lightgbm` or `brew install node`

### Setup

Please follow this guide to get your local development environment set up.
Start by updating shell environment variables and path with conda (for zsh and macos):

`/usr/local/anaconda3/bin/conda init zsh`

then you can actually create the python environment you need:

`conda create -n env311 python=3.11`

Download the Soleda repository from git, run

`brew install git`

`git clone https://github.com/derekchen14/soleda.git`

For the backend, run
`pip install -r requirements.txt`

`cd soleda/backend`
`brew install libomp` # can this be added to requirements.txt ?

For the frontend, run

`cd soleda/frontend`
`npm install` to get the dependencies.

If there is a version conflict, then re-run

`npm install --legacy-peer-deps`

### Starting the Servers

To run the webserver, make sure you activate the environment `conda activate env311`. First run: `uvicorn backend.webserver:app --reload` (at /soleda) for FastAPI.
Then in a separate tab, head to frontend/ and run `npm run dev -- --port 1414` (at /soleda/frontend) to get Svelte working.

Flags that are working include `--verbose` and `--debug`. Other options can be found in `utils/arguments.py`
It might be useful to add `stty erase ^H` to the terminal before starting the program in order to allow for backspace in the user input.

### Environment Variables

See [drive](https://docs.google.com/document/d/1K17xRBrxd-C7uPmx38KNY7BFq3mn12ZWxRaEFbiqck0/edit) for the full list of environment variables. Or follow the .env.example files.

#### LogReg

Download the LogReg expert from the Soleda Google Drive. Make sure it's saved to:

```
cd soleda
mkdir -p modeling/checkpoints/joint
 copy the model into `./modeling/checkpoints/joint/${LOGREG_MODEL}`
```

Update the backend environment variable `LOGREG_MODEL` to match.

#### ICL

Update the backend environment variable `OPENAI_API_KEY` to match your API key

#### Embed

This is only required if you setup local DB. The embeddings require setting up Postgres with pgvector. See the section on database setup for more details.

### Database Setup

The best way to setup the database is to connect to remove stagging database. If prefered, you can also choose between using locally installed Postgres or a Docker-compose instance of postgres. The Local postgres steps are more reliable but more complicated to setup.

#### Connect to Remote DB

Simply make sure your /database/.env file is properly setup from previous step.

#### Local postgres (Brew)

**1. Install postgres**
To get embedding retrieval to work, you will need to install Postgres, along with PG-Vector
For more details, see the [website](https://github.com/pgvector/pgvector-python)
Install postgres with `brew install postgresql@14`. Install pgvector with `brew install pgvector`

To start the cluster, you run

`/opt/homebrew/opt/postgresql@14/bin/postgres -D /opt/homebrew/var/postgresql@14`

If that doesn't work, try this instead

`pg_ctl -D /usr/local/var/postgresql\@14/ -l logfile start`

**2. Set up user**
Afterwards, log into the default `D` database:

`psql -d postgres`

Create you custom user with:

`CREATE USER local_user WITH ENCRYPTED PASSWORD 'secret_postgres_password';`

Next, create the custom database with:

`CREATE DATABASE soleda_db;`

Followed by:

`GRANT ALL PRIVILEGES ON DATABASE soleda_db TO local_user;`

Connect to the soleda_db with

`\c soleda_db`

Enable the pg-vector extension in your database with

`CREATE EXTENSION vector;`

Grant the local_user permissions on the public (default) schema:

`GRANT ALL ON SCHEMA public TO local_user;`

**3. Alembic Migrations**
Next you should run the alembic migrations so that the tables are ready to take data from `build_postgres_seed.py`. To run the alembic migrations:

```
cd database
conda activate env311
pip install alembic python-dotenv psycopg2 pgvector
alembic upgrade head
```

**4. Build Postgres Seed**
Lastly, you will need to run `build_postgres_seed.py` in order to seed the database. You may need to get `database/annotations/conversations.json` from the Soleda Google Drive. This script will (a) populate tables and (b) populate conversations. Comment out the appropriate sections as needed.

```
# in soleda root dir, with conda environment active
pip install sentence_transformers textblob pandas bcrypt
python utils/scripts/build_postgres_seed.py
```

**5. Download NLTK Corpa**

```
conda activate env311
python -m textblob.download_corpora
```

**6. Verify setup**
When all this has been set-up, you can use `psql -U local_user -d soleda_db` in the future to login.
You can see how many rows are in a table with `SELECT COUNT(*) FROM conversations;` where in this case, the name of the table is "conversations". Other useful commands:
\dt - display tables
\d tablename - display columns of a table (or \d+)
TABLE tablename; - to show the contents of the table

If you need to start _everything_ over, you can run "armaggedon_and_rebirth" from build_postgres_seed.py, followed by running the migrations to create the tables. Then you can run the other two lines to once again populate the tables.
A more targeted approach uses: `TRUNCATE TABLE utterances;` and `TRUNCATE TABLE conversations CASCADE;` followed by migrations and just the single "populate_conversations" line.

#### Docker postgres

Run `docker compose up` or `make db`. This will start up a postgres instance with pgvector installed and enabled.

To use this method, you should update your database .env file, to be `DB_HOST=0.0.0.0`. You can verify the host with `docker ps`.

To view things locally: `psql -U ${user_name} -d ${db_name} -h localhost` or use [postico](https://eggerapps.at/postico2/)

After running `docker compose up`, you will finish your database setup by going to step #3 in the Local Postgres setup.

### Migration and seeding

If you installed your environment with virtualenv instead of anaconda, you can run the alembic migrations and build_postgres_seed.py with these commands instead. If you already ran Local Postgres steps 3 & 4, you don't need to run these.

1. Run `make db-migrate`
2. Run `make db-seed`

### New migrations

Run `make db-migrate-new` or execute `alembic revision -m "<num> migration message"` followed by upgrading head. New users do _not_ need to create migrations. Check existing migration files to see what is the latest version number.
If a migration run locally is incorrect, run `alembic downgrade -1` or `make db-downgrade`, make the change, then upgrade head again.

## Production and deployment

IMPORTANT: When making changes, modify the blueprint when possible instead of making changes through the render GUI. Thes allows for trackable and repeatable changes.

The exception to this is that secrets (e.g. API keys) _should not_ be handled via blueprints because they should not be stored in git. Those should be handled via the render GUI.

### Steps for setting up a fresh deploy

0. Copy [render.yaml](/render.yaml) into a new blueprint instance, making sure to change the names of the services. e.g. `production-backend` -> `development-backend`
1. Go to [render](https://dashboard.render.com/) and create a new blueprint instance based on the new blueprint and your branch of choice. This should trigger a deploy
2. Setup secrets for the remaining environment variables not specified in the blueprint. e.g. `OPENAI_API_KEY`, `REMOTE_CORE_PORT`
3. Setup `<new>-soleda-db` by setting your local DB_DSN to the external database URL provided for the newly created database. `make db-migrate` and `make db-seed`
4. Setup `<new>-backend` by following [the guide on render](https://render.com/docs/disks#scp) to transfer the LOGREG_MODEL to `/data/disk/${LOGREG_MODEL}`

## Tests

You can (and should) run the test suite with Dana. First, find the `run.sh` file in the root folder. Then, uncomment the set of tests to run: integration tests for the model or unit tests for the backend. Finally:

```
# With your python conda env activated
./run.sh
```

## FAQs

Q: I got an error about superuser access when creating an extension "vector".
A: run `\du` to see a list of users and the access level each user has. Then make sure you're using the database as the user with the correct access level.
`psql -U {user_name} -d soleda_db -h localhost`

Q: If got error about role "postgres" does not exist.
A: If you're using postgres from Homebrew, then first createuser by /opt/homebrew/Cellar/postgresql@14/14.9/bin/createuser -s postgres (for version 14.9)

Q: I'm on a M2 OSX macbook, and I got a psycopg error like: `ImportError: dlopen(/Users/<USER>/anaconda3/envs/soleda311/lib/python3.11/site-packages/psycopg2/_psycopg.cpython-311-darwin.so, 0x0002): symbol not found in flat namespace (_PQbackendPID)`
A: Uninstall psycopg and psycopg-binary (`pip uninstall psycopg psycopg-binary`) and install psycopg-binary with rebuild flags. [Stackoverflow post](https://stackoverflow.com/a/73739783):

Q: I got error "Repository not found." when running git clone.
A: It's likely you are not logged in with the correct user. Try to run "gh auth login"

```
ARCHFLAGS="-arch arm64" pip3 install psycopg2-binary --no-cache-dir --force-reinstall -v
```
