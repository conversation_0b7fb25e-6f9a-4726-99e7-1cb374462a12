<script lang="ts">
	export let className = 'inline-block align-middle';
</script>

<style>
  svg {
    color: green;
    position: absolute;
    right: 10px;
    top: 10px;
    display: inline-block;
  }
</style>

<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" 
  stroke-width="3" stroke="currentColor" class={`w-5 h-5 ${className}`}>
  <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
</svg>
