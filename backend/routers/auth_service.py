import os
import traceback
import json
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Di<PERSON>, <PERSON><PERSON>, Optional
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

import httpx
from fastapi import APIRouter, Depends, HTTPException, Query, Security, Body, Request
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.auth.JWT_helpers import JW<PERSON>Bearer, decode_JWT, get_token
from backend.db import get_db
from database.tables import Credential
from backend.utilities.oauth_setup import (create_oauth_url, create_resource_request, create_token_request)
from backend.utilities.oauth_config import PROVIDER_CONFIG
from backend.utilities.verifier_store import get_verifier
from backend.manager import get_agent_with_token, get_user_id_from_token

auth_router = APIRouter()

class DataSource(Enum):
  DRIVE = "drive"
  GA4 = "ga4" 
  GOOGLE = "google"
  HUBSPOT = "hubspot"
  FACEBOOK = "facebook"
  SALESFORCE = "salesforce"

# Token encryption configuration
ENCRYPTION_KEY = os.getenv('OAUTH_ENCRYPTION_KEY')
ENCRYPTION_SALT = os.getenv('OAUTH_ENCRYPTION_SALT')

if not ENCRYPTION_KEY:
    raise ValueError("ENCRYPTION_KEY environment variable must be set")

def get_encryption_key(key=ENCRYPTION_KEY, salt=ENCRYPTION_SALT):
    """Derive a Fernet key from the provided key and salt."""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt if isinstance(salt, bytes) else salt.encode(),
        iterations=100000,
    )
    key_bytes = key.encode() if isinstance(key, str) else key
    key = base64.urlsafe_b64encode(kdf.derive(key_bytes))
    return Fernet(key)

def encrypt_token(token):
    """Encrypt a token string."""
    if not token:
        return None
    f = get_encryption_key()
    token_bytes = token.encode()
    encrypted_token = f.encrypt(token_bytes)
    return encrypted_token.decode()

def decrypt_token(encrypted_token):
    """Decrypt a token string."""
    if not encrypted_token:
        return None
    f = get_encryption_key()
    encrypted_bytes = encrypted_token.encode()
    decrypted_token = f.decrypt(encrypted_bytes)
    return decrypted_token.decode()

async def get_provider_user_info(client: httpx.AsyncClient, data_source: DataSource, access_token: str) -> Dict:
  """Fetch user info from provider"""
  config = PROVIDER_CONFIG[data_source.value]
  headers = {'Authorization': f'Bearer {access_token}'}
  user_info_url = config['user_info_url']
  
  # Adding access token to the URL required for Hubspot
  if '{access_token}' in user_info_url:
    user_info_url = user_info_url.format(access_token=access_token)
  
  response = await client.get(user_info_url, headers=headers)
  if response.status_code != 200:
    raise HTTPException(status_code=response.status_code, detail="Failed to fetch user info")
  return response.json()

def create_oauth_response(message: str, source: str, success: bool = True) -> HTMLResponse:
    msg_type = 'oauth_success' if success else 'oauth_error'
    return HTMLResponse(content=f"""
        <script>
          window.opener.postMessage({{ type: '{msg_type}', source: '{source}-callback' }}, '*');
          window.close();
        </script>
        <p>{message}</p>
    """)

@auth_router.get('/oauth/integration')
async def integration(data_source, token: str = Depends(JWTBearer()), db=Depends(get_db)):
  if token:
    user_id = get_user_id_from_token(token)
  else:
    raise HTTPException(status_code=400, detail="Invalid token")
  
  latest_token = db.query(Credential).filter(Credential.user_id == user_id).filter(Credential.token_expiry > datetime.utcnow()
                                                 ).order_by(Credential.token_expiry.desc()).first()
  if latest_token:
    access_token = latest_token.access_token
  url = create_oauth_url(data_source)
  url += f"&state={data_source}"
  return RedirectResponse(url, status_code=302)

# state is a security measure sent by the client, returned unchanged by external provider
@auth_router.get('/oauth/callback')
async def callback(code: str = Query(None), error: str = Query(None), state: str = Query(None), db=Depends(get_db)):
    if error:
        return create_oauth_response("Authentication failed. You can close this window.", source='oauth', success=False)
    
    try:
      # Extract data source and user ID from state
      # TODO: Decrypt state to prevent CSRF attacks
      data_source, user_id = state.split('.')
      try:
        data_source = DataSource(data_source)
      except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid data source: {data_source}")
      
      user_id = int(user_id)
      
      # Get the code verifier if using PKCE (for Salesforce)
      code_verifier = None
      if data_source == DataSource.SALESFORCE:
        code_verifier = get_verifier(state)
        if not code_verifier:
          raise HTTPException(status_code=400, detail="PKCE code verifier not found or expired")
      
      # Get the token request details
      url, payload, headers = create_token_request(data_source.value, code, code_verifier)
      
      async with httpx.AsyncClient() as client:
        # Get access token
        response = await client.post(url, data=payload, headers=headers)
        if response.status_code != 200:
          raise HTTPException(status_code=response.status_code, detail=response.text)

        token_data = response.json()
        
        # Salesforce does not return expires_in. Set it manually
        if data_source == DataSource.SALESFORCE:
          token_data['expires_in'] = 7200  # 2 hours in seconds

        # For Facebook, exchange short-lived token for long-lived token
        if data_source == DataSource.FACEBOOK:
          exchange_url = f"https://graph.facebook.com/v18.0/oauth/access_token"
          exchange_params = {
            'grant_type': 'fb_exchange_token',
            'client_id': os.getenv('FACEBOOK_CLIENT_ID'),
            'client_secret': os.getenv('FACEBOOK_CLIENT_SECRET'),
            'fb_exchange_token': token_data['access_token']
          }
          exchange_response = await client.get(exchange_url, params=exchange_params)
          
          if exchange_response.status_code != 200:
            raise HTTPException(status_code=exchange_response.status_code, detail=exchange_response.text)
          
          token_data = exchange_response.json()
          token_data['expires_in'] = 59 * 24 * 60 * 60  # 59 days in seconds (1 day buffer)
          token_data['refresh_token'] = 'None'
        
        # Get user info based on provider
        user_info = await get_provider_user_info(client, data_source, token_data["access_token"])
        vendor_id = user_info.get(PROVIDER_CONFIG[data_source.value]["id_field"])

        # Store credentials with encrypted tokens
        cred = Credential(
          user_id=user_id,
          vendor=data_source.value,
          vendor_id=vendor_id,
          access_token=encrypt_token(token_data['access_token']),
          refresh_token=encrypt_token(token_data.get('refresh_token')),
          token_expiry=datetime.utcnow() + timedelta(seconds=token_data['expires_in']),
          scope=PROVIDER_CONFIG[data_source.value]["scopes"],
          status='active',
          instance_url=token_data.get('instance_url') if data_source == DataSource.SALESFORCE else None
        )
        db.add(cred)
        db.commit()
        
        return create_oauth_response("Authentication successful! You can close this window.", source=data_source)
            
    except Exception as e:
        print(f"OAuth callback failed: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

class OAuthCredentials(BaseModel):
    """Represents OAuth credentials for an integration, including any provider-specific fields"""
    access_token: str
    instance_url: Optional[str] = None

async def refresh_token(db, user_id, integration):
  latest_token_cred = db.query(Credential).filter(
    Credential.user_id == user_id,
    Credential.vendor == integration
  ).first()

  if not latest_token_cred or not latest_token_cred.refresh_token:
    raise HTTPException(status_code=400, detail="No refresh token found")

  try:
    # Decrypt the refresh token
    refresh_token = decrypt_token(latest_token_cred.refresh_token)

    client_id, client_secret = get_client_credentials(integration)
    
    payload = {
      'client_id': client_id,
      'client_secret': client_secret,
      'refresh_token': refresh_token,
      'grant_type': 'refresh_token'
    }
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}

    # Get the token endpoint from provider config
    token_url = PROVIDER_CONFIG[integration]["token_url"]

    async with httpx.AsyncClient() as client:
      response = await client.post(token_url, data=payload, headers=headers)

    if response.status_code == 200:
      token_data = response.json()
      new_access_token = token_data['access_token']
      
      # Store encrypted access token
      latest_token_cred.access_token = encrypt_token(new_access_token)
      latest_token_cred.token_expiry = datetime.utcnow() + timedelta(seconds=token_data['expires_in'])
      db.commit()
      
      return {'status': 'success', 'access_token': new_access_token}
    else:
      raise HTTPException(status_code=response.status_code, detail=response.text)

  except Exception as e:
    raise HTTPException(status_code=500, detail=f"Token refresh failed: {str(e)}")

def get_client_credentials(integration: str) -> Tuple[str, str]:
  if integration in ['drive', 'ga4', 'google']:
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
  elif integration == 'hubspot':
    client_id = os.getenv('HUBSPOT_CLIENT_ID')
    client_secret = os.getenv('HUBSPOT_CLIENT_SECRET')
  elif integration == 'facebook':
    client_id = os.getenv('FACEBOOK_CLIENT_ID')
    client_secret = os.getenv('FACEBOOK_CLIENT_SECRET')
  elif integration == 'salesforce':
    client_id = os.getenv('SALESFORCE_CLIENT_ID')
    client_secret = os.getenv('SALESFORCE_CLIENT_SECRET')
  else:
    raise ValueError(f"Unknown integration: {integration}")

  if not client_id or not client_secret:
    raise ValueError(f"Missing client credentials for {integration}")

  return client_id, client_secret

async def get_oauth_credentials(db: Session, user_id: int, integration: str) -> OAuthCredentials:
    """Get valid OAuth credentials for an integration.
    
    Returns:
        OAuthCredentials containing the access token and any provider-specific fields
        like instance_url for Salesforce.
    """
    latest_token = db.query(Credential).filter(
        Credential.user_id == user_id,
        Credential.vendor == integration,
        Credential.token_expiry > datetime.utcnow() + timedelta(seconds=300)
    ).first()

    if latest_token:
        return OAuthCredentials(
            access_token=decrypt_token(latest_token.access_token),
            instance_url=latest_token.instance_url
        )

    # Get token that might be expired or expiring soon
    expired_token = db.query(Credential).filter(
        Credential.user_id == user_id,
        Credential.vendor == integration
    ).first()
    
    if not expired_token:
        raise HTTPException(status_code=401, detail="No credentials found. Please authorize.")
          
    if not expired_token.refresh_token:
        db.delete(expired_token)
        db.commit()
        raise HTTPException(status_code=401, detail="Invalid credentials. Please reauthorize.")
          
    refresh_result = await refresh_token(db, user_id, integration)
    
    if refresh_result['status'] == 'success':
        return OAuthCredentials(
            access_token=refresh_result['access_token'],
            instance_url=expired_token.instance_url  # Preserve instance_url through refresh
        )
          
    raise HTTPException(status_code=401, detail="Token refresh failed. Please reauthorize.")

# getResources fetches data, processes it, and returns some metadata
@auth_router.post('/oauth/getResources')
async def getResources(data_source: str = Body(...), config: dict = Body(...),
                       db=Depends(get_db), token: str = Depends(JWTBearer())):
  # Get the latest token for the user
  user_id = get_user_id_from_token(token)
  credentials = await get_oauth_credentials(db, user_id, data_source)
  
  data_analyst = get_agent_with_token(token)
  data_analyst.activate_loader(data_source)
  
  # Process the data based on the data source
  if data_source == 'drive':
    urls, headers, payload = create_resource_request(data_source, config, credentials.access_token)
    async with httpx.AsyncClient() as client:
      num_tables = len(urls)
      scope = config['scope']
      scope_dict = json.loads(scope) if isinstance(scope, str) else scope
      table_names = scope_dict.get('tabNames')

      for tab_index, url in enumerate(urls):
        response = await client.get(url, headers=headers)
        table_data = response.json()
        if 'error' in table_data:
          success = False
          detail = table_data['error']['message']
          break
        tab_name = table_names[tab_index]
        success, done, detail = data_analyst.initial_pass(table_data['values'], tab_name, tab_index+1, num_tables)
      
      joint_details = {
        'ssName': scope_dict['ssName'],
        'description': f"Data fetched from Google Drive with id {scope_dict['id']}",
        'globalExtension': data_source
      }
  elif data_source == 'ga4':
    url, headers, payload = create_resource_request(data_source, config, credentials.access_token)
    tab_name = 'ga4_import'
    table_names = [tab_name]

    async with httpx.AsyncClient() as client:
      response = await client.post(url, headers=headers, json=payload)
      response_data = response.json()
      dimensions = config['dimensions']
      metrics = config['metrics']
      joint_details = {
        'ssName': 'Dimensions and Metrics',
        'description': f"Data fetched with {', '.join(dimensions)} dimension and {', '.join(metrics)} metrics",
        'globalExtension': data_source
      }
    success, done, detail = data_analyst.initial_pass(response_data, tab_name, 1, 1)
      
  elif data_source == 'google':
    url, headers, payload = create_resource_request(data_source, config, credentials.access_token)
    tab_name = 'google_ads'
    table_names = [tab_name]

    async with httpx.AsyncClient() as client:
      response = await client.post(url, headers=headers, json=payload)
      response_data = response.json()
      joint_details = {
        'ssName': 'Google Ads Data',
        'description': 'Campaign data from Google Ads',
        'globalExtension': data_source
      }
    success, done, detail = data_analyst.initial_pass(response_data, tab_name, 1, 1)

  elif data_source == 'hubspot':
    url, headers, payload = create_resource_request(data_source, config, credentials.access_token)
    tab_name = ''

    async with httpx.AsyncClient() as client:
      response = await client.get(url, headers=headers)
      response_data = response.json()

      if response_data.get('status') == 'error':
        success = False
        detail = response_data.get('message')
      else:
        scope = config['scope']
        tab_name = scope
        joint_details = {
          'ssName': "Hubspot Integration",
          'description': f"Data fetched with scope: {scope}",
          'globalExtension': data_source
        }
    if len(tab_name) > 0:
      success, done, detail = data_analyst.initial_pass(response_data, tab_name, 0, 1)
      table_names = list(data_analyst.loader.holding.keys())
  
  elif data_source == 'salesforce':
    url, headers, payload = create_resource_request(data_source, config, credentials.access_token, credentials.instance_url)
    tab_name = ''

    async with httpx.AsyncClient() as client:
      response = await client.get(url, headers=headers)
      response_data = response.json()

      if response_data.get('status') == 'error':
        success = False
        detail = response_data.get('message')
      else:
        scope = config['scope']
        tab_name = scope
        joint_details = {
          'ssName': "Salesforce Integration",
          'description': f"Data fetched with scope: {scope}",
          'globalExtension': data_source
        }
    if len(tab_name) > 0:
      success, done, detail = data_analyst.initial_pass(response_data, tab_name, 0, 1)
      table_names = list(data_analyst.loader.holding.keys())
  
  elif data_source == 'facebook':
    try:
      url, headers, params = create_resource_request(data_source, config, credentials.access_token)
      print(f"Facebook API URL: {url}")
      print(f"Facebook API Headers: {headers}")
      print(f"Facebook API Params: {params}")
      tab_name = 'facebook_ads'
      table_names = [tab_name]

      async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, params=params)
        print(f"Facebook API Response: {response.text}")
        if response.status_code != 200:
          error_detail = response.text
          
          # Check for specific error cases
          try:
            error_data = json.loads(error_detail)
            error_message = error_data.get('error', {}).get('message', '')
            error_code = error_data.get('error', {}).get('code', 0)
            
            if "Object with ID 'act_'" in error_message:
              raise HTTPException(
                status_code=400,
                detail="No Facebook Ads account found or insufficient permissions. Please ensure you have an active Facebook Ads account and have granted the necessary permissions."
              )
            elif error_code == 100:  # Permission error
              raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to access Facebook Ads data. Please check your Facebook Ads account permissions."
              )
          except json.JSONDecodeError:
            pass
            
          raise HTTPException(status_code=response.status_code, detail=error_detail)
          
        response_data = response.json()
        
        # Check if we got an empty data set
        if not response_data.get('data'):
          return JSONResponse(
            status_code=200,
            content={
              'table': {
                'columns': ['campaign_id', 'campaign_name', 'spend', 'impressions', 'clicks', 'reach'],
                'rows': [],
                'message': 'No campaign data found for the specified date range.'
              },
              'all_tabs': table_names,
              'done': True
            }
          )
        
        joint_details = {
          'ssName': 'Facebook Ads Data',
          'description': 'Campaign data from Facebook Ads',
          'globalExtension': data_source
        }
        success, done, detail = data_analyst.initial_pass(response_data.get('data', []), tab_name, 1, 1)
        
    except Exception as e:
      print(f"Facebook API Error: {str(e)}")
      raise

  if success:
    load_success, detail = data_analyst.upload_data(detail, joint_details)
    if load_success:
      return JSONResponse(status_code=200, content={'table': detail, 'all_tabs': table_names, 'done': done})
    else:
      raise HTTPException(status_code=400, detail=detail)
  else:
    raise HTTPException(status_code=400, detail=detail)

# Generates URL for popup window for OAuth
@auth_router.get('/oauth/{integration}')
async def integration_auth(integration: str, token: str = Depends(JWTBearer()), db = Depends(get_db)):
  if not token: 
    raise HTTPException(status_code=400, detail="Invalid token")
  if integration not in {ds.value for ds in DataSource}:
    raise HTTPException(status_code=400, detail=f"{integration} not supported.")
  
  user_id = get_user_id_from_token(token)

  # Check for existing valid token
  latest_token = db.query(Credential).filter(
    Credential.user_id == user_id, Credential.vendor == integration, Credential.token_expiry > datetime.utcnow()
  ).first()

  # If we have a valid token, return success
  if latest_token:
    return create_oauth_response("Already authorized! You can close this window.", source=integration)

  # Try to refresh token if possible
  try:
    refresh_result = await refresh_token(db, user_id, integration)
    if refresh_result['status'] == 'success':
      return create_oauth_response("Authorization refreshed! You can close this window.", source=integration)
  except HTTPException:
    # If refresh fails, continue with new authorization
    pass

  # Create state parameter with integration type and user ID
  state = f"{integration}.{user_id}"
  
  # Get OAuth URL for the specified integration
  url = create_oauth_url(integration, state) + f"&state={state}"
  
  return RedirectResponse(url, status_code=302)

@auth_router.post('/disconnectIntegration/{integration}', dependencies=[Security(JWTBearer())]) 
async def disconnect_integration(integration: str, token: str = Depends(JWTBearer()), db=Depends(get_db)):
  user_id = get_user_id_from_token(token)
  db.query(Credential).filter(
    Credential.user_id == user_id,
    Credential.vendor == integration
  ).delete()
  db.commit()
  return {'status': 'success'}

@auth_router.get('/integrationStatus/{integration}')
async def check_integration_status(integration: str, token: str = Depends(JWTBearer()), db=Depends(get_db)):
  try:
    user_id = get_user_id_from_token(token)
    
    # First check for valid access token
    latest_token = db.query(Credential).filter(
      Credential.user_id == user_id,
      Credential.vendor == integration,
      Credential.token_expiry > datetime.utcnow()
    ).first()
    
    if latest_token:
      return {'connected': True}
    
    # If no valid access token, try refreshing
    try:
      refresh_result = await refresh_token(db, user_id, integration)
      return {'connected': refresh_result['status'] == 'success'}
    except HTTPException:
      return {'connected': False}
          
  except Exception as e:
    print(f"Error checking integration status: {str(e)}")
    return {'connected': False} 

@auth_router.get('/auth/check')
async def check_authentication(request: Request, auth_token: str = Depends(JWTBearer())):
  """
  Check if the user is authenticated by verifying their JWT token in the cookie
  
  Returns:
      JSON with authentication status and user info if authenticated
  """
  try:
    token = get_token(request=request, auth_token=auth_token)
    if not token:
      return {"authenticated": False}
    
    # Decode and verify the JWT token
    payload = decode_JWT(token)
    
    # Return authentication status and user info
    return {
      "authenticated": True,
      "user": {
        "email": payload.get("email"),
        "userID": payload.get("userID")
      }
    }
  except HTTPException:
    return {"authenticated": False}
  except Exception:
    return {"authenticated": False}