import asyncio
import json
from os import path
import sys
import time
import pytest

from fastapi.testclient import TestClient
# add backend to path
TEST_DIR = path.dirname(path.abspath(__file__))
PROJECT_DIR = path.dirname(path.dirname(TEST_DIR))
sys.path.append(PROJECT_DIR)

from backend.webserver import app
from backend.db import load_engine, get_db
from backend.test.dependencies import test_db, get_test_db, load_test_engine

app.dependency_overrides[get_db] = get_test_db
app.dependency_overrides[load_engine] = load_test_engine
client = TestClient(app)

def test_users():
  response = client.get("/api/v1/users")
  assert response.status_code == 200

  print('what methods were called on test_db?', test_db)
  print('calls', test_db.mock_calls)
  assert response.json() == []


def send_response(websocket, response):
  response_blob = json.dumps(response, indent=2)
  websocket.send_bytes(response_blob.encode('utf-8'))

@pytest.mark.timeout(30)
def test_simterm_reactive():
  shoe_sheet_select = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"]
  }
  response = client.post(
    "/api/v1/sheets/select", json=shoe_sheet_select
  )
  assert response.status_code == 200

  with client.websocket_connect("/api/v1/ws") as websocket:
    question = {
      'currMessage': "are there any typos in the products table?",
      'currState': "078", # Forcing the merge-remove dax
      'lastAction': None
    }
    send_response(websocket, question)
    
    received_simterm = False
    while True:
      data = websocket.receive_json()
      print('DATA RECEIVED', data)
      if data['interaction']['format'] == 'json':
        if data['interaction']['flowType'] == 'Detect(typo)':
          # found simterm!
          received_simterm = True
          assert 'column' in data['interaction']
          break

    assert received_simterm

    pause_time = 1
    time.sleep(pause_time)

    terms = data['interaction']['payload']
    column = data['interaction']['column']

    print('------ about to send POST to merge terms')
    for chosen, similar in terms.items():
      term_answer = {
        'column': chosen,
        'chosen': chosen,
        'similar': similar
      }
      response = client.post(
        '/api/v1/interactions/resolve/typo', json=term_answer
      )
      time.sleep(pause_time)
      
      assert response.status_code == 200

      print('response of POST', response)
      content = response.json()

      print('content', content)
      # I need to assert that the correct response is sent! This is a false positive currently
      # # data = websocket.receive_json()
      # print("data['message']", data['message'])
      # assert 'clarify' not in data['message']

@pytest.mark.timeout(30)
def test_simterm_reactive_on_non_active_tab():
  shoe_sheet_select = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"]
  }
  response = client.post(
    "/api/v1/sheets/select", json=shoe_sheet_select
  )
  assert response.status_code == 200

  with client.websocket_connect("/api/v1/ws") as websocket:
    question = {
      'currMessage': "are there any typos in the products table?",
      'currState': "078", # Forcing the merge-remove dax
      'lastAction': None
    }
    send_response(websocket, question)
    
    received_simterm = False
    while True:
      data = websocket.receive_json()
      if data['interaction']['format'] == 'json':
        if data['interaction']['flowType'] == 'Detect(typo)':
          # found simterm!
          received_simterm = True
          for term in data['interaction']['payload']['terms']:
            assert 'column' in term
            assert 'chosen' in term
            assert 'similar' in term
          break

    assert received_simterm

    # The client requests the tab data
    response = client.get(
      '/api/v1/sheets/fetch?tab_name=products&row_start=0&row_end=118'
    )

    pause_time = 3
    time.sleep(pause_time)

    terms = data['interaction']['payload']['terms']

    for term in terms:
      
      response = client.post(
        '/api/v1/merge_terms', json=term
      )
      time.sleep(pause_time)
      
      assert response.status_code == 200

      # I need to assert that the correct response is sent! This is a false positive currently
      # data = websocket.receive_json()
      # print("data['message']", data['message'])
      # assert 'clarify' not in data['message']


@pytest.mark.skip
@pytest.mark.timeout(20)
def test_simterm_proactive():
  # skip this test for now, waiting on improved ML. See ticket: 0575
  # Current problem: Dax code returns 269 instead of 078

  shoe_sheet_select = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"]
  }
  response = client.post(
    "/api/v1/sheets/select", json=shoe_sheet_select
  )
  assert response.status_code == 200

  with client.websocket_connect("/api/v1/ws") as websocket:
    # You can now interact with the websocket
    question = {
      'currMessage': "can you show the revenue by product styles?",
      'currState': "",
      'lastAction': None
    }
    send_response(websocket, question)

    received_simterm = False
    while True:
      data = websocket.receive_json()
      if "similar terms" in data.get('message', ''):
        # send a response back
        answer = {
          'currMessage': "yes, I'd like to investigate the similar terms",
          'currState': "",
          'lastAction': None
        }
        send_response(websocket, answer)
      if data['interaction']['format'] == 'json':
        if data['interaction']['flowType'] == 'Detect(typo)':
          # found simterm!
          received_simterm = True
          break

    assert received_simterm
