import sys
import json
import time
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from collections import defaultdict
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.webserver import app
from backend.db import get_db, load_engine
from backend.auth.credentials import get_auth_user_email
from utils.dependencies import get_test_db, load_test_engine

TEST_TOKEN = "secret_acromi_token_for_testing_123"

def get_test_user_email():
  return "<EMAIL>"

@pytest.fixture(scope="module")
def test_client():
  app.dependency_overrides[get_db] = get_test_db
  app.dependency_overrides[load_engine] = load_test_engine
  app.dependency_overrides[get_auth_user_email] = get_test_user_email
  
  client = TestClient(app)
  yield client
  
  app.dependency_overrides.clear()

@pytest.fixture(scope="module")
def websocket(test_client: TestClient):
  with test_client.websocket_connect(f"/api/v1/ws?token={TEST_TOKEN}") as ws:
    yield ws

def test_select_sheet(test_client):
  spreadsheet_data = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"],
  }
  response = test_client.post("/api/v1/sheets/select", json=spreadsheet_data)
  assert response.status_code == 200, f"Failed to select spreadsheet: {response.text}"

def decide_which_parts(parts):
  # determine which parts of the message to keep, returns 3 boolean values
  store_query = 'query' in parts
  store_code = 'code' in parts
  store_actions = 'actions' in parts
  return store_query, store_code, store_actions

def decide_part_availability(parsed):
  # determine which parts are available in the message, returns 3 boolean values
  has_query, has_code, has_actions = False, False, False
  
  if 'interaction' in parsed.keys():
    if parsed['interaction'] and 'content' in parsed['interaction']:
      if 'SQL Query' in parsed['interaction']['content']:
        has_query = True
      if 'Pandas Code' in parsed['interaction']['content']:
        has_code = True
  if 'actions' in parsed.keys():
    has_actions = True
  return has_query, has_code, has_actions

def send_message(websocket, message:str, gold_dax:str, parts:list=[]):
  websocket.send_json( { 'currentMessage': message, 'dialogueAct': gold_dax, 'lastAction': None}, mode='binary')
  store_query, store_code, store_actions = decide_which_parts(parts)
  results = defaultdict(str)

  while True:
    try:
      raw_output = websocket.receive_json()
      
      has_query, has_code, has_actions = decide_part_availability(raw_output)
      if store_query and has_query:
        results['query'] = raw_output['interaction']['content'][19:]
      if store_code and has_code:
        results['code'] = raw_output['interaction']['content'][21:]
      if store_actions and has_actions:
        results['actions'] = raw_output['actions']
      if 'message' in raw_output.keys():
        results['message'] = raw_output['message']
        return results

    except json.JSONDecodeError as e:
      return f"Failed to decode JSON: {e}"

  return 'Error'

@pytest.mark.timeout(30)
def test_join_col_with_separator(websocket):
  turn_one = send_message(websocket, "Create a location column that combines city and state with a comma", '05C')
  print("Turn 1:", turn_one)
  assert 'successfully' in turn_one['message'], 'Failed to create new column'

@pytest.mark.timeout(30)
def test_missing_separator(websocket, test_client):
  turn_two = send_message(websocket, "Also make a column for full name", '05C', ['actions'])
  print("Turn 2:", turn_two)
  assert 'INTERACT' in turn_two['actions'], 'Missing interaction to get separator'

  payload = {
    'flowType': 'Transform(merge)',
    'selected': [
      {'tab': 'customers', 'col': 'first', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'last', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'stage': 'merge-style',
    'style': {'name': 'space', 'setting': ''}
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  assert response.status_code == 200, 'Failed to complete merge style interaction'
  turn_three = response.json()
  print("Turn 3:", turn_three)
  assert 'successfully' in turn_three['message'], 'Failed to create new column'

@pytest.mark.timeout(30)
def test_ambiguous_source(websocket, test_client):
  turn_four = send_message(websocket, "Make me a column for ad group title", '05C', ['actions'])
  print("Turn 4:", turn_four)
  assert 'INTERACT' in turn_four['actions'], 'Missing interaction to clarify source columns'

  payload = {
    'flowType': 'Transform(merge)',
    'selected': [
      {'tab': 'products', 'col': 'brand', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'products', 'col': 'style', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'stage': 'pick-tab-col',
    'style': {'name': 'space', 'setting': ''}
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_five = response.json()
  assert 'message' in turn_five.keys(), 'Failed to clarify source columns'

  if turn_five['message'].startswith('How'):
    payload = {
      'flowType': 'Transform(merge)',
      'selected': [
        {'tab': 'customers', 'col': 'brand', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'customers', 'col': 'style', 'row': -1, 'ver': True, 'rel': ''}
      ],
      'stage': 'merge-style',
      'style': {'name': 'underscore', 'setting': ''}
    }

    response = test_client.post('/api/v1/interactions/merge', json=payload)
    turn_six = response.json()
    final_message = turn_six['message']
  else:
    final_message = turn_five['message']

  assert 'ad_group_title' in final_message, 'Failed to create new column'