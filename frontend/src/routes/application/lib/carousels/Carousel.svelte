<script>
  import Slide from './Slide.svelte';
  import Caption from './Caption.svelte';
  import Indicator from './Indicator.svelte';
  export let showIndicators = true;
  export let showCaptions = true;
  export let images;
  export let slideControls = true;
  export let loop = false;
  export let duration = 2000;
  // Carousel
  export let divClass = 'overflow-hidden relative h-56 rounded-lg sm:h-64 xl:h-80 2xl:h-96';
  export let indicatorDivClass = 'flex absolute bottom-5 left-1/2 z-30 space-x-3 -translate-x-1/2';
  // Caption
  export let captionClass = 'h-10 dark:bg-gray-700 dark:text-white p-2 my-2 text-center';
  // Indicator
  export let indicatorClass = 'w-3 h-3 rounded-full bg-teal-200 hover:bg-teal-500 opacity-60';
  // Slide
  export let slideClass = '';
  let imageShowingIndex = 0;
  $: image = images[imageShowingIndex];
  const nextSlide = () => {
    if (imageShowingIndex === images.length - 1) {
      imageShowingIndex = 0;
    } else {
      imageShowingIndex += 1;
    }
  };
  const prevSlide = () => {
    if (imageShowingIndex === 0) {
      imageShowingIndex = images.length - 1;
    } else {
      imageShowingIndex -= 1;
    }
  };
  const goToSlide = (number) => (imageShowingIndex = number);
  let thumbWidth = 100 / images.length;
  if (loop) {
    setInterval(() => {
      nextSlide();
    }, duration);
  }
</script>

<div id="default-carousel" class="relative">
  <div class={divClass}>
    <Slide image={image.imgurl} altTag={image.name} attr={image.attribution} {slideClass} />
  </div>
  {#if showIndicators}
    <!-- Slider indicators -->
    <div class={indicatorDivClass}>
      {#each images as { id, imgurl, name, attribution }}
        <Indicator
          {name}
          selected={imageShowingIndex === id}
          on:click={() => goToSlide(id)}
          {indicatorClass}
        />
      {/each}
    </div>
  {/if}
  {#if slideControls}
    <!-- Slider controls -->
    <button
      on:click={prevSlide}
      type="button"
      class="flex absolute top-0 left-0 z-30 justify-center items-center px-4 h-full cursor-pointer group focus:outline-none"
      data-carousel-prev
    >
      <span
        class="inline-flex justify-center items-center w-8 h-8 rounded-full sm:w-10 sm:h-10 bg-teal-300 group-hover:bg-teal-500 ring-4 ring-teal-300 group-focus:outline-none"
      >
        {#if $$slots.previous}
          <slot name="previous" />
        {:else}
          <svg
            aria-hidden="true"
            class="w-5 h-5 text-white sm:w-6 sm:h-6 dark:text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            ><path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            /></svg
          >
        {/if}
        <span class="hidden">Previous</span>
      </span>
    </button>
    <button
      on:click={nextSlide}
      type="button"
      class="flex absolute top-0 right-0 z-30 justify-center items-center px-4 h-full cursor-pointer group focus:outline-none"
      data-carousel-next
    >
      <span
        class="inline-flex justify-center items-center w-8 h-8 rounded-full sm:w-10 sm:h-10 bg-teal-300 group-hover:bg-teal-500 ring-4 ring-teal-300 group-focus:outline-none"
      >
        {#if $$slots.next}
          <slot name="next" />
        {:else}
          <svg
            aria-hidden="true"
            class="w-5 h-5 text-white sm:w-6 sm:h-6 dark:text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            ><path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            /></svg
          >
        {/if}
        <span class="hidden">Next</span>
      </span>
    </button>
  {/if}
</div>

{#if showCaptions}
  <Caption caption={images[imageShowingIndex].name} {captionClass} />
{/if}
