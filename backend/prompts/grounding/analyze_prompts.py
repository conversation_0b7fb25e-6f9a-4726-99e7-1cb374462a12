query_flow_prompt = """Given the conversation history and supporting details, your task is to determine the relevant columns to query.
Supporting details includes the valid tables and columns, along with the previous dialogue state, written as the table name followed by a list of column names.

Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as 'ambiguous'. If no columns are relevant, then just leave the list empty.
Current columns are often carried over from the previous state, so pay close attention since it provides useful context that may not be available from the conversation alone.
Your entire response should be in well-formatted JSON including keys for thought (string) and result (list) where each item is a dict, with no further explanations before or after the JSON output.
Each item in the list should be a dict with keys for 'tab' (string), 'col' (string), and optionally 'rel' (string) when the column is 'ambiguous'.

For example,
---
## Online Course Scenario
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
* Columns: CourseID, CourseTitle, InstructorID, CourseDescription, StartDate, EndDate, Duration, CourseFormat, Category, EnrollmentCount in BB_courses;
EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in BB_enrollments;
TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;
OutreachID, CampaignName, TargetAudience, Platform, ResponseRate, Collaborators in CanvasOutreach

_Conversation History_
User: how many of the students enrolled in the Biology course provided a testimonial?
Agent: 12 students enrolled in Biology provided an approved testimonial.
User: how about for the Chemistry course?

_Previous State_
BB_courses - [CourseID, CourseTitle, Category]
BB_enrollments - [CourseID, StudentID, EnrollmentDate]
Testimonials - [CourseID, StudentID, TestimonialText, ApprovalStatus]

_Output_
```json
{{
  "thought": "StudentID can be used to count number of students. BB_Courses info can match for Chemistry. TestimonialText should be checked to make sure it is not empty, and ApprovalStatus is useful to make sure it can be used. CourseID can be used to group by course and join tables.",
  "result": [
    {{"tab": "BB_courses", "col": "CourseID"}},
    {{"tab": "BB_courses", "col": "CourseTitle"}},
    {{"tab": "BB_courses", "col": "Category"}},
    {{"tab": "BB_enrollments", "col": "CourseID"}},
    {{"tab": "BB_enrollments", "col": "StudentID"}},
    {{"tab": "BB_enrollments", "col": "EnrollmentDate"}},
    {{"tab": "Testimonials", "col": "CourseID"}},
    {{"tab": "Testimonials", "col": "StudentID"}},
    {{"tab": "Testimonials", "col": "TestimonialText"}},
    {{"tab": "Testimonials", "col": "ApprovalStatus"}}
  ]
}}
```

## Restaurant Scenario
* Tables: customerContact, customerOrders, marketingOffers
* Columns: CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in customerContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in customerOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in marketingOffers

_Conversation History_
User: ok, let's also check for the SEND40OFF code
Agent: The SEND40OFF redemption code was used by 23 customers.
User: What are their email addresses?

_Previous State_
customerOrders - [OrderID, CustomerID]
marketingOffers - [OrderKey, EndDate, RedemptionCode]

_Output_
```json
{{
  "thought": "RedemptionCode can be used to filter for SEND40OFF and I can group customers with CustomerID. EndDate matches the year with OrderID and OrderKey for joins. Although customerContact has a ShippingAddress column, this is likely a physical address and not an email address, so EmailAddress is ambiguous.",
  "result": [
    {{"tab": "customerOrders", "col": "OrderID"}},
    {{"tab": "customerOrders", "col": "CustomerID"}},
    {{"tab": "marketingOffers", "col": "OrderKey"}},
    {{"tab": "marketingOffers", "col": "EndDate"}},
    {{"tab": "marketingOffers", "col": "RedemptionCode"}},
    {{"tab": "customerContact", "col": "EmailAddress", "rel": "ambiguous"}}
  ]
}}
```

## E-commerce Online Advertiser Scenario
* Tables: Salesforce, Google Campaigns, Shopify Inventory, FB Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, DateRegistered, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand in Salesforce;
CampaignID, CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Google Campaigns;
ItemID, BrandName, Category, Price, StockQuantity, DateAdded, Supplier in Shopify Inventory;
PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in FB Promotions

_Conversation History_
User: What are the most popular brands by preference for each month?
Agent: The most popular brand are Gucci, Chanel, and Louis Vuitton. See table for details.
User: When was the earliest date that we started promoting it?

_Previous State_
Salesforce - [CampaignSource, PreferredBrand]
Google Campaigns - [CampaignID, StartDate, EndDate]

_Lesson_
Think carefully to see if there are any ambiguities that may arise when writing a SQL query. In this case, it is unclear how to perform the join. Separately, since table names are case sensitive, we copy them over exactly.

_Output_
```json
{{
  "thought": "Promo has a StartDate. BrandName or PreferredBrand can both be used to filter for Gucci. However, neither can be used to join into the FB Promotions table, so it is unclear which to use. We might need to search in PromoName for Gucci instead.",
  "result": [
    {{"tab": "Shopify Inventory", "col": "BrandName", "rel": "ambiguous"}},
    {{"tab": "Salesforce", "col": "PreferredBrand", "rel": "ambiguous"}},
    {{"tab": "FB Promotions", "col": "StartDate", "rel": ""}},
    {{"tab": "FB Promotions", "col": "PromoName", "rel": "ambiguous"}}
  ]
}}
```

## SaaS Data Startup Scenario
* Tables: mq_leads, product_launches, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in mq_leads;
launch_id, is_secure, provenance, version, features, documentation_link in data_sources;
subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity

_Conversation History_
User: How many leads do we have from Qualcomm? Nice.
Agent: Yes, that is great. Glad to see our marketing efforts are paying off.
User: I wonder what their total GPU capacity could be, must be quite a lot!

_Previous State_
mq_leads - [lead_id, organization, status]

_Lesson_
When a request is completely unclear, label the table as 'unsure'. Separately, note how prior dialogue history may be incomplete, so just do the best you can with what you have.

_Output_
```json
{{
  "thought": "I can filter for Qualcomm with organization and group by lead_id. However, there does not seem to be a GPU capacity column in any table.",
  "result": [
    {{"tab": "mq_leads", "col": "organization", "rel": ""}},
    {{"tab": "mq_leads", "col": "lead_id", "rel": ""}},
    {{"tab": "unsure", "col": "gpu_capacity", "rel": "ambiguous"}}
  ]
}}
```

## High-end Yoga Studio Scenario
* Tables: members, classes, packages
* Columns: member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact in members;
class_id, class_name, instructor_id, class_date, start_time, end_time, room_number, class_capacity, enrolled_count, description, equipment_required in classes;
package_id, package_name, duration, package_cost, included_classes, additional_benefits in packages

_Conversation History_
User: What's the 3 most common ones?
Agent: The three most commonly purchased packages are the 1-Day Pass, Monthly Membership, and the 10-Class Grab Pack.
User: What is the average amount paid by all members?

_Previous State_
packages - [package_id, package_name, package_cost]

_Lesson_
Although not mentioned directly, you can infer the correct column for amount using the valid columns. Also, notice how the package_name in the previous state was relevant even though the user did not explicity ask for it. This is because name-related columns (ie. full_name, class_name, package_name) are useful for communicating the final result in a natural way.

_Output_
```json
{{
  "thought": "I can group using member_id. Members table has membership_fee for calculating amounts, but since we are already discussing package prices, I will use price instead from the packages table.",
  "result": [
    {{"tab": "packages", "col": "package_id"}},
    {{"tab": "packages", "col": "price"}},
    {{"tab": "members", "col": "member_id"}},
    {{"tab": "members", "col": "full_name"}},
    {{"tab": "members", "col": "packages_bought"}}
  ]
}}

## Mobile-First Car Rental Scenario
* Tables: Bookings, Promotions, CustomerSupport
* Columns: BookingID, CustomerID, VehicleID, StartDate, EndDate, PickupLocation, DropoffLocation, BookingStatus, TotalAmount, PaymentStatus in Bookings;
PromotionID, DiscountAmount, ApplicableVehicleTypes, TermsConditions, RedemptionCount in Promotions;
TicketID, CustomerID, IssueDate, IssueType, IssueDescription, AssignedAgent, ResolutionStatus, ResolutionDate, Feedback, FollowUpRequired in CustomerSupport

_Conversation History_
User: Yea, Los Angeles looks right
Agent: Sure, I have merged those terms into Los Angeles. Based on the results, the most popular pickup locations are Chicago, San Francisco, and Los Angeles. See table for more details.
User: What is the average price for all bookings in those locations.

_Previous State_
(temporary) - [RelatedLocationTerms]

_Output_
```json
{{
  "thought": "Based on conversation history, I should use PickupLocation rather than DropoffLocation for filtering and grouping the cities. I can calculate the average price using TotalAmount.",
  "result": [
    {{"tab": "Bookings", "col": "PickupLocation"}},
    {{"tab": "Bookings", "col": "TotalAmount"}}
  ]
}}
```

_Conversation History_
User: Do we have any data on a 'Brad Kenney' account?
Agent: Yes, I see a row for Brad Kenney in the Bookings table.
User: What's his status?

_Output_
```json
{{
  "thought": "We can infer from context that we are dealing with Bookings, rather than ResolutionStatus from CustomerSupport, but this could still be referring to either BookingStatus or PaymentStatus.",
  "result": [
    {{"tab": "Bookings", "col": "BookingStatus", "rel": "ambiguous"}},
    {{"tab": "Bookings", "col": "PaymentStatus", "rel": "ambiguous"}}
  ]
}}
```


---
## Current Scenario
{valid_tab_col}

_Conversation History_
{history}

_Previous State_
{prior_state}

_Lesson_
Please choose the list of relevant tab and cols from the valid options, which means preserving all spacing, capitalization, and special characters. There should be no text or explanations after the JSON output.

_Output_
"""

measure_flow_prompt = """Based on the conversation history, we currently want to calculate a marketing metric or KPI.
Please start by identifying the metric the user wants to calculate, and which columns need to be aggregated to produce the metric.
In all likelihood, the relationship may not be entirely obvious, in which case you should select _potentially_ relevant columns to form variables, and then combine these variables to calculate the metric.
Furthermore, consider whether there are any global filters, groupings, or other transformations might be applicable to the calculation as a whole.
Finally, note whether there are any complications or uncertainties that need to be resolved before proceeding.

When you predict the metric, be sure to includes its short form as `(acronym)` where the name is surrounded by parentheses. Common examples include: ARPU, AOV, CTR, CVR, ROAS, ROI, CPA, CPC, CPM, DAU, MAU, NPS, etc.
Some other not-so-standard short forms we also recognize include: Bounce, Churn, Device, Engage, Retain, Abandon, Open, and Profit.
These stand for Bounce Rate, Customer Churn Rate, Device Ratio, Engagement Rate, Retention Rate, Abandonment Rate, Email Open Rate, and Net Profit, respectively.
Of course, it is entirely possible that the user is not referring to any of these metrics, in which case you should generate a metric name that best captures the user's intent, matching industry standards where possible.
If the metric is not clear or not mentioned, then label it as `unsure`. For example, you could say: 'I am (unsure) what metric the user wants to calculate.'

Whenever a column is mentioned, (whether for aggregation, filtering, or variable formation) be sure to write it as `<column_name>` where the name is surrounded by angle brackets.
Also, please keep a bit of white space outside of the angle brackets to make it easier to read and parse.
If there are no obviously matching columns or conversely, multiple conflicting columns that could be used, then note these issues in your thoughts.
Critically, in all cases, do *not* mention any invalid columns. This is very important to my career, only choose from valid columns!

Your goal at the moment is to simply produce a well formed thought -- there is no need to write any code yet!
To give a sense of an ideal thought process, we will go through some sample scenarios, and then tackle the final real scenario.
Notice that the generated thoughts are only a few sentences long, so please avoid any unnecessary details.

For example,
---
## Enterprise Data Security Scenario
For our first sample spreadsheet, suppose the valid options are:
* Tables: HubspotCRM; TransactionHistory; InteractionLogs
* Columns: cust_id, signup_date, cust_name, email, region, tracking_id, channel, acct_status in HubspotCRM;
trans_id, cust_id, trans_date, product_id, amount, trans_type, license_fee, service_charge, maintenance_income in the TransactionHistory;
interaction_id, cust_id, interact_date, interact_type, interact_duration, issue_resolved, expenses in the InteractionLogs

_Conversation History_
User: How many new customers did we get in the past month from the west coast?
Agent: We acquired 2,398 new customers in the past month in the west coast.
User: Can you help me figure out how many of them are still around?

_Output_
A likely metric is retention rate (Retain) , which is the number of active customers divided by the total number of customers.
We should start by filtering for the west coast, likely using the <region> column. <signup_date> is likely the best way to determine if a customer is acquired.
Grouping by customer id <cust_id> will allow us to form the base of total customers.
The last interaction date <interact_date> or the last transaction date <trans_date> are both ways to determine if a customer is active in the last month, so we should clarify before proceeding.

_Conversation History_
User: What are the different types of interactions?
Agent: Interaction types include onboarding, training, support and cancellation.
User: How much was our return on investment last year?

_Output_
The user wants to calculate the return on investment (ROI) for the last year, which is revenue divided by costs.
Nothing is verified, so we can go with sum of <expenses> as 'costs'.  However, many reasonable options exist for revenue, including <license_fee>, <service_charge>, or <maintenance_income>.
We also need to filter for last year, which can come from <interact_date> or <trans_date> , so that should be clarified as well.

## E-commerce Online Advertiser Scenario
For our second sample spreadsheet, suppose the valid options are:
* Tables: GoogleAds_Q3, SalesRecord_Shopify_0812, Product_Details
* Columns: gAd_ID, spend(Dollars), clickCount, campaignInitDate, campaignTermDate, adBounceRate, audienceFocus, adContentCode in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
SKU, itemName, itemCategory, retailPrice, totalCost, stockLevel in Product_Details

_Conversation History_
User: What is the CPC for our ads?
Agent: Sure, I will need to verify the cost and clicks first. Does this look right?
User: Yea, clickCount looks good.

_Output_
Continuing the calculation of (CPC) , the user has confirmed that <clickCount> is the correct column for clicks, but we still need to verify the column used to derive cost.
Both <spend(Dollars)> and <totalCost> are plausible options for cost, but I will choose <spend(Dollars)> because it comes from the same table as <clickCount> .
When performing calculations, we should limit cost to only those that are directly attributable to clicks.

_Conversation History_
User: see the shopify table?
Agent: Yes, what would you like to know about it?
User: What's the average order value recently?

_Output_
Average order value (AOV) is not directly given, but can be possibly calculated as <revenueGenerated> / <unitsMoved> .
We need to consider if there are other columns such as taxes, discounts, or other adjustments that might affect order value.
The time range has not been specified, so we should clarify how far back to look to determine 'recently'.

_Conversation History_
User: Did we have any campaigns running in July?
Agent: Yes, there were 3 campaigns running in July with IDs: 1624632, 1624900, and 1624931.
User: and what was tehre bounce rate?

_Output_
The bounce rate (Bounce) is the inverse of engagement, which is already directly given as <adBounceRate> .
We simply need to average over the time period of July. No year is given, so I will default to the current year of 2024.
We can find these by checking any campaigns with IDs 1624632, 1624900, and 1624931, which can be found in <gAd_ID> .

_Conversation History_
User: Did people end up buying more stuff during the Labor Day sale?

_Output_
Ideally, we would filter for 'Labor Day' using a <campaignName> column in GoogleAds_Q3 to calculate the conversion rate (CVR) , but that does not seem to exist.
Another idea is to look for campaigns with initialization dates slightly before Labor Day, and termination dates slightly after Black Friday. If none are found, we should ask the user to clarify how to find specific campaigns.
In terms of variables, the closest match for conversions is <unitsMoved> . Since clicking on an ad leads to a visit, the closest match for visits is <clickCount> . We can possibly join the tables by <gAdRef> and <gAd_ID> .

## Mobile Workout Community Scenario
For our third sample spreadsheet, suppose the valid options are:
* Tables: AdobeAnalytics_final; SubscriptionMembership; Canva Content (revised); VendorExpenses
* Columns: campaign_id, ad_platform, ad_spend, ad_type, ad_copy, user_activity, conversion_count in AdobeAnalytics_final;
member_id, subscription_date, renewal_date, subscription_tier, monthly_fee, activity, member_status in SubscriptionMembership;
video_id, trainer_id, video_campaign_id, creation_date, video_type, trainer_fee, view_count in Canva Content (revised);
vendor_id, service_provided, expense_date, expense_amount, vendor_category in VendorExpenses

_Conversation History_
User: can we see just the past week, broken down by day?
Agent: OK, I have filtered to just the past week
User: what is the CTR during that time?
Metric: CTR = clicks / impressions

_Output_
Clicks and impressions are both found as activity types in <user_activity> , which can be used to calculate (CTR) .
However, <view_count> means a user already landed on the page, which may also count as a click, so we should clarify.
We also need to filter for the past week and group by day, which can be found in the <expense_date> column. No year is given, so I will default to the current year of 2024.

_Conversation History_
User: How many videos have over 1000 views?
Agent: 17 videos have over 1000 views.
User: Did we make a profit from any of them?
Agent: Does monthly_fee or trainer_fee count as revenue?
User: Go with monthly

_Output_
In calculating (Profit) , the user has confirmed that revenue can be derived from <monthly_fee> .
Possible costs include <expense_amount> and <ad_apend> , but I will choose <expense_amount> because we are discussing videos.
We also need to remember to filter for <view_count> over 1000. Additionally, we should double check how to join video expenses to monthly subscription fees.

_Conversation History_
User: yea, we can delete those since they seem like errors
Agent: Great, I have removed those for you.  The ROAS is now 134.2%
User: What if we limit only to MyFitnessPal ads?

_Output_
Our goal is to calculate (ROAS) , which is the sum of trainer fees divided by the sum of ad spend.
We now want to filter for MyFitnessPal ads that can be found in the <ad_platform> column.
Additionally, we should try our best to limit our sum of trainer fees to that which came as a direct result of <ad_spend>, which means joining the tables by <video_campaign_id> and <campaign_id>.
---
## Current Scenario
For our real case, the available data includes:
{valid_tab_col}
The currently active table is '{current}'.

_Conversation History_
{history}

Now it's your turn! Please generate a thought process that highlights the important considerations when calculating the target metric, making sure to note any surprising or nuanced interpretations.
Remember to use angle brackets ('<' and '>') to denote column names, and to keep your response concise.

_Output_
"""

# If any ambiguity arises, rather than making assumptions, instead note the uncertainty and consider what clarification is needed.
segment_analysis_prompt = """As seen in the conversation history, the user wants to perform complex analysis that likely takes multiple steps.
Before diving into a calculation, it's important to fully understand the situation and the data we need to access in order to avoid embarrassing mistakes.
However, this is not be straightforward because the final result requires deriving accurate intermediate results, which in turn necessitates careful planning.

Let's start by considering how we can manage the scope of the analysis, focusing on three key areas:
  1. Grounding to existing data
    * Can we identify specific tables or columns to focus our attention?
    * Is it possible to limit the scope by filtering to rows containing certain values?
    * Are there any time-based constraints that can help us narrow down the range?
  2. Staging intermediate results
    * What groupings or aggregations are needed in our query when pulling the data?
    * Should we create temporary tables or columns to store the derived metrics?
    * What validation checks can we put in place to confirm calculation accuracy?
  3. Disambiguating underspecified requests
    * Are we making any unwarranted assumptions about the user's intent?
    * What steps might contain branching logic that require clarification?
    * What information is still missing before we can proceed with the analysis?

When formulating your thoughts, whenever a column is referenced, be sure to write it as <column_name> where the name is surrounded by angle brackets: < and >.
These thoughts will set the foundation of our plan, which is tackled later on the process.
To reiterate, we are *not* writing any code or producing a plan at this stage, just outlining the key considerations.
Consequently, your entire response should be contained within a single paragraph that spans no longer than 5-7 sentences.

For example,
---
## Initial Request
For our first example, suppose the tables and columns are:
* Tables: CodePathCourses, StudentProgress, LearnerSuccess, MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, CareerOutcome, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, ConversionMetrics, PartnershipType, BudgetAllocated, LeadSource in MarketingCampaigns

_Conversation History_
User: What is the range of peer review scores for all students in the last cohort?
Agent: Using CohortStartDate to determine the last cohort, the peer review scores range from 60 to 95.
User: Ok, taking a broader view, does increased student activity lead to better outcomes?

Since the user has just begun to make a request, limited information is available to guide our analysis.
Thus, our focus should be enumerating the missing details we need to proceed, such as specific columns or time constraints.

_Output_
To analyze if increased student activity leads to better outcomes, we need to first ground our analysis using <LastSubmissionURL>, <MilestoneStatus>, and <ActiveStatus> from StudentProgress as activity indicators,
linking to outcome metrics like <CareerOutcome>, <SalaryIncrease>, and <NPSScore> from LearnerSuccess through the common <LearnerUUID> identifier.
Given so many options, we will need clarification on which combination of indicators is used to define 'activity', and also how to measure which outcomes are 'better'.
Since the conversation previously referenced the last cohort, we should consider using <CohortStartDate> and <CohortEndDate> to place constraints on the time period for analysis.
For staging, we should consider creating intermediate metrics that aggregate activity levels per student while accounting for different <PreReqSkills> and <DifficultyLevel> variations across courses,
as these could confound our analysis. Several critical pieces remain ambiguous and require user input: whether to control for factors like <TechStackJSON>, whether to segment by <DeliveryFormat>,
and how exactly should we determine what constitutes a 'better' outcome?

## Validating Accuracy
For our next scenario, suppose the tables and columns are:
* Tables: tbl_customer_master, mkt_campaigns_v2, inv_items_current, promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Conversation History_
User: Yes, you can use total revenue from the inventory items to measure the return. Just use the time range to join against the campaign data.
Agent: Ok, I will look back on the last quarter of inventory sold to calculate the return. Moving onto 'ad spend', should I be aggregating the 'budget_spent' column for the same time period?
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.

In this case, the user has already provided some context, so our focus should shift to identifying the intermediate metrics and confirming the proposed plan or grouping.
We should also surface any potential branching logic that may benefit from user input.

_Output_
To calculate promotional ROAS, we'll need to calulate return and ad spend as intermediate values, and possibly create staging tables to stitch these together.
As hinted at in the conversation, we can ground the 'return' derivation by pulling from <price_current> * <qty_on_hand> in inv_items_current,
while 'ad spend' should combine <budget_spent> from mkt_campaigns_v2 and <discount_amt> from promo_discount_rules for the last quarter.
The time period can be determined by aligning <dt_start>/<dt_end> with <valid_from>/<valid_to>, but inv_items_current lacks a clear time indicator, so that still needs clarification.
It's possible to group by <campaign_id>, segment by <channel_type>, or apply the <segment_code>, so we should confirm if any of these groupings are necessary.
Many other factors remain unclear, such as whether to consider <discontinued_flag> for calculations or if <excluded_items> should be excluded, which may introduce branching logic.

## Capturing Details
In our third scenario, suppose the tables and columns are:
* Tables: MarketingQualifiedLeads, ProductReleases, Subscriptions, RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, LastTouchpoint in MarketingQualifiedLeads;
ReleaseID, VersionNumber, EngineVersion, AIModelType, MaxResolution, NewFeaturesList, StabilityScore, BetaTestResults, DocumentationURL, ReleaseChannel in ProductReleases;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
RenderID, ClientUUID, ProjectName, RenderType, StartTimestamp, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Conversation History_
User: Show me the average processing time for all renders that were completed last month.
Agent: No problem, the average processing time for completed renders is mostly around 12 minutes. Please see the table for more.
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would consider the processing time broken down by render type. We can also check if there's any correlation with the new features list and beta test results.

Based on the example conversation, the user has given quite a few details so our job is to accurately record these points to guide our analysis.
Even with this much information, we should still be on the lookout for any ambiguities or missing pieces that could impact the final result.

_Output_
To investigate processing time spikes, we can ground our analysis by joining <ProcessingTime> and <RenderType> from RenderActivity with ProductReleases using <EngineVersion>,
focusing on records where <RenderStatus> indicates completion and filtering to recent <StartTimestamp> values that align with the reported spike.
The user directly mentioned cross-referencing <ProcessingTime> against <NewFeaturesList> and <BetaTestResults> from ProductReleases, so these should be calculated and staged as intermediate metrics.
We can also consider <StabilityScore> as a potential indicator, and grouping by <ClientUUID> or <ProjectName> to identify specific clients or projects that may isolate the cause of the spike.
Several aspects need more specificatio: the exact date range of "a few days ago," whether to consider <QueuePriority> or <PlanTier>, or whether the <ErrorTrace> entries are worth investigating.
While filtering by <StartTimestamp> is likely, we should be making assumptions about the user's intent, so this could be a clarified as well.

## Handling Ambiguity
For our final example, suppose the tables and columns are:
* Tables: booking_requests_v3, discount_programs_active, assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. My next question is, what is a reasonable baseline amount that you expect for a payment? When would you consider someone to be paying a lot or a little?
User: I guess 100 to 200 is normal, anything above $500 is over

Despite the lengthy back-and-forth between the speakers in the example, there is still some ambiguity that needs to be addressed.
Our thought process should help us break through the areas of confusion so we can develop a clear plan moving forward.

_Output_
To analyze patterns among repeat ticket issuers, we'll ground our analysis by joining assistance_tickets_main to booking_requests_v3 through <customer_book_id>,
filtering where ticket count per <pax_uuid> is greater than one and focusing on their <payment_amt> patterns (with clear thresholds of $100-200 as normal, >$500 as high).
We also have not been given a specific time period for analysis, so we should clarify whether <created_ts> or <valid_from_dt> should be used to constrain the data.
For staging, we'll need to create intermediate aggregations that group by passenger to show their ticket frequency, average <payment_amt>, common <pickup_addr> locations,
and <satisfaction_rating> trends, possibly segmented by <issue_category_main>. Several aspects remain unclear: whether to include tickets where <medical_emergency_flag>
is true since these might skew the analysis, if <severity_level> should influence how we count tickets, whether to consider <compliance_review_flag> in our filtering.

---
For our real case, the available data includes:
{valid_tab_col}
The currently active table is '{current}'.

_Conversation History_
{history}

Now it's your turn! Please generate a thorough thought process that hits upon (1) grounding, (2) staging, and (3) disambiguation.
Remember to use angle brackets (< and >) to denote column names, and to keep your response concise.

_Output_
"""

pivot_flow_prompt = """We define a pivot table as a query that (1) includes at least one grouping (2) requires multiple aggregations and (3) results in a staging table containing at least three columns.
Given the conversation history, start by thinking precisely about what variables are needed for the pivot table and how they can be calculated from the available data.
Then, choosing only from valid tables and columns, decide which columns are most relevant for writing a SQL query. 
If the table is unclear, then the currently active table of '{current}' is a safe default. On the other hand, if the columns are unclear, then mark as 'unsure' rather than making assumptions.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), where each item in the list should be a dict with tab (string) and col (string) keys.
Do not provide any further text or explanations after the JSON output.

For example,
---
Suppose the valid tables and columns are:
* Tables: CustomerContact, CustomerOrders, MarketingOffers
* Columns: CustomerID, CustomerName, FavCuisineType, EmailAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustomerContact;
OrderID, CustomerID, OrderDate, TotalAmount, ItemCount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustomerOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurant, RedemptionCode in MarketingOffers

_Conversation History_
User: ok, let's also check for the SEND40OFF code
Agent: The SEND40OFF redemption code was used by 613 customers.
User: How often did each customer use the code and how much was their order amount?

_Output_
```json
{{
  "thought": "The query can group customers with CustomerID, and including customer name will make the data more readable. I will also need to aggregate the total order amount and code usage. Joining can be done using OrderID and OrderKey.",
  "result": [
    {{"tab": "CustomerContact", "col": "CustomerID"}},
    {{"tab": "CustomerContact", "col": "CustomerName"}},
    {{"tab": "CustomerOrders", "col": "OrderID"}},
    {{"tab": "CustomerOrders", "col": "CustomerID"}},
    {{"tab": "CustomerOrders", "col": "TotalAmount"}},
    {{"tab": "MarketingOffers", "col": "OfferID"}},
    {{"tab": "MarketingOffers", "col": "OrderKey"}},
    {{"tab": "MarketingOffers", "col": "RedemptionCode"}}
  ]
}}
```

Moving onto the next example, suppose the tables and columns are:
* Tables: mailchimp_download, sub_aggregated_stripe, app_annie_analytics_Sept
* Columns: mc_id, email_address, is_subscribed, clicks, open_rate, domain, open_timestamp, delivered in mailchimp_download;
subscription_id, actitity_type, activity_cleaned, activity_timestamp, address, phone number, refund_amount, discount_applied in sub_aggregated_stripe;
view_id, visit_date, page_views, downloads, installs, uninstalls, email, num_clicks, time_in_app, time_in_hours in app_annie_analytics_Sept;

_Conversation History_
User: Do subscriptions actually lead to conversions later on?
Agent: What counts as a conversion?
User: Let's look at both downloads and installations.

_Output_
```json
{{
  "thought": "The query likely groups by subscriber, which is captured by subscription_id. I will then count the total downloads and installs. Joining can possibly be done using email_address and email.",
  "result": [
    {{"tab": "mailchimp_download", "col": "email_address"}},
    {{"tab": "mailchimp_download", "col": "is_subscribed"}},
    {{"tab": "app_annie_analytics_Sept", "col": "downloads"}},
    {{"tab": "app_annie_analytics_Sept", "col": "installs"}},
    {{"tab": "app_annie_analytics_Sept", "col": "email"}}
  ]
}}
```

In our third example, the valid options are:
* Tables: AdobeAnalytics final; Subscription Membership; Canva Content (revised); Vendor Expenses
* Columns: campaign_id, ad_platform, ad_spend, ad_type, ad_copy, user_activity, conversion_count in AdobeAnalytics final;
member_id, subscription_date, renewal_date, subscription_tier, monthly_fee, activity, member_status in Subscription Membership;
video_id, trainer_id, video_campaign_id, creation_date, video_type, trainer_fee, view_count in Canva Content (revised);
vendor_id, service_provided, expense_date, expense_amount, vendor_category in Vendor Expenses

_Conversation History_
User: what's the total view count for each video type?
Agent: The Pump It Up series recieved 27,204 views followed by 'Get Fit Now' with 24,511 views. See table for more.
User: What if we looked at the average per day for each series?

_Output_
```json
{{
  "thought": "We should group by video type and by date. We will also calculate the average view count. Joining is not needed.",
  "result": [
    {{"tab": "Canva Content (revised)", "col": "video_type"}},
    {{"tab": "Canva Content (revised)", "col": "creation_date"}},
    {{"tab": "Canva Content (revised)", "col": "view_count"}}
  ]
}}
```

Now suppose the tables and columns are:
* Tables: CodePathCourses, StudentProgress, LearnerSuccess
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity in CodePathCourses;
ProgressID, CourseID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CareerOutcome, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, ReferralSource, RetentionStatus, CompanyPlaced, ShowcasePermission in LearnerSuccess;

_Conversation History_
User: What are the different type of tech stacks offered?
Agent: There seem to 23 different options, with the most popular being Python Flask, Python Django, and JavaScript React.
User: How many companies are using Flask and Django and how much are they making?

_Output_
```json
{{
  "thought": "The query groups by company and filters by tech stack. We then count the number of companies and sum the salary increase. However, it's unclear how to join CodePathCourses and LearnerSuccess.",
  "result": [
    {{"tab": "CodePathCourses", "col": "TechStackJSON"}},
    {{"tab": "CodePathCourses", "col": "unsure"}},
    {{"tab": "LearnerSuccess", "col": "CompanyPlaced"}},
    {{"tab": "LearnerSuccess", "col": "SalaryIncrease"}}
  ]
}}
```

For our fifth example, suppose the tables and columns are:
* Tables: qualifiedLeads, userActivityLogs, subscriptionsAnnual, verifiedDataSources
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in qualifiedLeads;
activity_id, user_id, launch_id, activity_type, timestamp, duration, data_source, outcome, error_log in userActivityLogs;
subscription_id, lead_id, plan_name, sub_timestamp, billing_cycle, payment_amount, renewal_notice in subscriptionsAnnual;
launch_id, is_secure, provenance, version, features, documentation_link in verifiedDataSources

_Conversation History_
User: Which month gave us the best conversions to paid plans?
Agent: I'm sorry, how should I define a paid plan? Also, how should I define a conversion?
User: Anything that is attached to a payment amount greater than $0 will work. Any subscription counts as a conversion.

_Output_
```json
{{
  "thought": "We can group by month, filter for payment amounts greater than $0, and count the number of subscriptions based on unique subscription IDs. I should also add the plan name to make the output more readable.",
  "result": [
    {{"tab": "subscriptionsAnnual", "col": "subscription_id"}},
    {{"tab": "subscriptionsAnnual", "col": "plan_name"}},
    {{"tab": "subscriptionsAnnual", "col": "sub_timestamp"}},
    {{"tab": "subscriptionsAnnual", "col": "payment_amount"}}
  ]
}}
```

For our final example, suppose the tables and columns are:
* Tables: ride_bookings, active_promos, customer_support
* Columns: ride_id, customer_id, vehicle_id, promo_code_applied, start_time, end_time, pickup_location, dropoff_location, booking_status, total_amount, payment_status in ride_bookings;
promotion_id, discount_amount, applicable_vehicle_types, terms_conditions, redemption_count, application_date in active_promos;
ticket_id, customer_id, issue_date, issue_type, issue_description, assigned_agent, resolution_status, resolution_date, feedback, follow_up_required in customer_support

_Conversation History_
User: What percentage of people who recieved a promo code actually used it?
Agent: Comparing the redemption count to the number of people who received the code results in a 23.6% conversion rate.
User: What ratio of rides are taken using a promo code? Can you break it down by date?
Agent: How far back do you want to look?
User: Let's look at the last three months. Also, what ratio of rides result in a suport ticket being filed?

_Output_
```json
{{
  "thought": "We should group by date, and then compare the total number of rides to the count of tickets and the count of promo codes applied.  We will also need to filter for rides within the last three months. We can join the tables using customer_id.",
  "result": [
    {{"tab": "ride_bookings", "col": "ride_id"}},
    {{"tab": "ride_bookings", "col": "customer_id"}},
    {{"tab": "ride_bookings", "col": "promo_code_applied"}},
    {{"tab": "ride_bookings", "col": "start_time"}},
    {{"tab": "customer_support", "col": "ticket_id"}},
    {{"tab": "customer_support", "col": "customer_id"}}
  ]
}}
```
---
Now it's your turn! Focusing only on the user's final utterance, please decide on the relevant list of tabs and cols for staging the pivot table.
Do not concern yourself with merging, cleaning, or any other actions since they are out of scope. For reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

describe_flow_prompt = """Given the conversation history and the current table, our task is to figure out which tables and columns the user is requesting information about.
Start by constructing a concise thought concerning what tables or columns are being referenced. If it is unclear, then output an empty list.
Use the current table as a default if no other tables are mentioned. In the spirit of SQL queries, use a '*' to indicate all columns in a table.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list) where each item is a dict with 'tab' and 'col' keys. Do not provide any further explanations after the JSON output.
We will go through a few examples using two example spreadsheets, and then tackle the current case.

For example,
#############
Suppose the spreadsheet contains the following:
* Tables: qualified_leads, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in qualified_leads;
subscription_id, user_id, subscription_name, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity
* Current table: subscriptions

_Conversation History_
User: What sort of data is in the subscriptions table?
Agent: The subscriptions table contains information about user subscriptions, including subscription name and billing cycle.
User: How many unique plans are there?

_Output_
```json
{{
  "thought": "The number of unique plans most likely depends on the plan_name column.",
  "result": [
    {{ "tab": "subscriptions", "col": "plan_name" }}
  ]
}}
```

_Conversation History_
User: Do we have any time series data within subscriptions?
Agent: Yes, the sub_timestamp column in subscriptions contains time series data.
User: What about within the leads table?

_Output_
```json
{{
  "thought": "Contact date and follow-up date are relevant time series data.",
  "result": [
    {{ "tab": "qualified_leads", "col": "contact_date" }},
    {{ "tab": "qualified_leads", "col": "follow_up_date" }}
  ]
}}
```

_Conversation History_
User: Is there any data about error rates connected to user sign-ups?

_Output_
```json
{{
  "thought": "Cross-referencing activity type and error log columns might provide information about error rates.",
  "result": [
    {{ "tab": "user_activity", "col": "activity_type" }},
    {{ "tab": "user_activity", "col": "error_log" }}
  ]
}}
```
#############
Alternatively, suppose the spreadsheet contained the following:
* Tables: Bookings, Promotions, CustomerSupport
* Columns: BookingID, CustomerID, VehicleID, StartDate, EndDate, PickupLocation, DropoffLocation, BookingStatus, TotalAmount, PaymentStatus in Bookings;
PromotionID, DiscountAmount, ApplicationDate, ApplicableVehicleTypes, TermsConditions, RedemptionCount in Promotions;
TicketID, CustomerID, IssueDate, IssueType, IssueDescription, AssignedAgent, ResolutionStatus, ResolutionDate, Feedback, FollowUpRequired in CustomerSupport
* Current table: CustomerSupport

_Conversation History_
User: How big is this table?

_Output_
```json
{{
  "thought": "When the user mentions 'this', they are likely referring to the current table.",
  "result": [
    {{ "tab": "CustomerSupport", "col": "*" }}
  ]
}}
```

_Conversation History_
User: How many redemptions have we received in the past week?
Agent: There have been 23 redemptions in the past week.
User: That's not as many as I expected. Is there anything wrong with the tracking pixel?

_Output_
```json
{{
  "thought": "The final utterance doesn't seem to reference any tables or columns.",
  "result": [ ]
}}
```

_Conversation History_
User: So how many rows are in the Bookings table?
Agent: The Bookings table contains 1,207 rows.
User: Do we have any data related to revenue?

_Output_
```json
{{
  "thought": "The user is likely looking for the total amount column in the Bookings table.",
  "result": [
    {{ "tab": "Bookings", "col": "TotalAmount" }}
  ]
}}
```
#############
Now it's your turn! Remember, you can only choose from valid tables and columns. For our current case, the valid options are:
{valid_tab_col}
* Current table: {current}

_Conversation History_
{history}

_Output_
"""

check_existence_prompt = """Given the conversation history and the current table, our task is to figure out which tables and columns the user is requesting information about.
Start by constructing a concise thought concerning what tables or columns are being referenced. If it is unclear, then output an empty list.
Use the current table as a default if no other tables are mentioned. In the spirit of SQL queries, use a '*' to indicate all columns in a table.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict with 'tab' and 'col' keys. Do not provide any further explanations after the JSON output.
We will go through a few examples using two example spreadsheets, and then tackle the current case.

For example,
#############
Suppose the spreadsheet contains the following:
* Tables: qualified_leads, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in qualified_leads;
subscription_id, user_id, subscription_name, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity
* Current table: subscriptions

_Conversation History_
User: What sort of data is in the subscriptions table?
Agent: The subscriptions table contains information about user subscriptions, including subscription name and billing cycle.
User: How many unique plans are there?

_Output_
```json
{{
  "thought": "The number of unique plans most likely depends on the plan_name column.",
  "result": [
    {{ "tab": "subscriptions", "col": "plan_name" }}
  ]
}}
```

_Conversation History_
User: Do we have any time series data within subscriptions?
Agent: Yes, the sub_timestamp column in subscriptions contains time series data.
User: What about within the leads table?

_Output_
```json
{{
  "thought": "Contact date and follow-up date are relevant time series data.",
  "result": [
    {{ "tab": "qualified_leads", "col": "contact_date" }},
    {{ "tab": "qualified_leads", "col": "follow_up_date" }}
  ]
}}
```

_Conversation History_
User: Is there any data about error rates connected to user sign-ups?

_Output_
```json
{{
  "thought": "Cross-referencing activity type and error log columns might provide information about error rates.",
  "result": [
    {{ "tab": "user_activity", "col": "activity_type" }},
    {{ "tab": "user_activity", "col": "error_log" }}
  ]
}}
```
#############
Alternatively, suppose the spreadsheet contained the following:
* Tables: Bookings, Promotions, CustomerSupport
* Columns: BookingID, CustomerID, VehicleID, StartDate, EndDate, PickupLocation, DropoffLocation, BookingStatus, TotalAmount, PaymentStatus in Bookings;
PromotionID, DiscountAmount, ApplicationDate, ApplicableVehicleTypes, TermsConditions, RedemptionCount in Promotions;
TicketID, CustomerID, IssueDate, IssueType, IssueDescription, AssignedAgent, ResolutionStatus, ResolutionDate, Feedback, FollowUpRequired in CustomerSupport
* Current table: CustomerSupport

_Conversation History_
User: How big is this table?

_Output_
```json
{{
  "thought": "When the user mentions 'this', they are likely referring to the current table.",
  "result": [
    {{ "tab": "CustomerSupport", "col": "*" }}
  ]
}}
```

_Conversation History_
User: How many redemptions have we received in the past week?
Agent: There have been 23 redemptions in the past week.
User: That's not as many as I expected. Is there anything wrong with the tracking pixel?

_Output_
```json
{{
  "thought": "The final utterance doesn't seem to reference any tables or columns.",
  "result": [ ]
}}
```

_Conversation History_
User: So how many rows are in the Bookings table?
Agent: The Bookings table contains 1,207 rows.
User: Do we have any data related to revenue?

_Output_
```json
{{
  "thought": "The user is likely looking for the total amount column in the Bookings table.",
  "result": [
    {{ "tab": "Bookings", "col": "TotalAmount" }}
  ]
}}
```
#############
Now it's your turn! Remember, you can only choose from valid tables and columns. For our current case, the valid options are:
{valid_tab_col}
* Current table: {current}

_Conversation History_
{history}

_Output_
"""

recommend_flow_prompt = """Given the valid tables and columns along with the conversation history, your task is to propose some courses of action for the user to choose from.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

After the thought, your entire response should be in well-formatted JSON list, with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

inform_metric_prompt = """Given the valid tables and columns along with the conversation history, your task is to provide information on how a particular metric is calculated.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

After the thought, your entire response should be in well-formatted JSON list, with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

define_metric_prompt = """Given the valid tables and columns along with the conversation history, your task is to define a metric based on a formula and save it as a user preference.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

After the thought, your entire response should be in well-formatted JSON list, with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""