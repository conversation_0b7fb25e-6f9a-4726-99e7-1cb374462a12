<svelte:head>
  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-NSXZF342');</script>
  <!-- End Google Tag Manager -->
  <script src="https://cdn.amplitude.com/script/fc5b3464f5c65d952315a611b9757c05.js"></script>
  <script>
    window.amplitude.init('fc5b3464f5c65d952315a611b9757c05', {
      fetchRemoteConfig: true,
      autocapture: true
    });
    window.amplitude.add(window.sessionReplay.plugin({sampleRate: 1}));
  </script>

</svelte:head>