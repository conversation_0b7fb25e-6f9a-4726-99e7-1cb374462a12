import pdb
from faker import Faker
import numpy as np
import pandas as pd
import json
import random
from collections import Counter
from tqdm import tqdm as progress_bar

# fake = Faker()
vendor = 'zendesk'
# df = pd.read_csv('updated_combined.csv')
df = pd.read_csv(f'{vendor}.csv')
used_first = Counter()
used_last = Counter()

def sample_name(diverse_names):
  # sample an origin according to the probabilities
  origins = ['european', 'latino', 'asian', 'black', 'white']
  probabilities = [0.15, 0.15, 0.15, 0.1, 0.45]
  selected_origin = np.random.choice(origins, p=probabilities)
  current_names = diverse_names[selected_origin]
  fname = random.choice(current_names['first'])

  # add back in all the other origins with a lower probability
  new_origins = [selected_origin]
  new_origins.extend([o for o in origins if o != selected_origin])
  reweighted = [0.9, 0.04, 0.03, 0.02, 0.01]
  selected_origin = np.random.choice(new_origins, p=reweighted)
  current_names = diverse_names[selected_origin]
  lname = random.choice(current_names['last'])

  return fname, lname

def naming_loop():
  diverse_names = json.load(open('diverse_names.json', 'r'))
  
  for index, row in progress_bar(df.iterrows(), total=len(df.index)):
    fname, lname = sample_name(diverse_names)

    if lname in fname:
      fname, lname = sample_name(diverse_names)
    if used_first[fname] > 2:
      fname, lname = sample_name(diverse_names)
    if used_last[lname] > 2:
      fname, lname = sample_name(diverse_names)

    df.at[index, 'first'] = fname
    df.at[index, 'last'] = lname  

    used_first[fname] += 1
    used_last[lname] += 1

    if row['is_customer']:
      # With a 30% chance, change the value to False instead
      if random.random() < 0.3:
        df.at[index, 'is_customer'] = False

def stage_loop(index, row):
  collection = []
  if row[vendor]:
    if row['is_customer']:
      selected_stage = random.choice(['Closed won', 'Purchased'])
    else:
      selected_stage = random.choice(['Qualified', 'Prospecting', 'Negotiation', 'Closed lost'])
    combo = {'gid': row['global_id'], 'stage': selected_stage}
    collection.append(combo)

  # shuffle the list
  random.shuffle(collection)
  vendor_df = pd.DataFrame(collection)
  return vendor_df


canadian = ['Toronto', 'Montreal', 'Calgary', 'Ottawa', 'Edmonton', 'Mississauga', 'Winnipeg']
american = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 
          'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus',
          'Charlotte', 'San Francisco', 'Indianapolis', 'Seattle', 'Denver', 'Washington', 'Boston',
          'El Paso', 'Nashville', 'Detroit', 'Portland', 'Memphis', 'Oklahoma City', 'Las Vegas',
          'Louisville', 'Baltimore', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento',
          'Mesa', 'Kansas City', 'Atlanta', 'Long Beach', 'Colorado Springs', 'Raleigh', 'Miami',
          'Virginia Beach', 'Omaha', 'Oakland', 'Minneapolis', 'Tulsa', 'Arlington', 'New Orleans',
          'Wichita', 'Cleveland', 'Tampa', 'Bakersfield', 'Aurora', 'Honolulu', 'Anaheim', 'Santa Ana',
          'Corpus Christi', 'Riverside', 'St. Louis', 'Lexington', 'Stockton', 'Pittsburgh',
          'Saint Paul', 'Cincinnati', 'Anchorage', 'Henderson', 'Greensboro', 'Plano', 'Newark',
          'Lincoln', 'Toledo', 'Orlando', 'Chula Vista', 'Irvine', 'Fort Wayne', 'Jersey City',
          'Durham', 'St. Petersburg', 'Laredo', 'Buffalo', 'Madison', 'Lubbock', 'Chandler',
          'Scottsdale', 'Reno', 'Glendale', 'Gilbert', 'Winston–Salem', 'North Las Vegas',
          'Norfolk', 'Chesapeake', 'Garland', 'Irving', 'Hialeah', 'Fremont', 'Boise', 'Richmond']
states = ['New York', 'California', 'Illinois', 'Texas', 'Arizona', 'Pennsylvania', 'Texas',
          'California', 'Texas', 'California', 'Texas', 'Mississippi', 'Texas', 'Ohio',
          'North Carolina', 'California', 'Indiana', 'Washington', 'Colorado', 'District of Columbia', 'Massachusetts',
          'Texas', 'Tennessee', 'Michigan', 'Oregon', 'Tennessee', 'Oklahoma', 'Nevada',
          'Kentucky', 'Maryland', 'Wisconsin', 'New Mexico', 'Arizona', 'California', 'California',
          'Arizona', 'Missouri', 'Georgia', 'California', 'Colorado', 'North Carolina', 'Florida',
          'Virginia', 'Nebraska', 'California', 'Minnesota', 'Oklahoma', 'Texas', 'Louisiana',
          'Kansas', 'Ohio', 'Florida', 'California', 'Colorado', 'Hawaii', 'California', 'California',
          'Texas', 'California', 'Missouri', 'Kentucky', 'California', 'Pennsylvania',
          'Minnesota', 'Ohio', 'Alaska', 'Nevada', 'North Carolina', 'Texas', 'New Jersey',
          'Nebraska', 'Ohio', 'Florida', 'California', 'California', 'Indiana', 'New Jersey',
          'North Carolina', 'Florida', 'Texas', 'New York', 'Wisconsin', 'Texas', 'Arizona',
          'Arizona', 'Nevada', 'California', 'Arizona', 'North Carolina', 'Nevada',
          'Virginia', 'Virginia', 'Texas', 'Texas', 'Florida', 'California', 'Idaho', 'Virginia']

def location_loop(index, row, df):    
  if random.random() > 0.94:
    country = 'Canada'
  else:
    if random.random() > 0.8:
      country = 'United States'
    else:
      country = 'USA'

  if country == 'Canada':
    state = 'N/A'
    city = random.choice(canadian)
  else:
    # select a random index from 0 to 97
    location_index = random.randint(0, 97)
    state = states[location_index]
    city = american[location_index]

  df.at[index, 'Location (city)'] = city
  df.at[index, 'Location (state)'] = state
  df.at[index, 'Location (country)'] = country
  return df

def decision_maker_loop(index, row, df):
  if row['Stage'] in ['Qualified', 'Prospecting']:
    df.at[index, 'DecisionMaker'] = random.choice(['Yes', 'No'])
  elif row['Stage'] == 'Negotiation':
    df.at[index, 'DecisionMaker'] = np.random.choice(['Yes', 'No'], p=[0.8, 0.2])
  else:
    df.at[index, 'DecisionMaker'] = 'Yes'
  return df

def next_step_loop(index, row, df):
  stage = row['Stage']
  valid_steps = {
    'Qualified': [('Demo', 0.9), ('Negotiation', 0.1)],
    'Prospecting': [('Demo', 0.4), ('Negotiation', 0.1), ('Setup Meeting', 0.3), ('Meeting', 0.2)],
    'Negotiation': [('Pricing Discussion', 0.3), ('Pricing', 0.2), ('Contract', 0.1), ('Contract Review', 0.4)],
    'Closed won': [('Purchased', 0.95), ('Pricing', 0.05)],
    'Purchased': [('Installation', 0.7), ('N/A', 0.2), ('Follow-up', 0.1)],
    'Closed lost': [('N/A', 0.8), ('Follow-up', 0.2)]
  }

  steps = valid_steps[stage]
  nextstep = random.choices(steps, weights=[s[1] for s in steps])[0]
  df.at[index, 'NextStep'] = nextstep[0]
  return df

def last_contact_date_loop(index, row, df):
  # generate a random date between February 1, 2023 and today
  start_date = pd.to_datetime('2023-02-01')
  end_date = pd.to_datetime('2023-12-23')
  random_date = start_date + (end_date - start_date) * random.random()

  df.at[index, 'LastContactDate'] = random_date.strftime('%Y-%m-%d')
  return df

def deal_size_loop(index, row, df):
  if row['Stage'] == 'Qualified':
    size_num = random.choice([500, 600, 700, 800, 900, 1000])
  else:
    # choose a random value between 500 and 8000
    base = random.randint(500, 8000)
    # round to the nearst 50 dollars
    size_num = base - (base % 50)

  df.at[index, 'DealSize'] = f'${size_num}'
  return df

def opportunity_id_loop(index, row, df):
  oid = row['OpportunityID']
  # if oid is null
  if pd.isnull(oid):
    raw_id = random.randint(950, 7000)
  else:
    raw_id = oid[1:]

  df.at[index, 'OpportunityID'] = f'OPP-{raw_id}'
  return df

def email_address_loop(index, row, df):
  first = row['FirstName']
  last = row['LastName']
  providers = ["gmail", "yahoo", "outlook", "hotmail", "example"]

  ran = random.random()
  if ran < 0.3:
    handle = f'{first}{last}'.lower()
  elif ran < 0.5:
    # first initial, last name
    handle = f'{first[0]}{last}'.lower()
  elif ran < 0.7:
    # first name, last initial
    handle = f'{first}{last[0]}'.lower()
  elif ran < 0.9:
    # first name, last initial
    handle = f'{last}{first}'.lower()
  else:
    special_words = fake.words(nb=2, unique=True)
    handle = f'{special_words[0]}{special_words[1]}'.lower()

  if len(handle) <= 5:
    suffix = random.randint(10, 799)
  else:
    if random.random() < 0.5:
      suffix = random.randint(1, 199)
    else:
      suffix = ''

  df.at[index, 'EmailAddress'] = f'{handle}{suffix}@{random.choice(providers)}.com'
  return df

def date_time_joined_loop(index, row, df):
  # get the value at index row and LastContactDate column
  last_contact = df.loc[index, 'LastContactDate']
  # generate a random date between February 1, 2023 and today
  start_date = pd.to_datetime('2022-11-11')
  end_date = pd.to_datetime(last_contact)
  random_date = start_date + (end_date - start_date) * random.random()
  df.at[index, 'DateTimeJoined'] = random_date.strftime('%Y-%m-%d %H:%M:%S')
  return df

def downloaded_content_loop(index, row, df):
  if row['Stage'] == 'skip':
    downloaded_content = np.random.choice(['Yes', 'No'], p=[0.2, 0.8])
  elif row['Stage'] == 'Qualified':
    downloaded_content = np.random.choice(['Yes', 'No'], p=[0.4, 0.6])
  elif row['Stage'] == 'Prospecting':
    downloaded_content = np.random.choice(['Yes', 'No'], p=[0.6, 0.4])
  else:
    downloaded_content = np.random.choice(['Yes', 'No'], p=[0.8, 0.2])
  
  df.at[index, 'DownloadedContent'] = downloaded_content
  return df

def form_submitted_loop(index, row, df):
  if row['Stage'] in ['Qualified', 'skip']:
    form_submitted = random.choice([True, False])
  elif row['Stage'] == 'Prospecting':
    form_submitted = np.random.choice([True, False], p=[0.7, 0.3])
  else:
    form_submitted = np.random.choice([True, False], p=[0.9, 0.1])
  
  df.at[index, 'FormSubmitted'] = form_submitted
  return df

def form_submission_date_time_loop(index, row, df):
  dtj = df.at[index, 'DateTimeJoined']
  if dtj == 'skip':
    start_date = pd.to_datetime('2022-11-11')
    end_date = pd.to_datetime('2023-11-11')
    random_date = start_date + (end_date - start_date) * random.random()
    dtj = random_date.strftime('%Y-%m-%d %H:%M:%S')

  df.at[index, 'FormSubmissionDateTime'] = dtj if df.at[index, 'FormSubmitted'] else 'N/A'
  return df

def visit_counts_loop(index, row, df):
  # randomly sampled poisson distribution of visit counts
  if row['Stage'] == 'Qualified':
    visit_counts = np.random.poisson(lam=1.5)
  elif row['Stage'] == 'Prospecting':
    visit_counts = np.random.poisson(lam=2)
  else:
    visit_counts = np.random.poisson(lam=4)
  
  df.at[index, 'VisitCounts'] = visit_counts
  return df

def first_visit_time_loop(index, row, df):
  fsdt = df.at[index, 'FormSubmissionDateTime']
  if fsdt == 'N/A':
    joined = pd.to_datetime('2023-09-08')
  else:
    joined = pd.to_datetime(fsdt)

  if random.random() < 0.3:
    random_date = joined
  else:   # add 0 to 7 days to the joined date
    start_date = joined + pd.Timedelta(days=0)
    end_date = joined + pd.Timedelta(days=7)
    random_date = joined + (end_date - start_date) * random.random()
  
  df.at[index, 'FirstVisitTime'] = random_date.strftime('%Y-%m-%d %H:%M:%S')
  return df

def page_visited_loop(index, row, df):
  pages = ['Home', 'Pricing', 'About', 'Blog', 'Features', 'FAQ', 'Helpdesk', 'Contact']
  if row['zendesk']:
    page_visited = random.choice(['Helpdesk', 'FAQ'])
  else:
    page_visited = random.choice(pages)

  df.at[index, 'PageVisited'] = page_visited
  return df

def lead_score_loop(index, row, df):
  row = df.loc[index]

  if row['Stage'] == 'Qualified':
    lead_score = 10
  elif row['Stage'] == 'Prospecting':
    lead_score = 20
  else:
    lead_score = 30

  if row['DownloadedContent'] == 'Yes':
    lead_score += random.randint(10, 20)
  if row['FormSubmitted']:
    lead_score += random.randint(15, 25)
  if row['VisitCounts'] > 2:
    lead_score += random.randint(10, 20)

  df.at[index, 'LeadScore'] = min(100, lead_score)
  return df

def source_loop(index, row, df):
  sources = ['organic', 'referral', 'direct', 'social media', 'social', 'paid ad', 'paid', 'none']
  probabilities = [0.25, 0.2, 0.1, 0.1, 0.05, 0.2, 0.07, 0.03]
  source = np.random.choice(sources, p=probabilities)
  df.at[index, 'Source'] = source
  return df

def clicked_link_loop(index, row, df):
  page = df.at[index, 'PageVisited']
  prefix = 'www.nexstream.com'

  if page in ['Home', 'Pricing', 'About', 'Blog', 'Features', 'FAQ']:
    if page == 'Home':
      clicked_link = prefix
    else:
      clicked_link = f'{prefix}/{page.lower()}'
  else:
    clicked_link = 'N/A'
  
  df.at[index, 'Clicked_Link'] = clicked_link
  return df

def list_segment_loop(index, row, df):
  if row['Stage'] in ['Qualified', 'Prospecting']:
    segment = 'prospects'
  elif row['Stage'] == 'Negotiation':
    segment = random.choice(['opportunities', 'prospects'])
  elif row['Stage'] in ['Closed won', 'Purchased']:
    options = ['opportunities', 'customers', 'VIPs', 'VIP']
    probabilities = [0.1, 0.6, 0.2, 0.1]
    segment = np.random.choice(options, p=probabilities)
  else:  # Closed lost
    segment = 'n/a'

  df.at[index, 'List_Segment'] = segment
  return df

def opened_loop(index, row, df):
  clicked = np.random.choice(['Yes', 'No'], p=[0.8, 0.2])
  df.at[index, 'Opened'] = clicked
  return df

def clicked_loop(index, row, df):
  opened = df.at[index, 'Opened']
  if opened == 'Yes':
    clicked = np.random.choice(['Yes', 'No'], p=[0.8, 0.2])
  else:
    clicked = 'No'

  df.at[index, 'Clicked'] = clicked
  return df

def unsubscribed_loop(index, row, df):
  opened = df.at[index, 'Opened']

  if opened == 'Yes':
    df.at[index, 'Unsubscribed'] = random.choice(['Yes', 'No'])
  else:
    df.at[index, 'Unsubscribed'] = 'n/a'
  return df

def campaign_loop(index, row, df):
  options = ['June-A', 'June-B', 'July-A', 'July-B', 'July-C', 'Aug-A', 'Aug-B', 'Aug-C',
             'Sept-A', 'Sept-B', 'Sept-C', 'Sept-D']
  probabilities = [0.1, 0.1,          # June
            0.1, 0.1, 0.1,            # July
            0.1, 0.1, 0.1,            # August
            0.05, 0.05, 0.05, 0.05]    # September
  campaign = np.random.choice(options, p=probabilities)
  month, version = campaign.split('-')
  mapping = {'A': 0, 'B': 1, 'C': 2, 'D': 3}

  date_options = {'Sept': '2023-09-14', 'Aug': '2023-08-16', 'July': '2023-07-15', 'June': '2023-06-14'}
  launch_date = pd.to_datetime(date_options[month])

  campaign_names = {
    'June': ['Summer Special', 'Father\'s Day'],
    'July': ['Independence Day', 'Summer Special', 'Reduced Carbon Footprint'],
    'Aug': ['Anniversary Sale', 'Faster Shipping', 'Back to School'],
    'Sept': ['Back to School', 'Labor Day', 'Autumn Amazement', 'Product Launch']
  }
  campaign_name = campaign_names[month][mapping[version]]

  df.at[index, 'Campaign_ID'] = campaign
  df.at[index, 'Campaign_Name'] = campaign_name
  df.at[index, 'Campaign_Launch_Date'] = launch_date.strftime('%Y-%m-%d')
  return df

def another_email_loop(index, row, df):
  if pd.isnull(row['Email']):
    number = random.choice([2,3])
    special_words = fake.words(nb=number, unique=True)
    if number == 2:
      handle = f'{special_words[0]}{special_words[1]}'.lower()
    else:
      handle = f'{special_words[0]}{special_words[1]}{special_words[2]}'.lower()

    df.at[index, 'Email'] = f'{handle}@company.{random.choice(["com", "org", "net", "ai"])}'
  return df

def resolution_time_loop(index, row, df):
  # sample a time in half hour increments based on poisson distribution
  res_time = np.random.poisson(lam=3) * 0.5
  if res_time == 0:
    res_time = 0.5

  if res_time == 1:
    res_time = '1 hour'
  else:
    res_time = f'{res_time} hours'

  df.at[index, 'ResolutionTime'] = res_time
  return df

def assigned_agent_loop(index, row, df):
  agents = ['John', 'Mary', 'Chris', 'Sarah', 'Kevin', 'Jessica', 'Mike', 'Jennifer', 'David', 'Lisa']
  agent = random.choice(agents)
  df.at[index, 'AssignedAgent'] = agent
  return df

def satisfaction_rating_loop(index, row, df):
  options = ['1', '2', '3', '4', '5']
  probabilities = [0.2, 0.05, 0.1, 0.2, 0.45]
  rating = np.random.choice(options, p=probabilities)
  df.at[index, 'SatisfactionRating'] = rating

  status_options = ['Unresolved', 'Resolved', 'Follow-up']
  if rating in ['1', '2', '3']:
    status_probs = [0.4, 0.5, 0.1]
  else:
    status_probs = [0.1, 0.8, 0.1]
  status = np.random.choice(status_options, p=status_probs)

  df.at[index, 'Status'] = status

  lastcontact = df.at[index, 'LastContactDate']
  if lastcontact == 'skip':
    # generate a random date
    open_date = pd.to_datetime('2022-11-11')
  else:
    open_date = pd.to_datetime(lastcontact)

  # turn date into timestamp by adding random hour/minute/second
  extra_hms = pd.Timedelta(hours=random.randint(0, 23), minutes=random.randint(0, 59), seconds=random.randint(0, 59))
  open_timestamp = open_date + extra_hms
  df.at[index, 'OpenTimestamp'] = open_timestamp.strftime('%Y-%m-%d %H:%M:%S')

  if status == 'Unresolved':
    close_timestamp = 'N/A'
  else:
    extra_hour = random.randint(0, 5)
    extra_min = random.randint(0, 59)
    extra_sec = random.randint(0, 59)
    extra_hms = pd.Timedelta(hours=extra_hour, minutes=extra_min, seconds=extra_sec)
    close_timestamp = open_timestamp + extra_hms
    close_timestamp = close_timestamp.strftime('%Y-%m-%d %H:%M:%S')

  df.at[index, 'ClosedTimestamp'] = close_timestamp
  return df

if __name__ == '__main__':

  for index, row in progress_bar(df.iterrows(), total=len(df.index)):
    df = resolution_time_loop(index, row, df)
    df = assigned_agent_loop(index, row, df)
    df = satisfaction_rating_loop(index, row, df)

  cols_to_shuffle = ['IssueType', 'MessageHeader', 'MessageBody']
  shuffled_index = np.random.permutation(df.index)
  temp_df = df.loc[shuffled_index, cols_to_shuffle].reset_index(drop=True)
  df[cols_to_shuffle] = temp_df

  df.to_csv(f'{vendor}_update.csv', index=False)
  print("finished", vendor)




