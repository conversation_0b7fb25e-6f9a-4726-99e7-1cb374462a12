"""add tables for dialogue state and frame

Revision ID: e299b51da24f
Revises: 31bc4de7b2b1
Create Date: 2025-05-26 18:07:51.699390

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e299b51da24f'
down_revision = '31bc4de7b2b1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dialogue_state',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('utterance_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('intent', sa.String(), nullable=True),
    sa.Column('dax', sa.String(), nullable=True),
    sa.Column('flow_stack', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('source', sa.Enum('nlu', 'pex', name='dialogue_state_source_enum'), nullable=True),
    sa.ForeignKeyConstraint(['utterance_id'], ['utterance.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('frame',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('utterance_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('type', sa.Enum('direct', 'derived', 'dynamic', 'decision', name='frame_type_enum'), nullable=True),
    sa.Column('columns', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('source', sa.Enum('sql', 'pandas', 'plotly', 'interaction', 'default', name='frame_source_enum'), nullable=True),
    sa.Column('code', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['utterance_id'], ['utterance.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_index('idx_user_data_source_user_id', table_name='user_data_source')
    op.create_index(op.f('ix_user_data_source_user_id'), 'user_data_source', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_data_source_user_id'), table_name='user_data_source')
    op.create_index('idx_user_data_source_user_id', 'user_data_source', ['user_id'], unique=False)
    op.drop_table('frame')
    op.drop_table('dialogue_state')
    # ### end Alembic commands ###
