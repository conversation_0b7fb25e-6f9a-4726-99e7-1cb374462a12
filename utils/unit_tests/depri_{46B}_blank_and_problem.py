import sys
import json
import time
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from collections import defaultdict
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.webserver import app
from backend.db import get_db, load_engine
from backend.auth.credentials import get_auth_user_email
from utils.dependencies import get_test_db, load_test_engine

TEST_TOKEN = "secret_acromi_token_for_testing_123"

def get_test_user_email():
  return "<EMAIL>"

@pytest.fixture(scope="module")
def test_client():
  app.dependency_overrides[get_db] = get_test_db
  app.dependency_overrides[load_engine] = load_test_engine
  app.dependency_overrides[get_auth_user_email] = get_test_user_email
  
  client = TestClient(app)
  yield client
  
  app.dependency_overrides.clear()

@pytest.fixture(scope="module")
def websocket(test_client: TestClient):
  with test_client.websocket_connect(f"/api/v1/ws?token={TEST_TOKEN}") as ws:
    yield ws

def test_select_sheet(test_client):
  spreadsheet_data = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"],
  }
  response = test_client.post("/api/v1/sheets/select", json=spreadsheet_data)
  assert response.status_code == 200, f"Failed to select spreadsheet: {response.text}"

def decide_which_parts(parts):
  # determine which parts of the message to keep, returns 3 boolean values
  store_query = 'query' in parts
  store_code = 'code' in parts
  store_frame = 'frame' in parts
  return store_query, store_code, store_frame

def decide_part_availability(parsed):
  # determine which parts are available in the message, returns 3 boolean values
  has_query, has_code, has_frame = False, False, False
  
  if 'interaction' in parsed.keys():
    if parsed['interaction'] and 'content' in parsed['interaction']:
      if 'SQL Query' in parsed['interaction']['content']:
        has_query = True
      if 'Pandas Code' in parsed['interaction']['content']:
        has_code = True
  if 'frame' in parsed.keys() and len(parsed['frame']) > 0:
    if 'tabType' in parsed.keys() and len(parsed['tabType']) > 0:
      has_frame = True

  return has_query, has_code, has_frame

def send_message(websocket, message:str, gold_dax:str, parts:list=[]):
  websocket.send_json( { 'currentMessage': message, 'dialogueAct': gold_dax, 'lastAction': None}, mode='binary')
  store_query, store_code, store_frame = decide_which_parts(parts)
  results = defaultdict(str)

  while True:
    try:
      raw_output = websocket.receive_json()
      
      has_query, has_code, has_frame = decide_part_availability(raw_output)
      if store_query and has_query:
        results['query'] = raw_output['interaction']['content'][19:]
      if store_code and has_code:
        results['code'] = raw_output['interaction']['content'][21:]
      if store_frame and has_frame:
        results['frame'] = raw_output['frame']
        results['tabType'] = raw_output['tabType']
      if 'message' in raw_output.keys():
        results['message'] = raw_output['message']
        return results

    except json.JSONDecodeError as e:
      return f"Failed to decode JSON: {e}"

  return 'Error'

@pytest.mark.timeout(30)
def test_resolve_blanks(websocket):
  turn_one = send_message(websocket, "Are there any people with missing last names?", '46B')
  print("Turn 1:", turn_one)
  assert 'null' in turn_one['message'] or 'empty' in turn_one['message'], 'Should detect a blank'

  turn_two = send_message(websocket, "Let's just delete that person", '007', ['code'])
  print("Turn 2:", turn_two)
  assert 'null' in turn_two['message'] or 'empty' in turn_two['message'], 'Failed to remove user'

@pytest.mark.timeout(30)
def test_resolve_problems(websocket):
  turn_three = send_message(websocket, "What is the average price of shoes sold in 2022?", '001')
  print("Turn 3:", turn_three)
  assert '113' in turn_three['message'], 'Wrong average price'

  turn_four = send_message(websocket, "What do you mean?", '46F', ['frame'])
  print("Turn 4:", turn_four)
  assert 'type' in turn_four['message'], 'Should detect a problem'
  assert turn_four['tabType'] == 'dynamic', 'Should be a dynamic table'

@pytest.mark.timeout(30)
def test_follow_up_problem(websocket):
  turn_five = send_message(websocket, "We can change the first price to $23", '006', ['frame'])
  print("Turn 5:", turn_five)
  assert len(turn_five['frame']['rows']) == 1, 'Should have exactly one problem left'
  assert turn_five['tabType'] == 'dynamic', 'Should be a dynamic table'

  turn_six = send_message(websocket, "We can ignore the last one", '00F', ['frame'])
  print("Turn 6:", turn_six)
  assert 'remaining' not in turn_six['message'], 'Should have no more problems'
  assert 'ignore' in turn_six['message'], 'Should have no more problems'
  assert turn_six['tabType'] == 'direct', 'Should revert back to a direct table'