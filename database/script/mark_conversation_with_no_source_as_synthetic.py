#!/usr/bin/env python
import os
import sys
import argparse
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Setup path and environment variables
DIRNAME = os.path.dirname(__file__)
ENV_PATH = os.path.abspath(os.path.join(DIRNAME, "..", ".env"))
load_dotenv(ENV_PATH)

def get_db_connection(is_production):
    """Create database connection based on environment"""
    if is_production:
        db_dsn = os.getenv("PRODUCTION_DB_DSN")
        env_name = "PRODUCTION"
    else:
        db_dsn = os.getenv("STAGING_DB_DSN")
        env_name = "STAGING"
    
    if not db_dsn:
        raise ValueError(f"{env_name}_DB_DSN environment variable not set")
    
    # Fix DSN format for SQLAlchemy
    db_dsn = db_dsn.replace("postgres://", "postgresql://")
    return create_engine(db_dsn)

def update_conversation_source(is_production, dry_run=False):
    """Update all existing conversations to set source='synthetic'"""
    # Connect to database
    engine = get_db_connection(is_production)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Count how many conversations we'll update (where source is NULL)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM conversation WHERE source IS NULL"))
            count = result.scalar_one()
        
        print(f"Found {count} conversations with null source field in {'PRODUCTION' if is_production else 'STAGING'} database.")
        
        if count == 0:
            print("No conversations need to be updated.")
            return
            
        if dry_run:
            print("DRY RUN: No changes will be made.")
            return
            
        # Update conversations
        # Note: Using bulk update for efficiency with large tables
        session.execute(
            text("UPDATE conversation SET source = 'synthetic' WHERE source IS NULL")
        )
        
        session.commit()
        print(f"Successfully updated {count} conversations to have source='synthetic'")
        
    except Exception as e:
        session.rollback()
        print(f"Error updating conversations: {e}")
        raise
    finally:
        session.close()

def main():
    parser = argparse.ArgumentParser(description='Update existing conversations to set source field to synthetic')
    parser.add_argument('--env', choices=['staging', 'production'], required=True, 
                        help='Environment to run against (staging or production)')
    parser.add_argument('--dry-run', action='store_true', 
                        help='Perform a dry run without making any changes')
    
    args = parser.parse_args()
    
    is_production = args.env == 'production'
    
    # Confirm when running against production
    if is_production and not args.dry_run:
        confirm = input("You are about to modify the PRODUCTION database. Are you sure? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Operation canceled.")
            sys.exit(0)
    
    update_conversation_source(is_production, args.dry_run)

if __name__ == "__main__":
    main()