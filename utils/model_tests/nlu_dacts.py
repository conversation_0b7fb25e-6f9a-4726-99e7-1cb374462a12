# After making changes to this file, run the following command to update the beta test set
# python generate_beta_test_set.py
test_set = [
    {
        "convo_id": "1",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What columns are in the inventory table?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "The inventory table has 11 columns, which are ProductID, ProductName, SupplierID, CategoryID, QuantityPerUnit, UnitPrice, UnitsInStock, UnitsOnOrder, ReorderLevel, Discontinued, and CategoryName.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Great! Just curious, what kinds of analysis can you do?",
                "intent": "Converse",
                "dact": "retrieve",
                "dax": "004",
            },
        ],
    },
    {
        "convo_id": "2",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Which channels had the most pageviews in August?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Among the channels, TikTok has the most pageviews, followed by Google Ads, Google Display, Email and Marchex",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "What are their click-thru rates?",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "3",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Join together the city and state as a new column",
                "intent": "Transform",
                "dact": "insert + column",
                "dax": "05C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, I've added a column for DeliveryLocation.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's also add another column for tracking availability",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "4",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Are there any outliers to watch out for?",
                "intent": "Detect",
                "dact": "retrieve + update + column",
                "dax": "46C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Yes, I found 4 outliers in the PricePerUnit column. See table for details.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you change the cost of the $4350 sheet to $43.50?",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            },
        ],
    },
    {
        "convo_id": "5",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you help me remove test users from the spreadsheet?  Test users are those that don't have any purchases recorded.",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, I've removed the test users.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's split the user's by their name now into first and last name",
                "intent": "Transform",
                "dact": "insert + column + multiple",
                "dax": "5CD",
            },
        ],
    },
    {
        "convo_id": "6",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What was our ad spend on GoogleAds for each week last month?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "01A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Before continuing, I should let you know I found variations such as 'GoogleAds', 'google', 'GOOG', and 'search_google'. Should I group these together first?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, search_google is the right one",
                "intent": "Detect",
                "dact": "retrieve + update + confirm",
                "dax": "46E",
            },
        ],
    },
    {
        "convo_id": "7",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can we take a look at the Company Sector column?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "00C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found two potential concerns in the Company Sector column. Would you like to investigate further?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, let's investigate further.",
                "intent": "Detect",
                "dact": "retrieve + update + column",
                "dax": "46C",
            },
        ],
    },
    {
        "convo_id": "8",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Which products have low levels of stock under 50 units?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found three mixed subtype problems in the UnitsInStock column. Would you like to investigate?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "What sorts of issues?",
                "intent": "Detect",
                "dact": "retrieve + update + deny",
                "dax": "46F",
            },
        ],
    },
    {
        "convo_id": "9",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How many rows of feedback are there in the product survey results?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found 6 empty rows and two null values in the 'Feedback' section of the product survey. Should we take a look before proceeding?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, please show me",
                "intent": "Detect",
                "dact": "retrieve + update + row",
                "dax": "46B",
            },
        ],
    },
    {
        "convo_id": "10",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's move all items with a 4 or 5 star rating into the Approved column",
                "intent": "Transform",
                "dact": "insert + delete",
                "dax": "057",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Ok, I have moved those items over. How does this look?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Looks good, now let's get rid of the SurveyID column",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            },
        ],
    },
    {
        "convo_id": "11",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What are the conversion rates for the active campaigns?",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "The conversion rate will use the PurchaseMade column to count conversions. Is this right?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, that's right.",
                "intent": "Analyze",  # Keep the same intent as the previous turn
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "12",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "So we have all this purchase data for each user",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "00A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Yes, that's right. What would you like to do with it?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Make all the values where purchase is greater than 100 into bold.",
                "intent": "Visualize",
                "dact": "style",
                "dax": "13A",
            },
        ],
    },
    {
        "convo_id": "13",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How do you stay up-to-date with the latest trends in data analysis and marketing?",
                "intent": "Converse",
                "dact": "chat",
                "dax": "000",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I'm constantly updated with new data analysis and marketing techniques. I also learn from the data I analyze.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "That's great! Let's try to dive into some cohort analysis.",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "14",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Please fix TikTock, since it is a typo for TikTok",
                "intent": "Detect",
                "dact": "retrieve + update + confirm",
                "dax": "46E",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Noted. I also found variations 'TikTook', 'tiktok', do you want to merge those as well?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, please update all of them",
                "intent": "Detect",
                "dact": "retrieve + update + confirm",
                "dax": "46E",
            },
        ],
    },
    {
        "convo_id": "15",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's calculate the churn rate for the last quarter.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found two unsupported values in the customers table. Should we take a look first?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes, what do the values look like?",
                "intent": "Detect",
                "dact": "retrieve + update + deny",
                "dax": "46F",
            },
        ],
    },
    {
        "convo_id": "16",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Are there any missing values in the purchase data?",
                "intent": "Detect",
                "dact": "retrieve + update + row",
                "dax": "46B",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Yes, I found two default values and 5 null values in the 'MerchandiseCost' column. We can update them to a default value or remove them. Which would you prefer?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can we interpolate a cost by averaging everything in the same product category?",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "17",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What is Soleda's purpose?",
                "intent": "Converse",
                "dact": "faq",
                "dax": "004",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Soleda provides useful, reliable, and trustworthy assistance to enable people to do their best work.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "That's pretty cool!",
                "intent": "Converse",
                "dact": "chat",
                "dax": "000",
            },
        ],
    },
    {
        "convo_id": "18",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Any insights about the Salary table?",
                "intent": "Detect",
                "dact": "insight",
                "dax": "146",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, to start, I noticed that many columns have empty values. Would you like to take a investigate?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "That's fine, they're blank for privacy reasons.",
                "intent": "Detect",  # Keep the same intent as the previous turn
                "dact": "insight",
                "dax": "146",
            },
        ],
    },
    {
        "convo_id": "19",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you plot the total clicks for rubber stamp campaign this month?",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Here's a line graph showing the daily clicks for the rubber stamp campaign.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Actually, can I get a breakdown of clicks and pageviews for all campaigns, grouped by day.",
                "intent": "Analyze",
                "dact": "pivot",
                "dax": "01A",
            },
        ],
    },
    {
        "convo_id": "20",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Show me a graph of clicks and pageviews for all campaigns each day this month.",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "Done, how does this look?"},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "How would you summarize the information in this graph?",
                "intent": "Converse",
                "dact": "user",
                "dax": "008",
            },
        ],
    },
    {
        "convo_id": "21",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's add a new column for the attribution source on each campaign.",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Certainly, I've added a new column called 'Attribution Source'.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Using last-click attribution, which channel brought in most users from this group?",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "22",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "I'd like to see our engagement rate for promoted tweets.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Our promoted tweets have an average engagement rate of 1.65%.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "How did you calculate that ratio?",
                "intent": "Converse",
                "dact": "agent",
                "dax": "009",
            },
        ],
    },
    {
        "convo_id": "23",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Our machine learning model for personalized recommendations might be off due to duplicate user activity logs.",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "7BD",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Could you specify which columns are relevant for identifying duplicates? And how would you like to handle conflicts?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "The user_id column is relevant for identifying duplicates. For conflicts, choose the latest entry.",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "7BD",
            },
        ],
    },
    {
        "convo_id": "24",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you get rid of whitespace in the product description?",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, the description column has been cleaned up.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Also, split each sentence in the description into its own column.",
                "intent": "Transform",
                "dact": "insert + column + multiple",
                "dax": "5CD",
            },
        ],
    },
    {
        "convo_id": "25",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can we merge username and company name to form a new email column?",
                "intent": "Transform",
                "dact": "insert + column",
                "dax": "05C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, I've merged the two columns into a new column called 'Email Address'.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "That's not what I expected. Let's undo that.",
                "intent": "Clean",
                "dact": "update + deny",
                "dax": "06F",
            },
        ],
    },
    {
        "convo_id": "26",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What is the total impressions, clicks, and spend for each ad group?",
                "intent": "Analyze",
                "dact": "query + insert + table",
                "dax": "01A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, how does this look? Feel free to edit the table directly.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Which column did you use to pull the total spend number?",
                "intent": "Converse",
                "dact": "agent",
                "dax": "009",
            },
        ],
    },
    {
        "convo_id": "27",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you center all the prices in PaymentsProcessed column?",
                "intent": "Visualize",
                "dact": "query + plot + table",
                "dax": "13A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "No problem, the PaymentsProcessed column has been centered.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Nevermind, that doesn't look good, let's revert that",
                "intent": "Clean",
                "dact": "update + deny",
                "dax": "06F",
            },
        ],
    },
    {
        "convo_id": "28",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Tell me something interesting about the data.",
                "intent": "Detect",
                "dact": "query + retrieve + user",
                "dax": "146",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "After some analysis, I've found that Nike, Puma, Adidas and Nike(US) are the most popular brands of shoes.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Nike(US) is supposed to be Nike. Can you check all the brands are valid?",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "36D",
            },
        ],
    },
    {
        "convo_id": "29",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What is the package that has the most subscriptions in the last month?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I found 4 rows with null values in the 'IsSubscribed' column. Should we take a look?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's just ignore those users in our calculation.",
                "intent": "Analyze",  # Keep the same intent as the previous turn
                "dact": "query",
                "dax": "001",
            },
        ],
    },
    {
        "convo_id": "30",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you add a new column that calculates the total revenue from the start of the month?",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure, based on your preferences the start of the month is on November 3rd. Is that correct?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yea, the start date is on the 3rd.",
                "intent": "Transform",  # Keep the same intent as the previous turn
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "31",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Is there any column that shows the country location of each store?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Yes, the 'store_loc' column contains the country information",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Great. Can you compare it with our operating country list to make sure all the listed countries are valid?",
                "intent": "Clean",
                "dact": "plot + update + multiple",
                "dax": "36D",
            },
        ],
    },
    {
        "convo_id": "32",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Show me the last 10 rows of the trasanction table.",
                "intent": "Converse",
                "dact": "row",
                "dax": "00B",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "Here you go"},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "These are not our latest transactions? Are the data sorted?",
                "intent": "Converse",
                "dact": "agent + deny",
                "dax": "09F",
            },
        ],
    },
    {
        "convo_id": "33",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How has our customer acquistion been trending this year?",
                "intent": "Visualize",
                "dact": "analyze + plot",
                "dax": "023",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I plot the trend based on our monthly new customers this year. Is this what you want?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "For the labeling, use three-letter text instead when showing different months.",
                "intent": "Visualize",
                "dact": "query + plot + update",
                "dax": "136",
            },
            {
                "utt_id": 4,
                "speaker": "Agent",
                "text": "I updated the data labels and show months as MM. How does this look?",
            },
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Nice. Save the chart please.",
                "intent": "Visualize",
                "dact": "plot + user + table",
                "dax": "38A",
            },
        ],
    },
    {
        "convo_id": "34",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Calculate the 3-month max drawdown for S&P500 on a rolling basis.",
                "intent": "Clean",
                "dact": "row + multiple",
                "dax": "0BD",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Should I use Market Close price for this?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yes. Also use the Net Total Return Index",
                "intent": "Clean",
                "dact": "row + multiple",
                "dax": "0BD",
            },
        ],
    },
    {
        "convo_id": "35",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do you see any expense column related to our energy consumption?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "In the table, we have 'eletricity use' and 'eletricity rate'. We can calculate energy consumption expense based on these two columns",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Let's do it then. Add the calculated energy consumption expense as a new column. ",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "36",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you group all the campaigns from last year by the social platform and compare the total expense to the impression and clicks generated?",
                "intent": "Analyze",
                "dact": "query + insert + table",
                "dax": "01A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. I've created a pivot table for this.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Great. Can you also calculate the click-through-rate for each group.",
                "intent": "Analyze",
                "dact": "analyze",
                "dax": "002",
            },
        ],
    },
    {
        "convo_id": "37",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Join TradeLog with the Portfolio Holding table by date.",
                "intent": "Transform",
                "dact": "insert + table",
                "dax": "05A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "It looks like TradeLog table records date as datetime and includes intra-day timestamps. Do you want to convert them from DateTime to Date type first to match with the Portfolio Holding table?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Hmm I want to keep the time info. How about creating two columns - one for date and the other for time?",
                "intent": "Transform",
                "dact": "insert + column + multiple",
                "dax": "5CD",
            },
        ],
    },
    {
        "convo_id": "38",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Take a look at the Date column",
                "intent": "Converse",
                "dact": "column",
                "dax": "00C",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "Sure. What do you want to do?"},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "It looks messy. Can you standardize the format as YYYY-MM-DD?",
                "intent": "Clean",
                "dact": "plot + update + deny",
                "dax": "36F",
            },
            {"utt_id": 4, "speaker": "Agent", "text": "Yup. Here you go."},
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Great. Now export the table please",
                "intent": "Transform",
                "dact": "insert + user + table",
                "dax": "58A",
            },
        ],
    },
    {
        "convo_id": "39",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How has our engagement rate changed this year?",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I can take a look. Should I calculate month-over-month engagement rate across all the platforms?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Calculate engagement rate for each platform as of month end, take the average, and show me the result for the last 12 months",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
            {
                "utt_id": 4,
                "speaker": "Agent",
                "text": "You got it. Here's the average monthly engagement rate for the the past 12 months",
            },
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Plot this - I want to see the trend",
                "intent": "Visualize",
                "dact": "analyze + plot",
                "dax": "023",
            },
        ],
    },
    {
        "convo_id": "40",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Update the Traffic_Summary to include our latest traffic data",
                "intent": "Transform",
                "dact": "insert + row",
                "dax": "05B",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Looks like the latest data share the same columns. I'll append the latest data to the history. Here's the result.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Those from the first week of this month are repeated twice. We only need one copy.",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "7BD",
            },
        ],
    },
    {
        "convo_id": "41",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Move the ID column to the left as the first column.",
                "intent": "Transform",
                "dact": "insert + delete",
                "dax": "057",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. Any other changes needed?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Make sure they are all in text format.",
                "intent": "Clean",
                "dact": "update + confirm",
                "dax": "06E",
            },
        ],
    },
    {
        "convo_id": "42",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "For the member_status column, rather than showing 0 and 1, I want it as True or False. Can you do that?",
                "intent": "Clean",
                "dact": "update + confirm",
                "dax": "06E",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. It's now shown as True and False",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "So 1 is now True and 0 is False?",
                "intent": "Converse",
                "dact": "agent",
                "dax": "009",
            },
            {"utt_id": 4, "speaker": "Agent", "text": "Yes. That's what I did"},
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Hmm you know what, undo that. Let me confirm with my team which number represents member.",
                "intent": "Clean",
                "dact": "update + deny",
                "dax": "06F",
            },
        ],
    },
    {
        "convo_id": "43",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you split the Product column and separate Product Category from Product Name?",
                "intent": "Transform",
                "dact": "insert + column + multiple",
                "dax": "5CD",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "Sure. How does this look?"},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you compare Product Name with the current product list and make sure we only include those that we are selling at the moment?",
                "intent": "Clean",
                "dact": "plot + update + multiple",
                "dax": "36D",
            },
        ],
    },
    {
        "convo_id": "44",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do we need to clean the data first?",
                "intent": "Detect",
                "dact": "retrieve + update + user",
                "dax": "468",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I notice in both 'revenue' and 'unit price' columns, numbers are mixed with text. Do you want to take a look?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Good catch. Show me the details please.",
                "intent": "Detect",
                "dact": "retrieve + update + deny",
                "dax": "46F",
            },
        ],
    },
    {
        "convo_id": "45",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's this 'other' column?",
                "intent": "Converse",
                "dact": "column",
                "dax": "00C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Hmm, looks like the column is blank with no data. Do you want to update the column or delete it?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Just delete it.",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            },
        ],
    },
    {
        "convo_id": "46",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's the total returns of my stock portfolio in the last three years? I want to see a line chart for this",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Here you go. I plot the cumulative performance in a line chart.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you summarize the key takeaways?",
                "intent": "Visualize",
                "dact": "plot + user",
                "dax": "038",
            },
            {
                "utt_id": 4,
                "speaker": "Agent",
                "text": "The portfolio returned 12.3% over the last three years. With annual return averaging at 3.9% and annual volatility at 5.6%, the portfolio outperforms the market measured by S&P 500.",
            },
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Nice. What else can you do?",
                "intent": "Converse",
                "dact": "agent + confirm",
                "dax": "09E",
            },
        ],
    },
    {
        "convo_id": "47",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Is there a way to combine flights information with my travel plan?",
                "intent": "Detect",
                "dact": "retrieve + update + multiple",
                "dax": "46D",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I see a table called Flight Information and a table called Travel Details. Should I join these two tables first?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Sounds correct.",
                "intent": "Transform",
                "dact": "insert + table",
                "dax": "05A",
            },
            {
                "utt_id": 4,
                "speaker": "Agent",
                "text": "I can join these two tables by Date. I notice Flight Information table has multiple entries for a single date. Do you want to keep all of them?",
            },
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Yes, definitely. Keep all the flight information in the new table.",
                "intent": "Transform",
                "dact": "insert + table",
                "dax": "05A",
            },
        ],
    },
    {
        "convo_id": "48",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do we have any column that shows the education level of our subscribers?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Yes. We have a 'education' column",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Where is that column? I didn't see it earlier.",
                "intent": "Converse",
                "dact": "column",
                "dax": "00C",
            },
        ],
    },
    {
        "convo_id": "49",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "For the ROI column you just added, how was each value calculated?",
                "intent": "Converse",
                "dact": "agent",
                "dax": "009",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I calculated ROI as the ratio between our subscriber increase and campaign spend.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "How did you calculate the subscriber increase?",
                "intent": "Converse",
                "dact": "agent",
                "dax": "009",
            },
            {
                "utt_id": 4,
                "speaker": "Agent",
                "text": "I calculated it as the difference of total subscribers before and after the campaign",
            },
            {
                "utt_id": 5,
                "speaker": "User",
                "text": "Use the period from 3months before to 3month after each campagin. This is the standard window we should use to calculate all the ROI related metrics",
                "intent": "Analyze",
                "dact": "analyze + update + user",
                "dax": "268",
            },
        ],
    },
    {
        "convo_id": "50",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How many battery startups that have raised funding recently are already gone?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "001",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Can you clarify what you mean by 'recently'?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Since Aug 2022 - basically since the passing of IRA. Can you use this as the time framework across all the calculation?",
                "intent": "Converse",
                "dact": "update + user",
                "dax": "068",
            },
        ],
    },
    {
        "convo_id": "51",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Take a look at the Hubspot table",
                "intent": "Converse",
                "dact": "table",
                "dax": "00A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. Is there anything you want to do with it?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Many rows look the same. We should only keep those that are unique",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "7BD",
            },
        ],
    },
    {
        "convo_id": "52",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "We need to combine rows with the same ID.",
                "intent": "Clean",
                "dact": "delete + row + multiple",
                "dax": "7BD",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Some rows with the same user ID appear to have different login date and email. Do you want to keep them as separate?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "If they have different emails, keep them as separate. Otherwise, keep the one with the latest login",
                "intent": "Clean",
                "dact": "plot + update + user",
                "dax": "368",
            },
        ],
    },
    {
        "convo_id": "53",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Group all the events based on year. Calculate last 5 years of event spend from different regions.",
                "intent": "Analyze",
                "dact": "query + insert + table",
                "dax": "01A",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "Here's the result."},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Rather than having regions as columns, can you show them as rows instead?",
                "intent": "Transform",
                "dact": "insert + update",
                "dax": "056",
            },
        ],
    },
    {
        "convo_id": "54",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "In the 'daily returns' column, use 0 rather than null please.",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. I've replace all the n/a and null with 0.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Make sure all the numbers are formatted as 0.00%.",
                "intent": "Clean",
                "dact": "plot + update + deny",
                "dax": "36F",
            },
        ],
    },
    {
        "convo_id": "55",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "How do you do data cleaning?",
                "intent": "Converse",
                "dact": "retrieve",
                "dax": "004",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "To clean data, I identifiy potential issues, including missing values, typos, mixed types, or outliers, and work with users to resolve them.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "What's considered mixed types?",
                "intent": "Converse",
                "dact": "retrieve",
                "dax": "004",
            },
        ],
    },
    {
        "convo_id": "56",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's the correlation between new user sign up and traffic?",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I don't see any column called new user sign up or traffic.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Just use the 'signup' and 'visit' columns. Aggregate them by hours and calculate the correlation based on the hourly total 'sign up' and 'visit'",
                "intent": "Analyze",
                "dact": "analyze + insert + multiple",
                "dax": "02D",
            },
        ],
    },
    {
        "convo_id": "57",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "We should forward fill the reamining rows in the 'capacity price'. Use the price from the same month of the previous year and apply an annual inflation escalator of 2%.",
                "intent": "Clean",
                "dact": "row + multiple",
                "dax": "0BD",
            },
            {"utt_id": 2, "speaker": "Agent", "text": "The column is updated now."},
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you plot the price history in a line chart?",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
        ],
    },
    {
        "convo_id": "58",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What's our highest, lowest and average rating based on the 'customer rating' column?",
                "intent": "Analyze",
                "dact": "query",
                "dax": "014",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Our lowest rating is 1, highest rating is 5 and average rating is 2.3.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "How come we have such a low average rating? You didn't include 0, did you?",
                "intent": "Converse",
                "dact": "agent + deny",
                "dax": "09F",
            },
        ],
    },
    {
        "convo_id": "59",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "What are the different values in column 'Source'?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "We have 'referral', 'social media', 'company website', 'events' and 'other'.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Can you show me the distribution of applicants coming from different sources as a histogram?",
                "intent": "Visualize",
                "dact": "plot",
                "dax": "003",
            },
        ],
    },
    {
        "convo_id": "60",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Let's combine the internal customer records with the latest hubspot data.",
                "intent": "Transform",
                "dact": "insert + table",
                "dax": "05A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Can you clarify what you mean by the internal customer records",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Look at the 'History_2020-2022' file, there should be a table called 'customers'. Use that",
                "intent": "Transform",  # Keep the same intent as the previous turn
                "dact": "insert + table",
                "dax": "05A",
            },
        ],
    },
    {
        "convo_id": "61",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Append the expense tables for the last four quarters to the one we compiled end of last year.",
                "intent": "Transform",
                "dact": "insert + row",
                "dax": "05B",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sounds good. I'll append them to the latest table quarter by quarter.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Yup. Make a copy of the final table.",
                "intent": "Transform",
                "dact": "insert + user + table",
                "dax": "58A",
            },
        ],
    },
    {
        "convo_id": "62",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Combine google analytics table with our ad spent data",
                "intent": "Transform",
                "dact": "insert + table",
                "dax": "05A",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "I imported the data from Google Analytics. I also found a table called Ad Tracking. Should I use these two?",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Show me the table you imported first",
                "intent": "Converse",
                "dact": "table",
                "dax": "00A",
            },
        ],
    },
    {
        "convo_id": "64",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you add the new member data to the current member table? They should have the same columns",
                "intent": "Transform",
                "dact": "insert + row",
                "dax": "05B",
            },
            {
                "utt_id": 2,
                "speaker": "Agent",
                "text": "Sure. I append the latest data to the existing member table.",
            },
            {
                "utt_id": 3,
                "speaker": "User",
                "text": "Great. Remind me what columns we have in the table?",
                "intent": "Analyze",
                "dact": "query + retrieve",
                "dax": "014",
            },
        ],
    },
    {
        "convo_id": "65",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Do we have data on user abandoning the cart?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
        ],
    },
    {
        "convo_id": "66",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Are there any indication whcih country our suppliers are from?",
                "intent": "Analyze",
                "dact": "query + retrieve + column",
                "dax": "14C",
            },
        ],
    },
    {
        "convo_id": "67",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Add a new column for product name in purchases table",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "68",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Create a new column showing profit margin for each product based on price and estimated cost.",
                "intent": "Transform",
                "dact": "insert",
                "dax": "005",
            },
        ],
    },
    {
        "convo_id": "69",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Can you remove all activities after 1/10?",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            },
        ],
    },
    {
        "convo_id": "70",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Remove all records of discontinued products, those with ProductIDs below 9020.",
                "intent": "Transform",
                "dact": "delete",
                "dax": "007",
            },
        ],
    },
    {
        "convo_id": "71",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Correct the supplier name 'SkinLove Laboratories' to the nickname 'SkinLove Labs' across all records.",
                "intent": "Clean",
                "dact": "update",
                "dax": "006",
            },
        ],
    },
    {
        "convo_id": "72",
        "turns": [
            {
                "utt_id": 1,
                "speaker": "User",
                "text": "Change all product for product ID above 9020 to 'Discontinued' category",
                "intent": "Transform",
                "dact": "update",
                "dax": "006",
            },
        ],
    },
]
