import os
from dotenv import load_dotenv
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool
#for this to work correctly from the CLI you need to specify PYTHONPATH=. in the database folder
from tables import Base

DIRNAME = os.path.dirname(__file__)
ENV_PATH = os.path.abspath(os.path.join(DIRNAME, "..", ".env"))
load_dotenv(ENV_PATH)  # comment out if running in production

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# https://stackoverflow.com/questions/22178339/is-it-possible-to-store-the-alembic-connect-string-outside-of-alembic-ini
section = config.config_ini_section

# Step 1: Generate migrations (only do this in staging/development)
# ALEMBIC_ENV=staging alembic revision --autogenerate -m "description_of_your_change"

# Step 2: Review the generated migration file in migrations/versions/
# ALEMBIC_ENV=staging alembic upgrade head --sql # for dry-runs

# Step 3: Apply migrations in staging:
# ALEMBIC_ENV=staging alembic upgrade head

# Step 4: Test schema changes in the actual application.

# Step 5: Apply migrations in production after testing:
# ALEMBIC_ENV=production alembic upgrade head
ENV = os.getenv("ALEMBIC_ENV", "staging")
if ENV == "production":
    ENV_DB_DSN = os.getenv("PRODUCTION_DB_DSN")
else:
    ENV_DB_DSN = os.getenv("STAGING_DB_DSN")

if not ENV_DB_DSN:
    raise ValueError(f"Database DSN environment variable not set for environment: {ENV}")

# sqlalchemy does not support postgres://
# https://docs.sqlalchemy.org/en/20/core/engines.html#postgresql
DB_DSN = ENV_DB_DSN.replace("postgres://", "postgresql://")

# Set the appropriate section based on environment
config.set_section_option(ENV, "sqlalchemy.url", DB_DSN)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
  fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
  """Run migrations in 'offline' mode.

  This configures the context with just a URL
  and not an Engine, though an Engine is acceptable
  here as well.  By skipping the Engine creation
  we don't even need a DBAPI to be available.

  Calls to context.execute() here emit the given string to the
  script output.

  """
  url = DB_DSN
  context.configure(
      url=url,
      target_metadata=target_metadata,
      literal_binds=True,
      dialect_opts={"paramstyle": "named"},
  )

  with context.begin_transaction():
    context.run_migrations()


def run_migrations_online() -> None:
  """Run migrations in 'online' mode.

  In this scenario we need to create an Engine
  and associate a connection with the context.

  """
  # Use the correct section based on environment and ensure URL is set
  configuration = {
      "sqlalchemy.url": DB_DSN
  }
  
  connectable = engine_from_config(
      configuration,
      prefix="sqlalchemy.",
      poolclass=pool.NullPool,
  )

  with connectable.connect() as connection:
    context.configure(
        connection=connection,
        target_metadata=target_metadata
    )

    with context.begin_transaction():
      context.run_migrations()


if context.is_offline_mode():
  run_migrations_offline()
else:
  run_migrations_online()
