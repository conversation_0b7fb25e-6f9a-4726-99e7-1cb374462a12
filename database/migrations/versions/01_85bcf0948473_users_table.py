"""01 users table

Revision ID: 85bcf0948473
Revises: 
Create Date: 2023-05-10 07:31:49.427129

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '85bcf0948473'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('users',
        sa.Column('user_id', sa.Integer, primary_key=True),
        sa.<PERSON>umn('first', sa.String(), nullable=False),
        sa.<PERSON>umn('middle', sa.String()),
        sa.<PERSON>umn('last', sa.String()),
        sa.Column('email', sa.Unicode(128), unique=True, nullable=False),
        sa.<PERSON>umn('username', sa.String(32), unique=True)
    )

def downgrade() -> None:
    op.drop_table('users')