import sys
import json
import time
import random
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from collections import defaultdict
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.webserver import app
from backend.db import get_db, load_engine
from backend.auth.credentials import get_auth_user_email
from utils.dependencies import get_test_db, load_test_engine

TEST_TOKEN = "secret_acromi_token_for_testing_123"

def get_test_user_email():
  return "<EMAIL>"

@pytest.fixture(scope="module")
def test_client():
  app.dependency_overrides[get_db] = get_test_db
  app.dependency_overrides[load_engine] = load_test_engine
  app.dependency_overrides[get_auth_user_email] = get_test_user_email
  
  client = TestClient(app)
  yield client
  
  app.dependency_overrides.clear()

@pytest.fixture(scope="module")
def websocket(test_client: TestClient):
  with test_client.websocket_connect(f"/api/v1/ws?token={TEST_TOKEN}") as ws:
    yield ws

def test_select_sheet(test_client):
  spreadsheet_data = {
    'ssName': 'Customer Integration',
    'tabNames': ['Hubspot', 'Mailchimp', 'Salesforce', 'Zendesk'],
  }
  response = test_client.post("/api/v1/sheets/select", json=spreadsheet_data)
  assert response.status_code == 200, f"Failed to select spreadsheet: {response.text}"

def decide_which_parts(parts):
  # determine which parts of the message to keep, returns 3 boolean values
  store_query = 'query' in parts
  store_code = 'code' in parts
  store_actions = 'actions' in parts
  return store_query, store_code, store_actions

def decide_part_availability(parsed):
  # determine which parts are available in the message, returns 3 boolean values
  has_query, has_code, has_actions = False, False, False
  
  if 'interaction' in parsed.keys():
    if parsed['interaction'] and 'content' in parsed['interaction']:
      if 'SQL Query' in parsed['interaction']['content']:
        has_query = True
      if 'Pandas Code' in parsed['interaction']['content']:
        has_code = True
  if 'actions' in parsed.keys():
    has_actions = True
  return has_query, has_code, has_actions

def send_message(websocket, message:str, gold_dax:str, parts:list=[]):
  websocket.send_json( { 'currentMessage': message, 'dialogueAct': gold_dax, 'lastAction': None}, mode='binary')
  store_query, store_code, store_actions = decide_which_parts(parts)
  results = defaultdict(str)

  while True:
    try:
      raw_output = websocket.receive_json()
      
      has_query, has_code, has_actions = decide_part_availability(raw_output)
      if store_query and has_query:
        results['query'] = raw_output['interaction']['content'][19:]
      if store_code and has_code:
        results['code'] = raw_output['interaction']['content'][21:]
      if store_actions and has_actions:
        results['actions'] = raw_output['actions']
      if 'message' in raw_output.keys():
        results['message'] = raw_output['message']
        return results

    except json.JSONDecodeError as e:
      return f"Failed to decode JSON: {e}"

  return 'Error'

@pytest.mark.timeout(30)
def test_integrate_table(websocket):
  turn_one = send_message(websocket, "I want to merge the Mailchimp and Salesforce tables together", '05A', ['actions'])
  print("Turn 1:", turn_one)
  assert 'INTERACT' in turn_one['actions'], 'Missing interaction to merge the tables'

@pytest.mark.timeout(30)
def test_pick_columns(websocket, test_client):
  payload = {
    'flowType': 'Transform(join)',
    'selected': [
      {'tab': 'Salesforce', 'col': 'FirstName',   'row': -1, 'ver': True}, 
      {'tab': 'Salesforce', 'col': 'LastName',    'row': -1, 'ver': True}, 
      {'tab': 'Salesforce', 'col': 'EmailAddress', 'row': -1, 'ver': True}, 
      {'tab': 'Mailchimp',  'col': 'First_Name',  'row': -1, 'ver': True}, 
      {'tab': 'Mailchimp',  'col': 'Last_Name',   'row': -1, 'ver': True}, 
      {'tab': 'Mailchimp',  'col': 'Email',       'row': -1, 'ver': True}
    ],
    'stage': 'pick-tab-col'
  }

  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_two = response.json()
  print("Turn 2:", turn_two)
  assert turn_two['message'].endswith('?'), 'Failed to ask follow-up question about the column names'

@pytest.mark.timeout(60)
def test_merge_style(websocket, test_client):
  payload = {
    'flowType': 'Transform(join)',
    'checked': ['DateTimeJoined','Stage','DealSize','LastContactDate','FirstName',
                'LastName', 'Email', 'Campaign_Name', 'Opened', 'Clicked', 'Unsubscribed'
    ], 
    'stage': 'checkbox-opt',
    'support': 'EmailSales'
  }
  response = test_client.post('/api/v1/interactions/checkbox', json=payload)
  turn_three = response.json()
  print("Turn 3:", turn_three)
  assert 'equivalent entries' in turn_three['message'], 'Failed to check the column options'

@pytest.mark.timeout(30)
def test_combine_cards(websocket, test_client):

  for i in range(10):
    resolution = 'separate' if random.random() < 0.8 else 'merge'
    payload = {
      'flowType': 'Transform(join)',
      'stage': 'combine-cards', 
      'selected': [
        {'tab': 'Salesforce', 'col': 'FirstName', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'Salesforce', 'col': 'LastName', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'Salesforce', 'col': 'EmailAddress', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'Mailchimp', 'col': 'First_Name', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'Mailchimp', 'col': 'Last_Name', 'row': -1, 'ver': True, 'rel': ''},
        {'tab': 'Mailchimp', 'col': 'Email', 'row': -1, 'ver': True, 'rel': ''}
      ],
      'resolution': resolution,
      'chosen': {'left': [0], 'right': [0]},
      'method': 'manual'
    }

    response = test_client.post('/api/v1/interactions/merge', json=payload)
    turn_four = response.json()
    print(f"Turn 4-{i}:", turn_four['interaction']['stage'])
    assert response.status_code == 200, "Failed to combine cards"

@pytest.mark.timeout(30)
def test_combine_progress(websocket, test_client):
  payload = {
    'flowType': 'Transform(join)',
    'stage': 'combine-progress',
    'selected': [
      {'tab': 'Salesforce', 'col': 'FirstName', 'row': -1, 'ver': True, 'rel': ''}, 
      {'tab': 'Salesforce', 'col': 'LastName', 'row': -1, 'ver': True, 'rel': ''}, 
      {'tab': 'Salesforce', 'col': 'EmailAddress', 'row': -1, 'ver': True, 'rel': ''}, 
      {'tab': 'Mailchimp', 'col': 'First_Name', 'row': -1, 'ver': True, 'rel': ''}, 
      {'tab': 'Mailchimp', 'col': 'Last_Name', 'row': -1, 'ver': True, 'rel': ''}, 
      {'tab': 'Mailchimp', 'col': 'Email', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'method': 'automatic'
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_five = response.json()
  print("Turn 5:", turn_five)
  assert 'successfully' in turn_five['message'], 'Failed to finalize table integration'