import json
import numpy as np

from utils.help import flow2dax
from backend.prompts.mixins.for_detect import *
from backend.utilities.search import transfer_issues_entity_to_state
from backend.utilities.manipulations import unique_value_distribution
from backend.utilities.pex_helpers import count_tab_cols
from backend.components.engineer import PromptEngineer
from backend.components.metadata import MetaData
from backend.components.frame import Frame
from backend.modules.flow import flow_selection

class DetectMixin:
  """ Methods to fix concerns such as outliers, anomalies, problems, and other issues """

  def _extract_entities(self, flow, world, previous_frame, flow_type):
    """Setup entities for issue detection flows, handling ambiguity and activation state"""
    if previous_frame and len(previous_frame.issues_entity) > 0:
      tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
      activated = True
    else:
      tab_name, col_name = self.decide_main_entity(world, flow, flow_type)
      activated = False
    return tab_name, col_name, activated

  def _complete_resolution(self, flow, frame, state, world):
    active_step = flow.slots['plan'].current_step()

    # we completed all steps in the plan
    if flow.slots['plan'].is_verified():
      flow.completed = True
      state.has_issues = False

    # we found no issues to resolve
    elif active_step['name'] == 'ignore':
      flow, state = self.wrap_up_issues(flow, state)

    # take the next step in the plan
    else:
      flow, state, new_flow = self.build_resolution_flow(active_step, flow, state, world)
      ent_slot = new_flow.entity_slot
      tab_name, col_name = frame.issues_entity['tab'], frame.issues_entity['col']
      new_flow.slots[ent_slot].add_one(tab_name, col_name)

      if len(flow.slots['plan'].steps) == 1:
        state.flow_stack.pop()  # replace the planning flow with the new flow for resolution
        if new_flow.name() == 'update':
          new_flow.slots['exact'].add_one('beyond')
      state.flow_stack.append(new_flow)

    return flow, state

  def _prepare_for_resolution(self, flow, state, default_step, tab_name, col_name, itype):
    """ Prepare for resolution by checking if there are any issues to resolve """
    issue_df = self.database.db.shadow.issues[tab_name]
    issue_rows = issue_df[(issue_df['column_name'] == col_name) & (issue_df['issue_type'] == itype)]

    if issue_rows['row_id'].nunique() == 0:
      # no issues were detected, so clear out the flow
      flow.clear_all_issues()
    elif issue_rows['original_value'].nunique() <= 16:
      # just perform the default action, no need to craft a plan
      flow.slots['plan'].steps.append(default_step)
    else:
      state.current_tab = tab_name
      state.has_issues = True
    frame = Frame(state.current_tab)
    return frame, state, flow

  def identify_blanks(self, context, state, world):
    # Supports {46B} by resolving missing, default, or null values within the table
    flow = state.get_flow(flow_type='blank')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:
      unique_tabs = {ent['tab'] for ent in flow.slots['source'].values}
      if len(unique_tabs) > 1:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('confirmation', flow='blank', values=list(unique_tabs))
        return previous_frame, state

      tab_name, col_name, activated = self._extract_entities(flow, world, previous_frame, 'blank')
      tab_blanks = world.metadata['blank'][tab_name]

      # Check for blank issues specifically
      issue_df = self.database.db.shadow.issues[tab_name]
      blank_rows = issue_df[(issue_df['column_name'] == col_name) & (issue_df['issue_type'] == 'blank')]

      if len(blank_rows) == 0:
        # no issues were detected, so clear out the flow
        flow.clear_all_issues()
      elif set(blank_rows['issue_subtype']) == {'null'} and len(blank_rows) < 4096:
        # just use imputation directly, no need to craft a plan
        impute_step = {'name': 'impute', 'description': 'fill in the missing values in the column', 'checked': False}
        flow.slots['plan'].steps.append(impute_step)
        state.has_issues = True
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)
      frame.issues_entity = {'tab': tab_name, 'col': col_name, 'flow': flow.name()}

      column_data = self.database.db.tables[tab_name][col_name]
      settings = {'include_nulls': True, 'show_nulls_as_count': True, 'show_arrow': True, 'num_values': 32}
      convo_history = context.compile_history()

      if flow.is_filled():   # either because we created a plan, or because no legitimate issues exist
        flow, state = self._complete_resolution(flow, frame, state, world)

      elif activated:    # came back after clarifying a specific resolution
        data_preview = unique_value_distribution(column_data, settings, blank_rows, suffix=' instance')
        prompt = blank_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        if flow.fill_slot_values(prediction):
          frame, state = self.identify_blanks(context, state, world)
        else:
          if flow.interjected:
            state.flow_stack.pop()    # drop the interjected flow
          flow.completed = True       # abort the plan since something is wrong

      elif flow.is_newborn:
        # Activate all blank issues without model review step, since blanks are reviewed already in interactive panel
        flow = self.attach_follow_up(context, flow, frame, state)
        state = transfer_issues_entity_to_state(state, frame)
        data_preview = unique_value_distribution(column_data, settings, blank_rows, suffix=' instance')
        prompt = blank_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

        if flow.fill_slot_values(prediction):
          frame, state = self.identify_blanks(context, state, world)
        else:
          frame, state = self.clarify_issue_plan(flow, frame, state)

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_blanks, tab_name, col_name)
        match resolution:
          case 'ignore': flow, state = self.wrap_up_issues(flow, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')
    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='blank', slot=slot_desc, values=[state.current_tab], generate=True)

    return frame, state

  def identify_concerns(self, context, state, world):
    # Supports {46C} by resolving concerns such as outliers, anomalies, and other issues
    flow = state.get_flow(flow_type='concern')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:
      unique_tabs = {ent['tab'] for ent in flow.slots['source'].values}
      if len(unique_tabs) > 1:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('confirmation', flow='concern', values=list(unique_tabs))
        return previous_frame, state

      # Extract entities and determine activation state
      tab_name, col_name, activated = self._extract_entities(flow, world, previous_frame, 'concern')
      tab_concerns = world.metadata['concern'][tab_name]
      issue_df = self.database.db.shadow.issues[tab_name]
      concerning_rows = issue_df[(issue_df['column_name'] == col_name) & (issue_df['issue_type'] == 'concern')]

      if len(concerning_rows) == 0:
        # no issues were detected, so clear out the flow
        flow.clear_all_issues()
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)
      frame.issues_entity = {'tab': tab_name, 'col': col_name, 'flow': flow.name()}

      column_data = self.database.db.tables[tab_name][col_name]
      settings = {'include_nulls': False, 'show_nulls_as_count': False, 'show_arrow': True, 'num_values': 32}
      convo_history = context.compile_history()

      if flow.is_filled():   # either because we created a plan, or because no legitimate issues exist
        flow, state = self._complete_resolution(flow, frame, state, world)

      elif activated:    # came back after clarifying a specific resolution
        data_preview = unique_value_distribution(column_data, settings, concerning_rows, suffix=' instance')
        prompt = concern_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        if flow.fill_slot_values(prediction):
          frame, state = self.identify_concerns(context, state, world)
        else:
          if flow.interjected:
            state.flow_stack.pop()    # drop the interjected flow
          flow.completed = True       # abort the plan since something is wrong

      elif flow.is_newborn:
        flow = self.attach_follow_up(context, flow, frame, state)
        state = transfer_issues_entity_to_state(state, frame)
        
        data_preview = unique_value_distribution(column_data, settings, concerning_rows, suffix=' instance')
        prompt = concern_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        # prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        prediction = {
          "thought": "Looking at the price distribution, most values range from $30 to $230, which seems reasonable for shoe prices. However, there's one clear outlier at $3360.0, which is significantly higher than all other prices and likely represents a data entry error. Since this is just one instance among thousands of orders, it's probably a typo where extra digits were added.",
          "plan": [ "update - unsure" ]
        }

        if flow.fill_slot_values(prediction):
          flow, state = self._complete_resolution(flow, frame, state, world)
        else:
          frame, state = self.clarify_issue_plan(flow, frame, state)

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_concerns, tab_name, col_name)
        match resolution:
          case 'ignore': flow, state = self.wrap_up_issues(flow, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')
    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='concern', slot=slot_desc, values=[state.current_tab], generate=True)

    return frame, state

  def clarify_issue_plan(self, flow, frame, state):
    # either the step was not filled, or the description was unsure
    display_df = self.display_issues(frame, state)
    tab_name = frame.issues_entity['tab']
    issue_df = self.database.db.shadow.issues[tab_name]

    self.actions.add('CLARIFY')
    state.ambiguity.declare('specific', flow=flow.name())
    state.ambiguity.observation = flow.clarify_issue_resolution(issue_df, frame)

    issue_query = flow.describe_issues(issue_df, frame)
    frame.set_data(display_df, issue_query, source=flow.name())
    frame.tab_type = 'dynamic'
    return frame, state

  def identify_typos(self, context, state, world):
    # Supports {46E} by resolving similar terms or typos within the table
    flow = state.get_flow(flow_type='typo')
    previous_frame = world.frames[-1] if world.has_data() else None
    print("taa")

    if flow.slots['source'].filled:
      print("tbb")
      unique_tabs = {ent['tab'] for ent in flow.slots['source'].values}
      if len(unique_tabs) > 1:
        print("tcc")
        self.actions.add('CLARIFY')
        state.ambiguity.declare('confirmation', flow='typo', values=list(unique_tabs))
        return previous_frame, state

      tab_name, col_name, activated = self._extract_entities(flow, world, previous_frame, 'typo')
      validate_step = {'name': 'validate', 'description': 'only keep valid terms in the column', 'checked': False}
      frame, state, flow = self._prepare_for_resolution(flow, state, validate_step, tab_name, col_name, 'typo')
      frame.issues_entity = {'tab': tab_name, 'col': col_name, 'flow': flow.name()}

      column_data = self.database.db.tables[tab_name][col_name]
      settings = {'include_nulls': False, 'show_nulls_as_count': False, 'show_arrow': False, 'num_values': 32}
      convo_history = context.compile_history()

      if flow.is_filled():   # either because we created a plan, or because no legitimate issues exist
        print("tdd")
        flow, state = self._complete_resolution(flow, frame, state, world)

      elif activated:    # came back after clarifying a specific resolution
        print("tee")
        data_preview = unique_value_distribution(column_data, settings, suffix=' instance')
        prompt = typo_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        if flow.fill_slot_values(prediction):
          print("tff")
          frame, state = self.identify_typos(context, state, world)
        else:
          print("tgg")
          if flow.interjected:
            state.flow_stack.pop()    # drop the interjected flow
          flow.completed = True       # abort the plan since something is wrong

      elif flow.is_newborn:
        print("thh")
        flow = self.attach_follow_up(context, flow, frame, state)
        state = transfer_issues_entity_to_state(state, frame)

        data_preview = unique_value_distribution(column_data, settings, suffix=' instance')
        prompt = typo_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        # prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        prediction = {
          "thought": "Looking at the channel distribution, I can see there are inconsistent naming conventions. The most common values follow a pattern like 'search_google', 'email_existing', 'social_fb', but there are also standalone values like 'Google', 'Bing', 'Facebook' that should probably map to the underscore format. There are also some variations like 'email_newuser' vs 'email_new_user' and 'google_search' vs 'search_google'. Since there's a clear pattern for the correct format and a finite set of channels, I should standardize the naming first, then validate against the expected channel types.",
          "plan": [
            "validate - ensure all channels follow the expected format of 'source_platform' where source is one of search/email/social/affiliate and platform is the specific service"
          ]
        }
        # "update - standardize channel names to follow the consistent pattern (eg. 'Google' -> 'search_google', 'Facebook' -> 'social_fb', 'email_newuser' -> 'email_new_user', 'google_search' -> 'search_google')",

        if flow.fill_slot_values(prediction):
          print("tii")
          frame, state = self.identify_typos(context, state, world)
        else:
          print("tjj")
          self.actions.add('CLARIFY')
          state.ambiguity.declare('specific', flow='typo')

      else:      # issues are already activated, but NLU didn't predict a resolution
        tab_typos = world.metadata['typo'][tab_name]
        resolution = self.decide_issue_redirection(context, tab_typos, tab_name, col_name)
        match resolution:
          case 'ignore': flow, state = self.wrap_up_issues(flow, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')
    else:
      print("tkk")
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='typo', slot=slot_desc, values=[state.current_tab], generate=True)

    print("tll")
    return frame, state

  def build_resolution_flow(self, active_step, old_flow, state, world):
    active_dax = flow2dax(active_step['name'])
    new_flow = flow_selection[active_dax](world.valid_columns)
    new_flow.interjected = True

    state.store_dacts(dax=active_dax)
    state.thought = active_step['description']
    state.keep_going = True
    return old_flow, state, new_flow
  
  def wrap_up_issues(self, flow, state):
    state.has_issues = False
    flow.completed = True
    return flow, state

  def identify_problems(self, context, state, world):
    # Supports {46F} by resolving problems such as mixed datatypes and unsupported data structures
    flow = state.get_flow(flow_type='problem')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:
      unique_tabs = {ent['tab'] for ent in flow.slots['source'].values}
      if len(unique_tabs) > 1:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('confirmation', flow='problem', values=list(unique_tabs))
        return previous_frame, state

      # Extract entities and determine activation state
      tab_name, col_name, activated = self._extract_entities(flow, world, previous_frame, 'problem')
      tab_problems = world.metadata['problem'][tab_name]

      # Check for problem issues specifically
      issue_df = self.database.db.shadow.issues[tab_name]
      problem_rows = issue_df[(issue_df['column_name'] == col_name) & (issue_df['issue_type'] == 'problem')]

      if problem_rows['row_id'].nunique() == 0:
        # no issues were detected, so clear out the flow
        flow.clear_all_issues()
      elif problem_rows['original_value'].nunique() <= 16:
        # just change the column content directly, no need to craft a plan
        datatype_step = {'name': 'datatype', 'description': 'change the datatype of the column', 'checked': False}
        flow.slots['plan'].steps.append(datatype_step)
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)
      frame.issues_entity = {'tab': tab_name, 'col': col_name, 'flow': flow.name()}

      column_data = self.database.db.tables[tab_name][col_name]
      settings = {'include_nulls': False, 'show_nulls_as_count': False, 'show_arrow': True, 'num_values': 32}
      convo_history = context.compile_history()

      if flow.is_filled():   # either because we created a plan, or because no legitimate issues exist
        flow, state = self._complete_resolution(flow, frame, state, world)

      elif activated:    # came back after clarifying a specific resolution
        data_preview = unique_value_distribution(column_data, settings, problem_rows, suffix=' instance')
        prompt = problem_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        if flow.fill_slot_values(prediction):
          flow, state = self._complete_resolution(flow, frame, state, world)
        else:
          if flow.interjected:
            state.flow_stack.pop()    # drop the interjected flow
          flow.completed = True       # abort the plan since something is wrong

      elif flow.is_newborn:
        flow = self.attach_follow_up(context, flow, frame, state)
        state = transfer_issues_entity_to_state(state, frame)

        data_preview = unique_value_distribution(column_data, settings, problem_rows, suffix=' instance')
        prompt = problem_plan_prompt.format(column=col_name, table=tab_name, distribution=data_preview, history=convo_history)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        if flow.fill_slot_values(prediction):
          frame, state = self.identify_problems(context, state, world)
        else:
          frame, state = self.clarify_issue_plan(flow, frame, state)

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_problems, tab_name, col_name)
        match resolution:
          case 'ignore': flow, state = self.wrap_up_issues(flow, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')
    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='problem', slot=slot_desc, values=[state.current_tab], generate=True)

    return frame, state

  def decide_issue_redirection(self, context, concerns, tab_name, col_name):
    convo_history = context.compile_history()
    issue_df = self.database.db.shadow.issues[tab_name]
    issue_types = concerns.detected_issue_types(issue_df, col_name)

    issue_lines = []
    for issue_type in issue_types:
      count_issues = MetaData.num_issue_rows(issue_df, col_name, issue_type)
      issue_lines.append(concerns.type_to_nl(issue_type, count_issues))
    issue_desc = PromptEngineer.array_to_nl(issue_lines, connector='and')

    prompt = issue_redirection_prompt.format(history=convo_history, description=issue_desc, column=col_name)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    return prediction['method']

  def attach_follow_up(self, context, flow, frame, state):
    if flow.interjected:
      flow.follow_up['query'] = frame.code
      flow.follow_up['thought'] = state.thought
      flow.follow_up['history'] = context.compile_history(look_back=3)
    return flow

  def display_issues(self, frame, state):
    # Extract the issue rows from ShadowDB for display
    tab_name, col_name = frame.issues_entity['tab'], frame.issues_entity['col']
    issue_df = self.database.db.shadow.issues[tab_name]
    issue_rows = MetaData.detected_row_ids(issue_df, col_name)

    columns = [col_name]
    for entity in state.entities:
      if entity['tab'] == tab_name and entity['col'] != col_name and entity['col'] != '*':
        columns.append(entity['col'])
    display_df = self.database.db.tables[tab_name].loc[issue_rows, columns]
    return display_df

  def decide_main_entity(self, world, flow, issue_type):
    # select the one column with the most issues
    main_tab = flow.slots['source'].table_name()
    main_col = ''
    tab_metadata = world.metadata[issue_type][main_tab]
    issue_df = self.database.db.shadow.issues[main_tab]

    if any(ent['col'] == '*' for ent in flow.slots['source'].values):
      candidate_columns = world.valid_columns[main_tab]
    else:
      candidate_columns = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == main_tab]

    if not tab_metadata.prepared:
      for col_name in candidate_columns:
        column = self.database.db.tables[main_tab][col_name]
        issue_df, _ = tab_metadata.detect_issues(issue_df, column)
      self.database.db.shadow.issues[main_tab] = issue_df

    max_count = 0
    for col_name in candidate_columns:
      count = tab_metadata.num_issue_rows(issue_df, col_name, issue_type)
      if count > max_count:
        max_count, main_col = count, col_name
    return main_tab, main_col

  def drop_resolved_rows(self, selected_rows, frame, state):
    """ Users do not need to resolve all issues at once. Accordingly, the selected rows represent
    the subset of row ids that we are dealing with right now. This function deactivates the selected rows by
    removing them from the ShadowDB and storng them as resolved_rows within the frame. """
    issue_tab, issue_col = frame.issues_entity['tab'], frame.issues_entity['col']
    issue_df = self.database.db.shadow.issues[issue_tab]
    issue_type = frame.issues_entity['flow']

    # Remove the selected rows from the ShadowDB
    issue_df = MetaData.remove_issues(issue_df, issue_col, selected_rows, issue_type)
    self.database.db.shadow.issues[issue_tab] = issue_df
    # Mark the selected rows as resolved within the frame
    frame.raw_table = issue_tab
    frame.resolved_rows = selected_rows

    return frame, state

  def backfill_issue_flow(self, resolution_flow, issue_flow, state):
    issue_flow.turns_taken += 1
    issue_flow.slots['plan'].mark_as_complete(resolution_flow.name())

    if issue_flow.slots['plan'].is_verified():
      issue_flow.completed = True
    else:
      self.actions.clear()
      underlying_dax = flow2dax(issue_flow.name())
      state.flow_stack.pop()  # remove the stack_on flow
      state.store_dacts(dax=underlying_dax)  # point back to the underlying flow
      state.keep_going = True

    return issue_flow, state

  def connect_information(self, context, state, world):
    # Supports {46D} which is a generic request to combine two data sources together, returns a proposed table
    flow = state.get_flow(flow_type='connect')
    frame = Frame(state.current_tab, source='interaction')

    if flow.slots['source'].filled:
      if flow.slots['target'].filled:
        source_tab = flow.slots['source'].values[0]['tab']
        target_tab = flow.slots['target'].values[0]['tab']
        source_df = self.database.db.tables[source_tab]
        new_df = self.combine_tables(source_df)
        self.database.db.tables[target_tab] = new_df

        unique_tables = set([ent['tab'] for ent in flow.slots['target'].values])
        if len(unique_tables) > 1:
          connected_tabs = list(unique_tables)
          frame.properties['tabs'] = connected_tabs
        else:
          connected_tabs = [state.current_tab]
        self.update_system_prompt(connected_tabs, world, context, flow)

        self.actions.remove('INTERACT')
        flow.completed = True

      else:
        flow.stage = 'pick-tab-col'
        frame.tab_type = 'decision'
        frame.raw_table = state.current_tab
    return frame, state

  def resolve_issues(self, context, state, world):
    # Supports {468} which is a planning request to identify issues within the table
    flow = state.get_flow(flow_type='resolve')

    if flow.slots['source'].filled and not flow.is_uncertain:
      tab_name = flow.slots['source'].values[0]['tab']
      if state.has_plan:
        if len(flow.slots['plan'].options) > 0:

          if flow.slots['plan'].filled:
            for selected_opt in flow.slots['plan'].values:
              fall_back_dax = flow2dax(selected_opt)
              if fall_back_dax != 'none':
                flow.fall_back = fall_back_dax
            flow.completed = True
            state.has_plan = False
          else:
            self.actions.add('CLARIFY')
            slot_desc = 'type of fix to apply'
            fix_options = flow.slots['plan'].options
            state.ambiguity.declare('confirmation', slot=slot_desc, values=fix_options, generate=True)
          frame = self.default_frame(state, state.entities)

        else:
          # propose different options for the user to consider
          for fix_opt in flow.slots['plan'].options:
            if fix_opt in ['dedupe', 'validate', 'format']:
              flow = self.collect_cleaning_option(tab_name, flow, fix_opt, world)
            else:
              flow = self.collect_issue_option(fix_opt, tab_name, flow, world)
            if len(flow.slots['plan'].options) >= 3:
              break
          # if no options are found, just randomly select two after shuffling
          if len(flow.slots['plan'].options) == 0:
            randomized_options = np.random.choice(flow.slots['plan'].options, size=2, replace=False)
            for random_opt in randomized_options:
              flow.slots['plan'].options.append(random_opt)
          frame = Frame(tab_name)

      else:
        frame, state = self.propose_fix_plan(context, flow, state, tab_name, world)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='resolve')
      if len(flow.slots['source'].active_tab) > 0:
        state.ambiguity.observation = "Is there a specific column you want to focus on?"
      else:
        state.ambiguity.observation = "Is there a specific table or column you have in mind?"
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    return frame, state

  def propose_fix_plan(self, context, flow, state, tab_name, world):
    routing_prompt = resolve_routing_prompt.format(table=tab_name, history=context.compile_history())
    raw_output = self.api.execute(routing_prompt)
    pred_situation = PromptEngineer.apply_guardrails(raw_output, 'json')

    match pred_situation:
      case 'blank': flow.fall_back = '46B'
      case 'concern': flow.fall_back = '46C'
      case 'typo': flow.fall_back = '46E'
      case 'problem': flow.fall_back = '46F'
      case 'validate': flow.fall_back = '36D'
      case 'format': flow.fall_back = '36F'
      case 'dedupe': flow.fall_back = '7BD'
      case _: flow.fall_back = ''

    if len(flow.fall_back) == 0:
      # then we are truly in a situation dealing with an open-ended request to fix issues
      state.has_plan = True
      frame, state = self.resolve_issues(context, state, world)
    else:
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)
    return frame, state

  def collect_issue_option(self, issue, tab_name, flow, world):
    # Run through the different issues to see if anything turns up
    issue_metadata = world.metadata[issue][tab_name]
    if not issue_metadata.prepared:
      issue_metadata.detect_issues(self.database.db.tables[tab_name])

    for entity in flow.slots['source'].values:
      issue_df = self.database.db.shadow.issues[tab_name]
      if issue_metadata.issues_exist(issue_df, [entity['col']]):
        if issue not in flow.slots['plan'].options:
          flow.slots['plan'].options.append(issue)

    return flow

  def collect_cleaning_option(self, tab_name, flow, option, world):
    option_subtypes = {
      'dedupe': ['name', 'email', 'address'],
      'validate': ['status', 'category', 'city', 'state'],
      'format': ['date', 'time', 'month', 'phone']
    }
    candidate_subtypes = option_subtypes.get(option, [])
    tab_schema = world.metadata['schema'][tab_name]

    for entity in flow.slots['source'].values:
      if entity['tab'] == tab_name:
        col_info = tab_schema.get_type_info(entity['col'], include_supplement=False)
        if col_info['subtype'] in candidate_subtypes:
          flow.slots['plan'].options.append(option)
    return flow

  def uncover_insights(self, context, state, world):
    """ Supports {146} as a method for performing advanced analysis requiring multiple metrics and variables. This flow
    also serves as a fallback for when the user makes an open-ended request for insights where they do not have the
    intention of analyzing the data, but merely want to see what the agent is capable of doing. If this happens, we set
    the stage of the flow as 'automatic-execution'. We keep going until all steps in the plan have been executed."""
    flow = state.get_flow(flow_type='insight')
    frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    if flow.slots['source'].filled and not flow.is_uncertain:
      tab_col_str = PromptEngineer.tab_col_rep(world)

      if state.has_plan and len(flow.scratchpad) > 0:

        if flow.slots['plan'].is_verified():
          flow, state = self.finish_up(context, flow, state, tab_col_str)
        elif flow.slots['plan'].filled:
          flow, state = self.execute_select_flow(context, flow, state, world)
        elif flow.slots['plan'].approved:
          flow, state = self.convert_to_stack_on(context, flow, state, world)
        else:  # user saw the plan, but gave feedback on how to change it
          flow, state = self.revise_plan(context, flow, state, tab_col_str)

      elif flow.stage.endswith('proposal'):
        if state.ambiguity.present():
          flow, state = self.review_insight_proposal(context, flow, state, tab_col_str)
        else:
          flow, state = self.propose_insight_plan(context, flow, state, tab_col_str)

      else:
        flow, state = self.insight_routing(context, flow, state, tab_col_str)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='insight')

    if flow.completed and not state.ambiguity.present():
      # fill frame with data so that we can summarize results
      table_df = self.database.db.tables[state.current_tab]
      query = f'SELECT * FROM {state.current_tab}'
      frame.set_data(table_df, query)
    return frame, state

  def insight_routing(self, context, flow, state, tab_col_str):
    prompt = insight_routing_prompt.format(history=context.compile_history())
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    flow.slots['analysis'].value = prediction['type']
    scenario = prediction['scenario'].lower()
    match scenario:
      case 'basic': flow.fall_back = '001'
      case 'intermediate': flow.fall_back = '002'
      case 'advanced': flow.stage = 'initialize-proposal'
      case 'vague': flow.stage = 'automatic-proposal'
      case _: flow.clarify_attempts = 0

    if flow.stage.endswith('proposal'):
      flow, state = self.review_insight_proposal(context, flow, state, tab_col_str)
      context.set_bookmark()    # so we can reference the first turn later
    elif flow.clarify_attempts == 0:
      state.thought = "Your request is out of scope and is not something I can handle."
      flow.completed = True
    return flow, state

  def propose_insight_plan(self, context, flow, state, tab_col_str):
    # generate a natural language plan to present to the user for approval
    convo_history = context.compile_history()
    if flow.stage.startswith('automatic'):
      prompt = automatic_plan_prompt.format(history=convo_history, valid_tab_col=tab_col_str)
    else:
      analysis_type = flow.slots['analysis'].value
      prompt = insight_plan_prompt.format(history=convo_history, type=analysis_type, valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, version='reasoning-model', max_tok=2048)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    if len(prediction['plan']) <= 1:
      flow.fall_back = '001'
      flow.completed = True
    else:
      flow.scratchpad = prediction['plan']
      state.has_plan = True
    return flow, state

  def review_insight_proposal(self, context, flow, state, tab_col_str):
    # propose the type of analysis to run and ask for clarification on any missing information to conduct the analysis
    state.ambiguity.resolve()
    flow.clarify_attempts -= 1
    analysis_type = flow.slots['analysis'].value

    if flow.clarify_attempts > 0:
      prompt = proposal_confirmation_prompt.format(history=context.compile_history(), analysis_type=analysis_type,
                                                  valid_tab_col=tab_col_str)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    else:
      prediction = {'questions': [], 'type': analysis_type}
      flow.completed = True

    if prediction['type'] != analysis_type:
      flow.slots['analysis'].value = prediction['type']
    # purposely *avoid* adding the CLARIFY action, since we want to use the flow to manage the ambiguity
    if len(prediction['questions']) > 0:
      state.ambiguity.declare('specific', flow='insight', slot='proposal', generate=True)
      state.ambiguity.observation = ' '.join(prediction['questions'][:2])
    return flow, state

  def revise_plan(self, context, flow, state, tab_col_str, restart=False):
    # the user has seen the plan, but has given feedback on how to change it
    convo_history = context.compile_history(look_back=7)
    if restart:
      prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')
    else:
      prior_plan = '\n'.join(flow.scratchpad)           # natural language plan
    analysis_type = flow.slots['analysis'].value

    revision_prompt = revise_hypothesis_prompt if flow.stage.startswith('automatic') else revise_plan_prompt
    prompt = revision_prompt.format(history=convo_history, previous_plan=prior_plan, analysis_type=analysis_type,
                                    valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, version='claude-sonnet')
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    state.ambiguity.resolve()
    flow.scratchpad = prediction['plan']
    flow.clarify_attempts -= 1

    if flow.clarify_attempts < 0:
      flow.completed = True   # situation is too complex for the agent to handle, so we exit instead
    else:
      state.ambiguity.declare('specific', flow='insight', slot='plan', generate=True)
    return flow, state

  def convert_to_stack_on(self, context, flow, state, world):
    # convert the plan written in natural language into a plan composed of a series of stack_on flows
    analysis_type = flow.slots['analysis'].value
    nl_plan = ' '.join(flow.scratchpad)
    tab_col_str = PromptEngineer.tab_col_rep(world)
    prompt = convert_to_flow_prompt.format(analysis_type=analysis_type, plan_steps=nl_plan, valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, max_tok=1024)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    flow.scratchpad = []
    flow.fill_slot_values(prediction, adjusted=False)
    if flow.slots['plan'].filled:
      match flow.stage:
        case 'automatic-proposal': flow.stage = 'automatic-execution'
        case 'initialize-proposal': flow.stage = 'plan-execution'
      flow, state = self.execute_select_flow(context, flow, state, world)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('specific', flow='insight', slot='plan', generate=True)
    return flow, state

  def execute_select_flow(self, context, flow, state, world):
    """ cycle through a battery of analyses to find anything that can be considered interesting """
    if flow.turns_taken > 0 and len(flow.scratchpad) > 0:  # we have already executed a stack_on flow
      past_results = '\n'.join([f" * {summary['text']}" for summary in flow.scratchpad])
      prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')
      match flow.turns_taken:
        case 1: iteration = 'one step'
        case 2: iteration = 'two steps'
        case 3: iteration = 'three steps'
        case _: iteration = 'a few steps'

      prompt = adjust_plan_prompt.format(analysis_type=flow.slots['analysis'].value, previous_plan=prior_plan,
                            iteration=iteration,  history=context.compile_history(), past_results=past_results)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      state.thought = prediction['thought']
      if prediction['needs_adjustment']:
        flow.fill_slot_values(prediction, adjusted=True)

    # in either case, prepare the next stack_on flow for execution
    flow.turns_taken += 1
    self.actions.clear()
    next_step = next((step for step in flow.slots['plan'].steps if not step['checked']), None)
    if next_step is None or flow.turns_taken > 7:
      tab_col_str = PromptEngineer.tab_col_rep(world)
      return self.finish_up(context, flow, state, tab_col_str)
    for step in PromptEngineer.display_plan(flow.slots['plan'].steps):
      print(step)

    flow_dax, flow_desc = next_step['dax'], next_step['description']
    stack_on_flow = flow_selection[flow_dax](world.valid_columns)
    stack_on_flow.interjected = True
    if len(flow_desc) > 0 and 'operation' in stack_on_flow.slots:
      stack_on_flow.slots['operation'].values.append(flow_desc)

    stack_on_flow = self.transfer_metrics_and_entities(context, flow, stack_on_flow, next_step)
    state.flow_stack.append(stack_on_flow)
    state.store_dacts(dax=flow_dax)
    state.keep_going = True
    return flow, state

  def transfer_metrics_and_entities(self, context, flow, stack_on, next_step):
    # transfer over the source entities to the analyze flow
    for entity in flow.slots['source'].values:
      stack_on.slots['source'].add_one(**entity)

    if next_step['dax'] == '002' or next_step['dax'] == '02D':
      acronym = next_step.get('acronym', 'N/A')
      expanded = next_step.get('expanded', 'N/A')
      stack_on.slots['metric'].assign_metric(acronym, expanded)

      has_one_table, num_columns, tab_name = count_tab_cols(stack_on.slots['source'].values)
      if has_one_table and num_columns > 4:
        convo_history = context.compile_history()
        metric_name = stack_on.slots['metric'].formula.get_name()
        col_list = ', '.join([ent['col'] for ent in stack_on.slots['source'].values])
        prompt = focus_metric_prompt.format(history=convo_history, metric=metric_name, table=tab_name, columns=col_list)
        raw_output = self.api.execute(prompt, prefix='Columns:')
        pred_columns = [pred.strip() for pred in raw_output[8:].split(',')]

        stack_on.slots['source'].drop_unverified()
        for col_name in pred_columns:
          stack_on.slots['source'].add_one(tab_name, col_name)

    elif next_step['dax'] == '39B':
      stack_on.slots['style'].assign_one('sample')
      for step in flow.slots['plan'].steps:
        if not step['checked']:
          desc_str = f"  * {step['description']}"
          stack_on.slots['task'].add_one(desc_str)

    stack_on.origin = '146'
    return stack_on

  def write_to_scratchpad(self, curr_flow, prev_flow, table_df):
    # use all the information from metric or variable info to summarize results
    plan_description = PromptEngineer.display_plan(prev_flow.slots['plan'].steps, join_key='\n')
    iteration_map = ['<skip>', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'last']
    analysis_type = prev_flow.slots['analysis'].value
    task = f'performing {analysis_type}' if 'analysis' in analysis_type.lower() else f'analyzing {analysis_type}'

    prompt = summarize_results_prompt.format(analysis=task, iteration=iteration_map[prev_flow.turns_taken],
                                        current_plan=plan_description, table_md=PromptEngineer.display_preview(table_df))
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    for summary_text in prediction['summary']:
      summary_point = {'flow_name': curr_flow.name(full=True), 'text': summary_text}
      prev_flow.scratchpad.append(summary_point)

    prev_flow.slots['plan'].mark_as_complete(curr_flow.name(full=False))
    return curr_flow, prev_flow

  def finish_up(self, context, flow, state, tab_col_str):
    # wrap up the flow by presenting the results to the user
    # use all the information from metric or variable info to summarize results
    summaries = '\n'.join([f"  * {summary['text']}" for summary in flow.scratchpad])
    prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')

    if flow.stage.startswith('automatic'):
      prompt = interesting_enough_prompt.format(history=context.compile_history(), summaries=summaries,
                                          analysis_type=flow.slots['analysis'].value, previous_plan=prior_plan)
      raw_output = self.api.execute(prompt)
      result = PromptEngineer.apply_guardrails(raw_output, 'json')
      if not result['is_interesting']:
        return self.revise_plan(context, flow, state, tab_col_str, restart=True)

    state.has_plan = False
    flow.completed = True
    flow.stage = 'complete'
    return flow, state

