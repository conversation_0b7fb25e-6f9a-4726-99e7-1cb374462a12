"""Add name, desc, and data source ids to conv

Revision ID: 31bc4de7b2b1
Revises: bbadc73bebd4
Create Date: 2025-05-21 16:19:12.659996

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '31bc4de7b2b1'
down_revision = 'bbadc73bebd4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('conversation_data_source',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('conversation_id', sa.UUID(), nullable=True),
    sa.Column('data_source_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.Column('updated_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversation.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['data_source_id'], ['user_data_source.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('conversation', sa.Column('name', sa.Text(), nullable=True))
    op.add_column('conversation', sa.Column('description', sa.Text(), nullable=True))

    # Add indexes for foreign keys
    op.create_index('ix_conversation_data_source_conversation_id', 'conversation_data_source', ['conversation_id'])
    op.create_index('ix_conversation_data_source_data_source_id', 'conversation_data_source', ['data_source_id'])

    op.execute("""
        CREATE TRIGGER update_conversation_data_source_updated_at
            BEFORE UPDATE ON conversation_data_source
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)


def downgrade() -> None:
    op.drop_column('conversation', 'description')
    op.drop_column('conversation', 'name')
    op.execute("DROP TRIGGER IF EXISTS update_conversation_data_source_updated_at ON conversation_data_source;")
    
    # Drop indexes
    op.drop_index('ix_conversation_data_source_conversation_id', table_name='conversation_data_source')
    op.drop_index('ix_conversation_data_source_data_source_id', table_name='conversation_data_source')
    
    op.drop_table('conversation_data_source')
