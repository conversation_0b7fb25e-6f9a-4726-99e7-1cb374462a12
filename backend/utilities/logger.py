import os
import time
import json
import base64
import httpx
import asyncio
import traceback
from typing import Dict, Any, Optional
from enum import Enum
from functools import wraps

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles non-serializable objects."""
    def default(self, obj):
        # Handle common types that aren't JSON serializable
        if hasattr(obj, '__dict__'):
            # For custom objects, return their string representation
            return str(obj)
        elif hasattr(obj, 'items'):
            # For dict-like objects
            return dict(obj)
        # Add more custom handling as needed
        return str(obj)

class SoledaLogger:
    def __init__(self):
        """Initialize the global logger instance with configuration from environment variables."""
        self.instance_id = os.getenv("GRAFANA_INSTANCE_ID")
        self.api_key = os.getenv("GRAFANA_API_KEY")
        self.loki_url = os.getenv("GRAFANA_LOKI_URL")
        self.environment = os.getenv('SOLEDA_ENV', 'development')
        self.default_user_id = "anonymous"
        self.loki_available = True  # Flag to track if Loki is available
        self.last_loki_attempt = 0  # Timestamp of last attempt
        self.loki_retry_interval = 300  # 5 minutes between retries
        
        if not self.api_key:
            print("Warning: GRAFANA_API_KEY environment variable not set")
            self.loki_available = False
    
    # NOTE: This method is maintained for backward compatibility, but it's better to explicitly pass user_id
    # in each logging call instead of relying on default context in a concurrent environment
    def set_user_context(self, user_id: str):
        """
        Set a default user_id for all subsequent logging calls.
        NOTE: In a concurrent environment with multiple users, this approach is not recommended.
        Instead, explicitly pass user_id with each logging call.
        
        Args:
            user_id: User identifier to use as default
        Returns:
            self: Returns the logger instance for method chaining
        """
        self.default_user_id = user_id
        return self
    
    def _sanitize_data(self, data):
        """Sanitize data to ensure it's JSON serializable."""
        try:
            if data is None:
                return None
            if isinstance(data, (str, int, float, bool, type(None))):
                return data
            if isinstance(data, dict):
                return {k: self._sanitize_data(v) for k, v in data.items()}
            if isinstance(data, list):
                return [self._sanitize_data(item) for item in data]
            if isinstance(data, tuple):
                return tuple(self._sanitize_data(item) for item in data)
            if isinstance(data, set):
                return list(self._sanitize_data(item) for item in data)
            
            # Handle stream objects from various libraries
            if hasattr(data, "read") and callable(data.read):
                return f"<Stream object: {str(data)}>"
            
            # Handle pydantic models
            if hasattr(data, 'dict') and callable(data.dict):
                return self._sanitize_data(data.dict())
            if hasattr(data, 'model_dump') and callable(data.model_dump):
                return self._sanitize_data(data.model_dump())
            
            # Attempt to serialize with custom encoder for other objects
            try:
                json.dumps(data, cls=CustomJSONEncoder)
                return data
            except (TypeError, OverflowError, ValueError):
                if hasattr(data, '__dict__'):
                    return self._sanitize_data(data.__dict__)
                return str(data)
        except Exception:
            # Last resort: convert to string
            return str(data)
    
    async def _send_to_loki(self, level: LogLevel, event_name: str, component: str, 
                          details: Dict = None, user_id: str = None, error: Dict = None) -> Dict:
        # Use default values if not explicitly provided
        user_id = user_id or self.default_user_id
        details = details or {}
        
        # Create a human-readable message for console output
        message = f"{level.value} - {component} - {event_name}"
        if details.get('message'):
            message += f": {details['message']}"
            
        print(message)
        
        # If Loki is not available or API key is not set, just log to console and return
        if not self.loki_available or not self.api_key:
            return {"status": "warning", "message": "Loki logging disabled or not configured"}
        
        # Check if we should attempt to connect to Loki
        current_time = time.time()
        if current_time - self.last_loki_attempt < self.loki_retry_interval and not self.loki_available:
            # Skip Loki attempt if we recently failed and haven't waited long enough
            return {"status": "skipped", "message": "Skipping Loki due to recent failure"}
        
        self.last_loki_attempt = current_time
        
        try:
            # Sanitize details and error data to ensure they are JSON serializable
            details = self._sanitize_data(details)
            if error:
                error = self._sanitize_data(error)
            
            # Encode credentials for Basic Auth
            auth_string = f"{self.instance_id}:{self.api_key}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            
            # Format timestamp for Loki (nanoseconds since epoch)
            timestamp = str(int(time.time() * 1e9))
            
            # Create log payload similar to frontend structure
            log_data = {
                "timestamp": time.time(),
                "level": level.value,
                "source": "soleda-backend",
                "event_name": event_name,
                "component": component,
                "userID": user_id,
                "details": details
            }
            
            # Add error information for ERROR and CRITICAL logs
            if level in [LogLevel.ERROR, LogLevel.CRITICAL]:
                log_data["error"] = error or {}
            
            # Ensure log_data is fully sanitized one more time before JSON serialization
            log_data = self._sanitize_data(log_data)
            
            # Create Loki payload with stream labels
            loki_payload = {
                "streams": [{
                    "stream": {
                        "service": "soleda-backend",
                        "level": level.value,
                        "component": component,
                        "event": event_name,
                        "env": self.environment,
                        "user_id": user_id
                    },
                    "values": [[
                        timestamp,
                        json.dumps(log_data, cls=CustomJSONEncoder)
                    ]]
                }]
            }
            
            # Forward to Loki endpoint with a shorter timeout
            async with httpx.AsyncClient(timeout=5.0) as client:  # 5 second timeout
                loki_response = await client.post(
                    self.loki_url,
                    json=loki_payload,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Basic {encoded_auth}"
                    }
                )
                
                if loki_response.status_code != 200 and loki_response.status_code != 204:
                    print(f"Loki response: {loki_response.status_code} - {loki_response.text}")
                    self.loki_available = False
                    return {"status": "error", "message": f"Failed to forward telemetry to Loki: {loki_response.text}"}
                
                self.loki_available = True
                return {"status": "success"}
                
        except httpx.ConnectTimeout:
            print(f"Connection timeout when sending logs to Loki")
            self.loki_available = False
            return {"status": "error", "message": "Connection timeout to Loki"}
        except Exception as e:
            print(f"Error forwarding telemetry: {str(e)}")
            self.loki_available = False
            return {"status": "error", "message": str(e)}
    
    # Thread-safe synchronous versions of logging methods
    def log_sync(self, level: LogLevel, component: str, event_name: str, details: Dict = None, 
                user_id: str = None, error: Dict = None):
        """
        Synchronous version of logging that works in any thread context.
        This method prints to console but doesn't attempt to send to Loki if there's no
        active event loop. Use this version in non-async contexts.
        """
        # Always print to console for immediate feedback
        user_id = user_id or self.default_user_id
        # Sanitize inputs for printing
        details_str = self._sanitize_data(details) if details else None
        error_str = self._sanitize_data(error) if error else None
        #details_str = str(sanitized_details) if sanitized_details else ""
        #error_str = str(sanitized_error) if sanitized_error else ""
        print(f"{level.value} - {component} - {event_name} - User: {user_id} - {details_str} {error_str}")
        
        try:
            # Try to get event loop and schedule async logging task if possible
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._send_to_loki(level, event_name, component, details, user_id, error))
        except RuntimeError:
            # No event loop available (like in thread context)
            # We already printed to console, so just continue
            pass
        
        return {"status": "printed"}
    
    async def debug(self, component: str, event_name: str, details: Dict = None, user_id: str = None):
        return await self._send_to_loki(LogLevel.DEBUG, event_name, component, details, user_id)
    
    async def info(self, component: str, event_name: str, details: Dict = None, user_id: str = None):
        return await self._send_to_loki(LogLevel.INFO, event_name, component, details, user_id)
    
    async def warning(self, component: str, event_name: str, details: Dict = None, user_id: str = None):
        return await self._send_to_loki(LogLevel.WARNING, event_name, component, details, user_id)
    
    async def error(self, component: str, event_name: str, details: Dict = None, user_id: str = None, error: Dict = None):
        return await self._send_to_loki(LogLevel.ERROR, event_name, component, details, user_id, error)
    
    async def critical(self, component: str, event_name: str, details: Dict = None, user_id: str = None, error: Dict = None):
        return await self._send_to_loki(LogLevel.CRITICAL, event_name, component, details, user_id, error)
   
    # Helper method for safely serializing objects
    def serialize_safely(self, obj: Any) -> Any:
        return self._sanitize_data(obj)
        
    async def forward_telemetry(self, log_data: Dict[str, Any]) -> Dict:
        """
        Forward telemetry data from the frontend to Loki
        
        Args:
            log_data: Telemetry data from the frontend
            
        Returns:
            Dict with status of the operation
        """
        if not self.api_key:
            print(f"Telemetry: {json.dumps(log_data, cls=CustomJSONEncoder)}")
            return {"status": "warning", "message": "API key not configured"}
        
        # Sanitize log data to ensure it's JSON serializable
        log_data = self._sanitize_data(log_data)
        
        # Encode credentials for Basic Auth
        auth_string = f"{self.instance_id}:{self.api_key}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        
        # Format timestamp for Loki (nanoseconds since epoch)
        timestamp = str(int(time.time() * 1e9))
        
        # Use default user if not specified
        user_id = log_data.get("userID", self.default_user_id)
        session_id = log_data.get("sessionID", "none")
        
        # Create Loki payload with your custom structure in the log line
        loki_payload = {
            "streams": [{
                "stream": {
                    "service": "soleda-frontend",
                    "level": log_data.get("level", "INFO"),
                    "component": log_data.get("component", "unknown"),
                    "event": log_data.get("event_name", "unknown"),
                    "env": self.environment,
                    "user_id": user_id,
                    "session_id": session_id
                },
                "values": [[
                    timestamp,
                    json.dumps(log_data, cls=CustomJSONEncoder)  # Store the entire original structure
                ]]
            }]
        }
        
        try:
            # Forward to Loki endpoint only with UPDATED URL
            async with httpx.AsyncClient() as client:
                loki_response = await client.post(
                    self.loki_url,
                    json=loki_payload,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Basic {encoded_auth}"
                    }
                )
                            
                if loki_response.status_code != 200 and loki_response.status_code != 204:
                    print(f"Loki response: {loki_response.status_code} - {loki_response.text}")
                    return {"status": "error", "message": f"Failed to forward telemetry to Loki: {loki_response.text}"}
                
                return {"status": "success"}
                
        except Exception as e:
            print(f"Error forwarding telemetry: {str(e)}")
            return {"status": "error", "message": str(e)}

# Create a singleton instance of the logger
logger = SoledaLogger()
