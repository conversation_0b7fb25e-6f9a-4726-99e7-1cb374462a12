import json
import os
import pdb
import datetime

from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer, util
from sqlalchemy import create_engine, select, text
from sqlalchemy import MetaData

from sqlalchemy.orm import sessionmaker
from tqdm import tqdm as progress_bar

from database.tables import User, Agent, Conversation, Utterance, Intent, DialogueAct, Comment
from database.tables import Base

from utils.help import dax2intent, dact2dax, dialogue_acts

load_dotenv("database/.env")  # comment out if running in production

DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST")
DB_NAME = os.getenv("DB_NAME")

print(DB_NAME)

# model = SentenceTransformer("all-MiniLM-L12-v2")
data = json.load(open("database/annotations/seed_data.json", "r"))
convos = json.load(open('database/annotations/conversations_v3.7.3.json', 'r'))
intent_map = {intent["intent_name"]: idx for idx, intent in enumerate(data["Intents"])}
dact_map = {dact["dact"]: idx for idx, dact in enumerate(data["Dacts"])}

def make_session():
    engine_name = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"
    print(engine_name)
    print("engine name: ", engine_name)

    engine = create_engine(engine_name)
    Session = sessionmaker(bind=engine)
    session = Session()
    return session

def create_tables(engine):
    Base.metadata.create_all(engine)

def autogenerate_dact_combos():
  """ Method generates all combinations of dacts and dax given the list above.
  This updates global dact_map with the autogenerated values. """
  # first load the existing dacts
  full_dact_list = [existing_dact['dact'] for existing_dact in data["Dacts"]]

  # then generate the rest to maintain order
  for idx_1, dact_1 in enumerate(dialogue_acts):
    for idx_2, dact_2 in enumerate(dialogue_acts[idx_1+1:]):
      for idx_3, dact_3 in enumerate(dialogue_acts[idx_2+1:]):
        # ignore the ones already in our seed_data
        new_dact = dact_1 + " + " + dact_2 + " + " + dact_3
        if new_dact not in full_dact_list:
          full_dact_list.append(new_dact)
  return full_dact_list

def build_new_dialog_act(dact):
    dax = dact2dax(dact)
    intent = dax2intent(dax)

    dact_index = dact_map.get(dact, -1)
    if dact_index > 0:
        description = data["Dacts"][dact_index]["description"]
    else:
        description = f"This is an undefined dact that is part of the {intent} category"

    intent_id = intent_map[intent]
    dialog_act = DialogueAct(dact=dact, dax=dax, description=description, intent_id=intent_id, agent_id=1)
    return dialog_act

def populate_tables(session):
    for category, examples in data.items():
        for example in examples:
            if category == "Users":
                new_item = User(**example)
                new_item.set_password(new_item.password)
            elif category == "Agents":
                new_item = Agent(**example)
            elif category == "Intents":
                new_item = Intent(**example)
            elif category == "Dacts":
                new_item = build_new_dialog_act(example['dact'])
            session.merge(new_item)
            session.commit()
        print(f"Completed {category} with {len(examples)}")

    dax_list = autogenerate_dact_combos()
    for dact_name in dax_list:
        new_item = build_new_dialog_act(dact_name)
        session.merge(new_item)
        session.commit()
    print("Completed auto-generated dacts and dax")

def populate_conversations(session):
    # fill autogenerated dact and dax
    for convo in progress_bar(convos, total=len(convos)):
        new_conv = Conversation(user_id=3, agent_id=1, status='completed', structure=None)
        session.add(new_conv)
        for turn in convo["turns"]:
            if turn["speaker"] == "User":
                intent, dact_list = turn["intent"], turn["dact"]
                dact_id = dact_map[dact_list]
                core_result, ops = turn.get("core_result", ""), turn.get("operations", [])
                new_turn = Utterance(
                    speaker=turn['speaker'],
                    utt_id=turn['utt_id'],
                    text=turn['text'],
                    operations=ops,
                    entity=[{"core_result": core_result}],
                    dact_id=dact_id,
                    conversation=new_conv
                )
            if turn["speaker"] == "Agent":
                new_turn = Utterance(
                    speaker=turn['speaker'],
                    utt_id=turn['utt_id'],
                    text=turn['text'],
                    conversation=new_conv
                )

            session.add(new_turn)
        session.commit()
    session.close()


if __name__ == "__main__":
    session = make_session()
    populate_tables(session)
    populate_conversations(session)