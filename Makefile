# Project variables
VENV_NAME=.venv
PYTHON=${VENV_NAME}/bin/python
PYTHONPATH=${PWD}

# TODO: This Makefile isn't compatible with the Anacdona setup from the README. `.venv` doesn't reference the correct location, and assumes that it's a local dir.

# Default target executed when no arguments are given to make.
default: run

.PHONY: setup
setup:
	test -d $(VENV_NAME) || python3 -m venv $(VENV_NAME)
	$(VENV_NAME)/bin/pip install --upgrade pip
	$(VENV_NAME)/bin/pip install -r requirements.txt
	$(VENV_NAME)/bin/python -m textblob.download_corpora

.PHONY: run-dev
run-dev:
	PYTHONPATH=${PYTHONPATH} $(VENV_NAME)/bin/uvicorn backend.webserver:app --reload

.PHONY: run-prod
run-prod:
	PYTHONPATH=${PYTHONPATH} $(VENV_NAME)/bin/uvicorn backend.webserver:app --port 8000

# .PHONY: lint
# lint:
# 	PYTHONPATH=$(PYTHONPATH) $(VENV_NAME)/bin/autopep8 . --check

# .PHONY: format
# format:
# 	PYTHONPATH=$(PYTHONPATH) $(VENV_NAME)/bin/autopep8 --in-place --recursive .

.PHONY: requirements
requirements:
	PYTHONPATH=${PYTHONPATH} $(VENV_NAME)/bin/pip freeze > requirements.txt

.PHONY: db
db:
	docker-compose up -d

.PHONY: db-migrate
db-migrate:
	cd database && PYTHONPATH=${PYTHONPATH}  ../${VENV_NAME}/bin/alembic upgrade head

.PHONY: db-history
db-history:
	cd database &&  PYTHONPATH=${PYTHONPATH} ../${VENV_NAME}/bin/alembic history

.PHONY: db-downgrade
db-downgrade:
	cd database && PYTHONPATH=${PYTHONPATH} ../${VENV_NAME}/bin/alembic downgrade -1

.PHONY: db-seed
db-seed:
	PYTHONPATH=${PYTHONPATH} ${PYTHON} utils/scripts/build_postgres_seed.py

.PHONY: db-reset
db-reset:
	PYTHONPATH=${PYTHONPATH} ${PYTHON} utils/scripts/armageddon_and_rebirth.py

.PHONY: db-new-migration
db-new-migration:
	@read -p "Enter migration message: " MESSAGE; \
	cd database && PYTHONPATH=${PYTHONPATH} ../${VENV_NAME}/bin/alembic revision --autogenerate -m "$$MESSAGE"

docker-reset:
	docker compose down
	docker volume rm soleda
	docker compose up --build -d
