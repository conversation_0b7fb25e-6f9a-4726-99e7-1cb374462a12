{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^2.0.3", "autoprefixer": "^10.4.14", "flowbite-svelte-icons": "^0.4.4", "postcss": "^8.4.23", "svelte": "^4.2.17", "tailwindcss": "^3.4.17", "vite": "^4.4.2"}, "dependencies": {"@amplitude/analytics-browser": "^2.12.2", "@floating-ui/dom": "^1.5.1", "@fontsource/merriweather": "5.0.8", "@popperjs/core": "^2.11.7", "@stripe/stripe-js": "^7.0.0", "@sveltejs/adapter-auto": "^2.1.0", "@sveltejs/adapter-node": "^1.3.1", "@sveltejs/kit": "^1.22.4", "@tailwindcss/typography": "^0.5.9", "@types/papaparse": "^5.3.15", "classnames": "^2.3.2", "date-fns": "^2.30.0", "express": "^4.18.2", "express-http-proxy": "^2.0.0", "express-ws": "^5.0.2", "flowbite": "^2.3.0", "flowbite-svelte": "^0.44.12", "gtag": "^1.0.1", "http-proxy-middleware": "^2.0.6", "papaparse": "^5.5.2", "plotly.js-dist": "^2.22.0", "svelte-file-dropzone": "^2.0.1", "svelte-grid-responsive": "^1.2.4", "svelte-keyed": "^1.1.7", "svelte-kit": "^1.2.0", "tailwind-merge": "^1.14.0", "uuid": "^11.1.0", "ws": "^8.14.2", "xlsx": "^0.18.5"}}