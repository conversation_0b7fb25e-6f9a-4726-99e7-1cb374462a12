from dotenv import load_dotenv
from urllib.parse import urlencode, quote
import json
import os
from pathlib import Path
from backend.utilities.oauth_config import PROVIDER_CONFIG
from backend.utilities.pkce_utils import create_pkce_pair
from backend.utilities.verifier_store import store_verifier

load_dotenv()

GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
FACEBOOK_CLIENT_ID = os.getenv("FACEBOOK_CLIENT_ID")
FACEBOOK_CLIENT_SECRET = os.getenv("FACEBOOK_CLIENT_SECRET")
HUBSPOT_CLIENT_ID = os.getenv("HUBSPOT_CLIENT_ID")
HUBSPOT_CLIENT_SECRET = os.getenv("HUBSPOT_CLIENT_SECRET")
SALESFORCE_CLIENT_ID = os.getenv("SALESFORCE_CLIENT_ID")
SALESFORCE_CLIENT_SECRET = os.getenv("SALESFORCE_CLIENT_SECRET")
OAUTH_CALLBACK_URL = os.getenv("OAUTH_CALLBACK_URL")

# Generate OAuth URL for popup window
def create_oauth_url(data_source: str, state: str = None):
  if data_source not in PROVIDER_CONFIG:
    raise ValueError(f"Unsupported data source: {data_source}")
  
  config = PROVIDER_CONFIG[data_source]
  params = {
    "client_id": GOOGLE_CLIENT_ID if data_source in ["drive", "ga4", "google"] 
                 else FACEBOOK_CLIENT_ID if data_source == "facebook"
                 else HUBSPOT_CLIENT_ID if data_source == "hubspot"
                 else SALESFORCE_CLIENT_ID,
    "redirect_uri": OAUTH_CALLBACK_URL,
    "response_type": "code",
    "scope": " ".join(config["scopes"])
  }
  
  # Special handling for different OAuth providers
  if data_source in ["drive", "ga4", "google"]:
    params.update({
      "access_type": "offline",
      "prompt": "consent"
    })
  elif data_source == "hubspot":
    params["client_secret"] = HUBSPOT_CLIENT_SECRET
  elif data_source == "salesforce":
    # Add PKCE for Salesforce
    code_verifier, code_challenge = create_pkce_pair()
    store_verifier(state, code_verifier)
    
    params.update({
      "code_challenge": code_challenge,
      "code_challenge_method": "S256"
    })
      
  return config["auth_url"] + "?" + urlencode(params)

# Create token request payload
def create_token_request(data_source: str, code: str, code_verifier: str = None, credential: dict = None):
  if data_source not in PROVIDER_CONFIG:
    raise ValueError(f"Unsupported data source: {data_source}")

  config = PROVIDER_CONFIG[data_source]["token_request"]
  
  # Handle Salesforce specific case
  if data_source == "salesforce" and credential and credential.get("instance_url"):
    config = config.copy()
    config["url"] = credential["instance_url"] + "/services/oauth2/token"
  
  # Create payload from template
  payload = config["payload_template"].copy()
  payload["redirect_uri"] = OAUTH_CALLBACK_URL
  payload["code"] = code
  
  # Add client credentials
  if data_source in ["drive", "ga4", "google"]:
    payload["client_id"] = GOOGLE_CLIENT_ID
    payload["client_secret"] = GOOGLE_CLIENT_SECRET
  elif data_source == "hubspot":
    payload["client_id"] = HUBSPOT_CLIENT_ID
    payload["client_secret"] = HUBSPOT_CLIENT_SECRET
  elif data_source == 'facebook':
    payload["client_id"] = FACEBOOK_CLIENT_ID
    payload["client_secret"] = FACEBOOK_CLIENT_SECRET
  elif data_source == 'salesforce':
    payload["client_id"] = SALESFORCE_CLIENT_ID
    payload["client_secret"] = SALESFORCE_CLIENT_SECRET
    payload["code_verifier"] = code_verifier

  return config["url"], payload, config["headers"]

def create_resource_request(data_source: str, config: dict, access_token: str, instance_url: str = None):
  ACCESS_TOKEN = access_token
  if data_source == 'drive':
    scope = config['scope']
    scope_dict = json.loads(scope) if isinstance(scope, str) else scope
    # Create array of URLs for fetching tables
    file_id, tab_names = scope_dict.get('id'), scope_dict.get('tabNames')
    urls = []
    for tab_name in tab_names:
      # Properly quote the tab name and add range specification
      quoted_range = f"'{tab_name}'!A1:Z1024"
      url = f"https://sheets.googleapis.com/v4/spreadsheets/{file_id}/values/{quote(quoted_range)}"
      urls.append(url)

    headers = {
      'Authorization': f'Bearer {ACCESS_TOKEN}',
      'Accept': 'application/json'
    }
    payload = {}
    return urls, headers, payload

  elif data_source == 'ga4':
    url = f"https://analyticsdata.googleapis.com/v1beta/properties/{config['propertyId']}:runReport"
    headers = {
      "Authorization": f"Bearer {ACCESS_TOKEN}",
      "Content-Type": "application/json"
    }
    # Using a relative path to the JSON file
    current_dir = os.path.dirname(os.path.realpath(__file__))
    json_path = Path(current_dir).joinpath('ga4_mappings.json')

    with open(json_path, 'r') as file:
      mappings = json.load(file)

    DIMENSION_MAP = mappings['DIMENSION_MAP']
    METRIC_MAP = mappings['METRIC_MAP']
    mapped_dimensions = [{"name": DIMENSION_MAP[dim]} for dim in config['dimensions'] if dim in DIMENSION_MAP]
    mapped_metrics = [{"name": METRIC_MAP[metric]} for metric in config['metrics'] if metric in METRIC_MAP]
    
    start_date = config.get('dateRange', {}).get('startDate')
    end_date = config.get('dateRange', {}).get('endDate')
    
    payload = {
      "dimensions": mapped_dimensions,
      "metrics": mapped_metrics,
      "dateRanges": [{"startDate": start_date, "endDate": end_date}],
      "keepEmptyRows": False,
      "returnPropertyQuota": True,
    }
    
  elif data_source == 'google':
    account_id = config.get('accountId', '').replace('-', '')
    url = f"https://googleads.googleapis.com/v18/customers/{account_id}/googleAds:searchStream"
    headers = {
      "Authorization": f"Bearer {ACCESS_TOKEN}",
      "Content-Type": "application/json",
      "developer-token": os.getenv("GOOGLE_ADS_DEVELOPER_TOKEN"),
      "login-customer-id": account_id,
      "customer-id": account_id
    }
    
    # Ensure minimum required fields are present
    required_fields = ['campaign.id', 'campaign.name', 'metrics.clicks', 'metrics.impressions']
    selected_fields = list(set(config.get('selectedFields', []) + required_fields))
        
    fields_str = ', '.join(selected_fields)
    campaign_ids = config.get('campaignIds')
    
    start_date = config.get('dateRange', {}).get('startDate')
    end_date = config.get('dateRange', {}).get('endDate')
    
    campaign_filter = (
      f"campaign.id IN ({','.join(map(str, campaign_ids))})" 
      if len(campaign_ids) > 1 
      else f"campaign.id = {campaign_ids[0]}"
    )
    
    where_clauses = [
      campaign_filter,
      f"segments.date >= '{start_date}' AND segments.date <= '{end_date}'"
    ]
    where_clause = " WHERE " + " AND ".join(where_clauses)
    
    query = f"SELECT {fields_str} FROM campaign{where_clause}"
    
    payload = { "query": query.strip() }

  elif data_source == 'hubspot':
    contacts = 'https://api.hubapi.com/crm/v3/objects/contacts'
    companies = 'https://api.hubapi.com/crm/v3/objects/companies'
    deals = 'https://api.hubapi.com/crm/v3/objects/deals'
    if config['scope'] == 'contacts':
      url = contacts
    elif config['scope'] == 'companies':
      url = companies
    elif config['scope'] == 'deals':
      url = deals
    headers = {
      "Authorization": f"Bearer {ACCESS_TOKEN}",
      "Content-Type": "application/json"
    }
    payload = {}

  elif data_source == 'salesforce':
    scope = config['scope']
    
    try:
      base_url = f"{instance_url}/services/data/v58.0/query?q="
    
      if scope == 'accounts':
          query = "SELECT+Id,Name,Industry,Type,BillingAddress,Phone,Website+FROM+Account"
      elif scope == 'contacts':
          query = "SELECT+Id,FirstName,LastName,Email,Phone,Title,Department+FROM+Contact"
      elif scope == 'opportunities':
          query = "SELECT+Id,Name,StageName,Amount,CloseDate,Type,Probability+FROM+Opportunity"
      elif scope == 'leads':
          query = "SELECT+Id,FirstName,LastName,Email,Phone,Company,Status+FROM+Lead"
      elif scope == 'cases':
          query = "SELECT+Id,CaseNumber,Subject,Status,Priority,CreatedDate+FROM+Case"
      url = base_url + query

      headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
      }
      payload = {}
    except Exception as e:
      print(f"Error creating Salesforce resource request: {str(e)}")
      # Return a dummy URL that will trigger an error with a clear message
      url = f"{instance_url}/error/invalid_instance_url"
      headers = {"Content-Type": "application/json"}
      payload = {}

  elif data_source == 'facebook':
    try:
      campaign_ids = config.get('campaignIds')
      account_id = config.get('accountId')
      
      # Validate account_id
      if not account_id:
        raise ValueError("Facebook Ad Account ID is required")
      
      # Use ad account ID for insights
      url = f'https://graph.facebook.com/v18.0/{account_id}/insights'
      headers = {
        'Authorization': f'Bearer {access_token}',
        'Accept': 'application/json'
      }
      
      start_date = config.get('dateRange', {}).get('startDate')
      end_date = config.get('dateRange', {}).get('endDate')
            
      if not start_date or not end_date:
        raise ValueError("Date range is required for Facebook Ads data")
      
      params = {
        'time_range': json.dumps({
          'since': start_date,
          'until': end_date
        }),
        'fields': 'campaign_id,campaign_name,spend,impressions,clicks,reach',
        'level': 'campaign',
      }
      
      # Add campaign filter if specified
      if campaign_ids and len(campaign_ids) > 0:
        # For multiple campaigns, use IN operator
        if len(campaign_ids) > 1:
          params['filtering'] = json.dumps([{
            'field': 'campaign.id',
            'operator': 'IN',
            'value': campaign_ids
          }])
        else:
          # Single campaign uses EQUAL operator
          params['filtering'] = json.dumps([{
            'field': 'campaign.id',
            'operator': 'EQUAL',
            'value': campaign_ids[0]
          }])
      
      return url, headers, params
      
    except ValueError as ve:
      print(f"Validation error creating Facebook resource request: {str(ve)}")
      raise
    except Exception as e:
      print(f"Error creating Facebook resource request: {str(e)}")
      raise

  return url, headers, payload