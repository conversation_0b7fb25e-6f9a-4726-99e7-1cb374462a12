#!/usr/bin/env python
"""
Database Sync Script: This script synchronizes the production database with staging.
It will:
1. Drop all tables in production
2. Copy the schema from staging to production
3. Copy the data from staging to production
"""

import os
import sys
import time
import argparse
from dotenv import load_dotenv
from sqlalchemy import create_engine, MetaData, Table, select, text, inspect
from sqlalchemy.schema import CreateTable
from pgvector.sqlalchemy import Vector

DIRNAME = os.path.dirname(__file__)
ENV_PATH = os.path.abspath(os.path.join(DIRNAME, "..", ".env"))
load_dotenv(ENV_PATH)

def get_db_connection(is_production):
    """Create database connection based on environment"""
    if is_production:
        db_dsn = os.getenv("PRODUCTION_DB_DSN")
        env_name = "PRODUCTION"
    else:
        db_dsn = os.getenv("STAGING_DB_DSN")
        env_name = "STAGING"
    
    if not db_dsn:
        raise ValueError(f"{env_name}_DB_DSN environment variable not set")
    
    # Fix DSN format for SQLAlchemy
    db_dsn = db_dsn.replace("postgres://", "postgresql://")
    return create_engine(db_dsn)



def drop_all_tables(engine, dry_run=False):
    """Drop all tables in the database"""
    print("Dropping all tables in the database...")
    
    sql_statements = []
    
    # Get all table names from target (production) database
    with engine.connect() as conn:
        result = conn.execute(text(
            "SELECT tablename FROM pg_tables WHERE schemaname = 'public';"
        ))
        tables = [row[0] for row in result]
    
    if not tables:
        print("No tables found to drop")
        return sql_statements if dry_run else None
    
    print(f"Found {len(tables)} tables to drop: {', '.join(tables)}")
    
    # Add drop statements for each table
    for table in tables:
        print(f"{'Would drop' if dry_run else 'Dropping'} table: {table}")
        sql_statements.append(f'DROP TABLE IF EXISTS "{table}" CASCADE;')
    
    # Also drop Alembic version table if it exists
    sql_statements.append('DROP TABLE IF EXISTS alembic_version;')
    
    if dry_run:
        return sql_statements
    else:
        with engine.connect() as conn:
            for statement in sql_statements:
                conn.execute(text(statement))
            conn.commit()
        
        print("All tables dropped successfully")
        return None

def copy_schema(source_engine, target_engine, dry_run=False):
    """Copy schema from source to target database"""
    print("Copying schema from staging to production...")
    
    sql_statements = []
    
    # Enable vector extension if not exists
    sql_statements.append("CREATE EXTENSION IF NOT EXISTS vector;")
    
    # Get metadata from source database
    source_metadata = MetaData()
    source_metadata.reflect(bind=source_engine)
    
    # Handle vector types for conversation table
    if 'conversation' in source_metadata.tables:
        table = source_metadata.tables['conversation']
        table.columns['short_embed'].type = Vector(384)
        table.columns['medium_embed'].type = Vector(768)
        table.columns['long_embed'].type = Vector(1536)
    
    if dry_run:
        # First, generate all CREATE TABLE statements
        for table_name, table in source_metadata.tables.items():
            # Skip alembic_version as we handle it separately
            if table_name == 'alembic_version':
                continue
                
            # Get the CREATE TABLE statement
            create_stmt = str(CreateTable(table).compile(dialect=target_engine.dialect))
            sql_statements.append(create_stmt + ";")
        
        # Handle alembic_version table
        try:
            inspector = inspect(source_engine)
            if 'alembic_version' in inspector.get_table_names():
                with source_engine.connect() as conn:
                    result = conn.execute(text("SELECT version_num FROM alembic_version;"))
                    version = result.scalar_one_or_none()
                
                sql_statements.append("CREATE TABLE IF NOT EXISTS alembic_version (version_num VARCHAR(32) NOT NULL);")
                
                if version:
                    sql_statements.append(f"INSERT INTO alembic_version VALUES ('{version}');")
        except Exception as e:
            print(f"Warning: Could not generate SQL for alembic_version: {e}")
        
        return sql_statements
    else:
        # Actually create the tables
        with target_engine.connect() as conn:
            # Enable vector extension
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
            
            # Create all tables
            source_metadata.create_all(target_engine)
            
            # Copy alembic_version table to maintain migration state
            try:
                # Check if alembic_version exists in source
                inspector = inspect(source_engine)
                if 'alembic_version' in inspector.get_table_names():
                    # Get the version from source
                    with source_engine.connect() as source_conn:
                        result = source_conn.execute(text("SELECT version_num FROM alembic_version;"))
                        version = result.scalar_one_or_none()
                    
                    # Create alembic_version table in target
                    conn.execute(text("CREATE TABLE IF NOT EXISTS alembic_version (version_num VARCHAR(32) NOT NULL);"))
                    
                    # Insert the version number
                    if version:
                        conn.execute(text(f"INSERT INTO alembic_version VALUES ('{version}');"))
            except Exception as e:
                print(f"Warning: Could not copy alembic_version: {e}")
            
            conn.commit()
        
        print("Schema copied successfully")
        return None

def get_tables_in_dependency_order(metadata):
    """Return tables in order based on foreign key dependencies"""
    # Dictionary to store tables and their dependencies
    dependencies = {}
    for table_name, table in metadata.tables.items():
        dependencies[table_name] = set()
        for fkey in table.foreign_keys:
            # Add the table this foreign key references to dependencies
            dependencies[table_name].add(fkey.column.table.name)
    
    # Find tables with no dependencies
    independent_tables = set(table for table, deps in dependencies.items() if not deps)
    ordered_tables = list(independent_tables)
    tables_to_process = set(dependencies.keys()) - independent_tables
    
    while tables_to_process:
        # Find tables whose dependencies are all satisfied
        ready_tables = set()
        for table in tables_to_process:
            if dependencies[table].issubset(set(ordered_tables)):
                ready_tables.add(table)
        
        if not ready_tables:
            remaining = ', '.join(tables_to_process)
            raise ValueError(f"Circular dependency detected among tables: {remaining}")
        
        # Add the ready tables to our ordered list
        ordered_tables.extend(ready_tables)
        tables_to_process -= ready_tables
    
    return ordered_tables

def copy_data(source_engine, target_engine, dry_run=False):
    """Copy all data from source to target database"""
    print("Copying data from staging to production...")
    
    sql_statements = []
    
    # Get metadata from source database
    source_metadata = MetaData()
    source_metadata.reflect(bind=source_engine)
    
    # Handle vector types for conversation table
    if 'conversation' in source_metadata.tables:
        table = source_metadata.tables['conversation']
        table.columns['short_embed'].type = Vector(384)
        table.columns['medium_embed'].type = Vector(768)
        table.columns['long_embed'].type = Vector(1536)
    
    # Get tables in correct dependency order
    try:
        table_order = get_tables_in_dependency_order(source_metadata)
        print("Tables will be copied in this order:", ", ".join(table_order))
    except ValueError as e:
        print(f"Error determining table order: {e}")
        raise
    
    if dry_run:
        # Generate INSERT statements for sample data
        for table_name in table_order:
            # Skip alembic_version table
            if table_name == 'alembic_version':
                continue
                
            table = source_metadata.tables[table_name]
            print(f"Analyzing data for table: {table_name}")
            
            try:
                # Get count and sample data from source
                with source_engine.connect() as source_conn:
                    # Get count
                    count_result = source_conn.execute(text(f"SELECT COUNT(*) FROM \"{table_name}\""))
                    row_count = count_result.scalar_one()
                    
                    # Get sample data (first 5 rows max)
                    sample_result = source_conn.execute(select(table).limit(5))
                    sample_data = sample_result.fetchall()
                
                if row_count == 0:
                    print(f"  No data found in table {table_name}")
                    continue
                
                print(f"  Found {row_count} rows to copy")
                
                # For dry run, just show sample INSERT statements
                if sample_data:
                    sql_statements.append(f"-- Table: {table_name} (showing sample of {min(5, row_count)} out of {row_count} rows)")
                    
                    for row in sample_data:
                        columns = ", ".join([f'"{column}"' for column in row._mapping.keys()])
                        values = []
                        for value in row._mapping.values():
                            if value is None:
                                values.append("NULL")
                            elif isinstance(value, (int, float)):
                                values.append(str(value))
                            else:
                                # Escape single quotes and wrap in quotes
                                escaped_value = str(value).replace("'", "''")
                                values.append(f"'{escaped_value}'")
                        
                        values_str = ", ".join(values)
                        sql_statements.append(f"INSERT INTO \"{table_name}\" ({columns}) VALUES ({values_str});")
                    
                    # Add comment indicating more rows
                    if row_count > 5:
                        sql_statements.append(f"-- ... and {row_count - 5} more rows")
            except Exception as e:
                print(f"Error processing table {table_name}: {e}")
                continue
        
        return sql_statements
    
    # Actually copy the data
    with target_engine.connect() as target_conn:
        # Start a transaction
        with target_conn.begin():
            # Create target metadata once
            target_metadata = MetaData()
            target_metadata.reflect(bind=target_engine)
            
            # For each table in dependency order, copy data
            for table_name in table_order:
                # Skip alembic_version table
                if table_name == 'alembic_version':
                    continue
                
                print(f"Copying data for table: {table_name}")
                source_table = source_metadata.tables[table_name]
                
                try:
                    # Get target table reference
                    target_table = target_metadata.tables[table_name]
                    
                    # Get row count first
                    with source_engine.connect() as source_conn:
                        count_result = source_conn.execute(text(f"SELECT COUNT(*) FROM \"{table_name}\""))
                        total_rows = count_result.scalar_one()
                    
                    if total_rows == 0:
                        print(f"  No data found in table {table_name}")
                        continue
                    
                    print(f"  Found {total_rows} rows to copy in {table_name}")
                    
                    # Copy data in batches using server-side cursor
                    batch_size = 1000
                    with source_engine.connect() as source_conn:
                        # Enable server-side cursor for memory efficiency
                        source_conn.execution_options(stream_results=True)
                        result = source_conn.execute(select(source_table))
                        
                        batch = []
                        rows_processed = 0
                        
                        for row in result:
                            batch.append(dict(row._mapping))
                            rows_processed += 1
                            
                            if len(batch) >= batch_size:
                                try:
                                    # Insert batch
                                    target_conn.execute(target_table.insert(), batch)
                                    print(f"  Copied batch {rows_processed//batch_size}/{(total_rows + batch_size - 1)//batch_size}")
                                    batch = []
                                except Exception as e:
                                    print(f"  Error copying batch in table {table_name}: {e}")
                                    raise  # Re-raise to trigger rollback
                        
                        # Insert final partial batch if any
                        if batch:
                            try:
                                target_conn.execute(target_table.insert(), batch)
                                print(f"  Copied final batch - {rows_processed} total rows copied")
                            except Exception as e:
                                print(f"  Error copying final batch in table {table_name}: {e}")
                                raise  # Re-raise to trigger rollback
                
                except Exception as e:
                    print(f"Error copying data for table {table_name}: {e}")
                    raise  # Re-raise to trigger rollback
    
    print("Data copied successfully")
    return None

def write_sql_to_file(sql_statements, filename):
    """Write SQL statements to a file"""
    with open(filename, 'w') as f:
        for statement in sql_statements:
            f.write(statement + "\n")
    print(f"SQL statements written to {filename}")

def main():
    parser = argparse.ArgumentParser(description='Sync production database with staging.')
    parser.add_argument('--schema-only', action='store_true', help='Only copy schema, not data')
    parser.add_argument('--force', action='store_true', help='Skip confirmation prompts')
    parser.add_argument('--dry-run', action='store_true', help='Generate SQL without executing it')
    parser.add_argument('--output-file', help='File to write SQL statements to (for dry run)')
    
    args = parser.parse_args()
    
    # Connect to databases
    staging_engine = get_db_connection(is_production=False)
    production_engine = get_db_connection(is_production=True)
    
    # Display database info
    staging_db = staging_engine.url.database
    prod_db = production_engine.url.database
    
    print("\n=== Database Sync Tool ===")
    print(f"Staging database: {staging_db} ({staging_engine.url.host})")
    print(f"Production database: {prod_db} ({production_engine.url.host})")
    print(f"Mode: {'Dry run (SQL generation only)' if args.dry_run else 'Execute'}")
    
    # Confirm operation if not in dry run mode
    if not args.dry_run and not args.force:
        confirm = input("\nWARNING: This will DELETE ALL DATA in the production database. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Operation cancelled.")
            sys.exit(0)
    
    # Start timer
    start_time = time.time()
    
    all_sql_statements = []
    
    # Process in dry run or execute mode
    if args.dry_run:
        print("\n=== Generating SQL statements (dry run) ===")
        
        # Generate drop table SQL
        print("\n--- Drop Tables SQL ---")
        drop_sql = drop_all_tables(production_engine, dry_run=True)
        all_sql_statements.extend(drop_sql)
        
        # Generate schema copy SQL
        print("\n--- Schema Creation SQL ---")
        schema_sql = copy_schema(staging_engine, production_engine, dry_run=True)
        all_sql_statements.extend(schema_sql)
        
        # Generate data copy SQL if not schema-only
        if not args.schema_only:
            print("\n--- Data Copy SQL (sample) ---")
            data_sql = copy_data(staging_engine, production_engine, dry_run=True)
            all_sql_statements.extend(data_sql)
        else:
            print("\nSkipping data copy SQL generation as requested (schema-only mode).")
        
        # Write SQL to file if output file specified
        if args.output_file:
            write_sql_to_file(all_sql_statements, args.output_file)
        else:
            # Print SQL to console
            print("\n=== Generated SQL ===")
            for stmt in all_sql_statements:
                print(stmt)
    else:
        # Execute mode
        # Uncomment the following lines to execute, make sure you know what you are doing!
        # Drop all tables in production
        ### drop_all_tables(production_engine)
        
        # Copy schema from staging to production
        ### copy_schema(staging_engine, production_engine)
        
        '''
        # Copy data if not schema-only
        if not args.schema_only:
            copy_data(staging_engine, production_engine)
        else:
            print("Skipping data copy as requested (schema-only mode).")
        '''
    
    # End timer
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n=== Operation Complete ===")
    print(f"Total time: {duration:.2f} seconds")
    
    if args.dry_run:
        print(f"SQL statements for syncing {prod_db} with {staging_db} generated successfully")
        if args.output_file:
            print(f"SQL written to: {args.output_file}")
    else:
        print(f"Production database {prod_db} has been synced with staging database {staging_db}")

if __name__ == "__main__":
    main()