version: "3.6"

services:
  postgres:
    image: ankane/pgvector
    # command: ["postgres", "-c", "log_statement=all"]
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: soleda
      PGDATA: /data/postgres
    volumes:
      - ./database/init_pgvector.sql:/docker-entrypoint-initdb.d/init.sql
      - postgres-db:/data/postgres
    ports:
      - "5432:5432"

volumes:
  postgres-db:
    name: soleda
