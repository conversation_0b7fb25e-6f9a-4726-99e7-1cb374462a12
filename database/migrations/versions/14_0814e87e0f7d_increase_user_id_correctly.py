"""increase User ID correctly

Revision ID: 0814e87e0f7d
Revises: dd2a0c8f3ab4
Create Date: 2025-04-09 21:23:52.496488

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text  


# revision identifiers, used by Alembic.
revision = '0814e87e0f7d'
down_revision = 'dd2a0c8f3ab4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create sequence if it doesn't exist
    op.execute("CREATE SEQUENCE IF NOT EXISTS user_id_seq")
    
    # Get current max ID and set sequence to start after it
    op.execute(text("SELECT setval('user_id_seq', (SELECT MAX(id) FROM \"user\"), true)"))
    
    # Alter the ID column to use the sequence
    op.execute("ALTER TABLE \"user\" ALTER COLUMN id SET DEFAULT nextval('user_id_seq')")

def downgrade() -> None:
    # Remove the default sequence value from the column
    op.execute("ALTER TABLE \"user\" ALTER COLUMN id DROP DEFAULT")
