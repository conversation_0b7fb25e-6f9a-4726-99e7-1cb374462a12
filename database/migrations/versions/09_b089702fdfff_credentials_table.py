"""09_credentials_table

Revision ID: b089702fdfff
Revises: 25be54d50732
Create Date: 2023-09-18 16:51:21.068602

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import DateTime


# revision identifiers, used by Alembic.
revision = 'b089702fdfff'
down_revision = '25be54d50732'
branch_labels = None
depends_on = None


def upgrade():
  op.create_table(
    'credentials',
    sa.Column('id', sa.Integer, nullable=False, primary_key=True),
    sa.Column('user_id', sa.Integer, nullable=False),
    sa.Column('access_token', sa.String, unique=True, index=True, nullable=False),
    sa.Column('refresh_token', sa.String, nullable=False),
    sa.Column('token_expiry', DateTime, nullable=False),
    sa.Column('vendor', sa.String, nullable=False), # Vendors we connect to such as GA4 or HubSpot
    sa.Column('vendor_id', sa.String, nullable=False), #User's id in vendor's system eg., Google property ID. 
    sa.Column('scope', sa.String), # Scope we are requesting access to such as user profile or 
    sa.Column('last_sync_time', DateTime),
    sa.Column('status', sa.String),
  )

def downgrade() -> None:
  op.drop_table('credentials')
