import sys
import json
import time
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from collections import defaultdict
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.webserver import app
from backend.db import get_db, load_engine
from backend.auth.credentials import get_auth_user_email
from utils.dependencies import get_test_db, load_test_engine

TEST_TOKEN = "secret_acromi_token_for_testing_123"

def get_test_user_email():
  return "<EMAIL>"

@pytest.fixture(scope="module")
def test_client():
  app.dependency_overrides[get_db] = get_test_db
  app.dependency_overrides[load_engine] = load_test_engine
  app.dependency_overrides[get_auth_user_email] = get_test_user_email
  
  client = TestClient(app)
  yield client
  
  app.dependency_overrides.clear()

@pytest.fixture(scope="module")
def websocket(test_client: TestClient):
  with test_client.websocket_connect(f"/api/v1/ws?token={TEST_TOKEN}") as ws:
    yield ws

def test_select_sheet(test_client):
  spreadsheet_data = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"],
  }
  response = test_client.post("/api/v1/sheets/select", json=spreadsheet_data)
  assert response.status_code == 200, f"Failed to select spreadsheet: {response.text}"

def decide_which_parts(parts):
  # determine which parts of the message to keep, returns 3 boolean values
  store_query = 'query' in parts
  store_code = 'code' in parts
  store_thought = 'thought' in parts
  return store_query, store_code, store_thought

def decide_part_availability(parsed):
  # determine which parts are available in the message, returns 3 boolean values
  has_query, has_code, has_thought = False, False, False
  
  if 'interaction' in parsed.keys():
    if parsed['interaction'] and 'content' in parsed['interaction']:
      if 'SQL Query' in parsed['interaction']['content']:
        has_query = True
      if 'Pandas Code' in parsed['interaction']['content']:
        has_code = True
      if parsed['interaction']['flowType'] == 'Default(thought)':
        has_thought = True
  return has_query, has_code, has_thought

def send_message(websocket, message:str, gold_dax:str, parts:list=[]):
  websocket.send_json( { 'currentMessage': message, 'dialogueAct': gold_dax, 'lastAction': None}, mode='binary')
  store_query, store_code, store_thought = decide_which_parts(parts)
  results = defaultdict(str)

  while True:
    try:
      raw_output = websocket.receive_json()
      
      has_query, has_code, has_thought = decide_part_availability(raw_output)
      if store_query and has_query:
        results['query'] = raw_output['interaction']['content'][19:]
      if store_code and has_code:
        results['code'] = raw_output['interaction']['content'][21:]
      if store_thought and has_thought:
        results['thought'] = raw_output['interaction']['content'][28:]
      if 'message' in raw_output.keys():
        results['message'] = raw_output['message']
        return results

    except json.JSONDecodeError as e:
      return f"Failed to decode JSON: {e}"

  return 'Error'

@pytest.mark.timeout(30)
def test_basic_faq_retrieval(websocket):
  turn_one = send_message(websocket, "Who made you?", '004')
  print("Turn 1 message:", turn_one)
  assert 'Soleda' in turn_one['message'], 'Missing name of creator'

@pytest.mark.timeout(30)
def test_technology_faq(websocket):
  turn_two = send_message(websocket, "What is the technology behind your ML models?", '004')
  print("Turn 2 message:", turn_two)
  assert 'model' in turn_two['message'], 'Missing model response'

@pytest.mark.timeout(30)
def test_pricing_faq(websocket):
  turn_three = send_message(websocket, "How much does Soleda cost?", '004')
  print("Turn 3 message:", turn_three)
  assert 'free' in turn_three['message'].lower(), 'Missing pricing information'