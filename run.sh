#!/bin/bash
set -xue

# Model predictions for testing nlu model
# For the folloeing nlu models, add --run-beta to run only the crutial dacts
# python3 main.py --debug --do-nlu --test-expert embed
# python3 main.py --debug --do-nlu --test-expert logreg
# python3 main.py --debug --do-nlu --temperature 0 --test-expert peft
# python3 main.py --do-nlu --temperature 0 --api-version gpt-4o --test-expert icl
# python3 main.py --do-nlu --temperature 0 --api-version claude-3-5-haiku-latest --test-expert icl
# python3 main.py --do-nlu --temperature 0 --api-version claude-3-5-sonnet-latest --test-expert icl
# python3 main.py --debug --do-nlu --temperature 0 --api-version gpt-4-1106-preview --test-expert icl --input-file ./database/annotations/conversations_v2.0.json

# Auto-label
#python3 main.py --debug --do-label --temperature 0 --api-version gpt-4-1106-preview \
#--input-file ./database/annotations/augmentations_label.json --output-dir ./database/annotations

# Run unit tests for the backend
pytest -sv utils/unit_tests/{000}_hello_world.py
pytest -sv utils/unit_tests/{001}_query_flow.py
pytest -sv utils/unit_tests/{002}_analyze_flow.py
pytest -sv utils/unit_tests/{003}_plot_flow.py
pytest -sv utils/unit_tests/{004}_retrieve_flow.py
pytest -sv utils/unit_tests/{005}_insert_flow.py
pytest -sv utils/unit_tests/{006}_update_flow.py
pytest -sv utils/unit_tests/{007}_delete_flow.py
pytest -sv utils/unit_tests/{014}_describe_flow.py
pytest -sv utils/unit_tests/{46B}_blank_and_problem.py
pytest -sv utils/unit_tests/{05A}_integrate_flow.py
pytest -sv utils/unit_tests/{05C}_merge_columns.py
pytest -sv utils/unit_tests/{5CD}_split_column.py
pytest -sv utils/unit_tests/{7BD}_remove_duplicates.py
pytest -sv utils/unit_tests/{36F}_format_flow.py