# name: CI/CD Pipeline

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - master
  # pull_request:
  #   branches:
  #     - master

jobs:
  build_and_test:
    runs-on: ubuntu-latest
    env: # Adding env section
      VITE_SERVER_URL: "http://localhost:8000"
      LOGREG_MODEL: "v4_acc835_lr6e-05_ont1.5.pt"

    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      # --- Backend steps (FastAPI) ---
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9

      - name: Install Backend Dependencies
        run: |
          pip install -r requirements.txt

      - name: Run Backend Locally
        run: |
          uvicorn backend.webserver:app --host 0.0.0.0 --port 8000 &
      
      # --- Frontend steps (Svelte) ---
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16.14'

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm install

      - name: Build Frontend Locally
        run: |
          cd frontend
          npm run build
          npx serve ./dist &

      # --- Testing steps ---
      - name: Run Backend Tests against Local Deployment
        run: |
          python3 main.py --verbose --debug --do-qa --temperature 0 --api-version gpt-4
      
      - name: Run Frontend Tests against Local Deployment
        run: |
          cd frontend
          npm test # Assuming you have frontend tests set up to run against the local build

      # --- Deployment steps ---
      - name: Deploy to Render
        if: ${{ success() }}
        run: ./deploy-to-render.sh
        env:
          RENDER_API_KEY: ${{ secrets.RENDER_API_KEY }}