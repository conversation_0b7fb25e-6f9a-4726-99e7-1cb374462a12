{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"lib": ["DOM", "ES2017", "WebWorker"], "paths": {"@src": ["./src"], "@shared": ["./src/routes/application/components/shared"], "@store": ["./src/routes/application/storage/store.js"], "@alert": ["./src/routes/application/storage/alert.js"], "$lib": ["./src/lib"], "$lib/*": ["./src/lib/*"], "@assets": ["./src/assets"], "@lib": ["./src/routes/application/lib"], "@sveltejs/*": ["./node_modules/@sveltejs/*"]}, "allowSyntheticDefaultImports": true}}