preloaded_descriptions = {
    "Shoe Store Sales": {
        "goal": "understand the sales trends of the store",
        "orders": "Orders table contains columns: order_id, product_id, customer_id, date, size, channel, price. Primary key is order_id. Channels are methods of placing an order (search engine, email, social media) with values such as search_google, email_existing, email_new_user or social_fb. date is a date column written as MM/DD/YYYY spanning from October 2023 to July 2025.",
        "customers": "Customers table contains columns: customer_id, first, last, city, state, member. Primary key is customer_id.",
        "products": "Products table contains columns: product_id, sku, type, brand, style, cost. Primary key is product_id. sku stands for stock keeping unit. Type values are either Kids, Men, or Women. Styles are types of shoes with values such as casual, fashion, streetwear or running."
    },
    "E-commerce Web Traffic": {
        "goal": "analyze website traffic patterns",
        "activities": "Activities table contains columns: ActivityID, ActivityType, ActivityTime, PageURL, UserAgent, ReferrerURL, DeviceID. Primary key is likely to be ActivityID. ActivityID is an ID column serving as a good candidate for joins. ActivityTypes are customer website interaction types with values such as view_product, search, add_to_cart, checkout, or visit_site. ActivityTime datetimes range from 2024-01-10 02:33:22 to 2024-02-19 21:16:45. PageURL are urls such as www.beautyshop.com/cart, www.beautyshop.com/checkout, www.beautyshop.com, or www.beautyshop.com/complete. UserAgent are general strings including Mozilla/5.0 (compatible; MSIE 5.0; Windows NT 4.0; Trident/3.0), Mozilla/5.0 (compatible; MSIE 5.0; Windows NT 4.0; Trident/3.1), Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; Trident/4.1), or Mozilla/5.0 (compatible; MSIE 6.0; Windows NT 5.01; Trident/3.0). ReferrerURL are general strings including Mail Client, www.beautyshop.com/checkout, www.google.com, or www.yahoo.com. DeviceID are general strings including 00DE7798-FF95-40C1-AB1D-C31CBB076581, 6d3d2040-36ef-49a2-8e82-66179c4bd2a5, A8BD2ACE-DFA0-41E4-9EBF-228D29E28BD5, or f235eca2-53ee-45b0-8e9a-f33a9f985bfd.",
        "purchases": "Purchases table contains columns: PurchaseID, ProductID, PurchaseTime, Quantity, Discount, Tax, UnitPrice, PaymentMethod, ShippingAddress. Primary key is likely to be PurchaseID. PurchaseID are general strings including 20500G, 21005H, 21224H, or 21222I. ProductID is an ID column serving as a good candidate for joins. PurchaseTime datetimes range from 2024-01-10 05:07:53 to 2024-02-19 16:40:11. Quantity values range from 1 to 8. Discount values range from 0.0 to 0.2. Tax values range from 0.07 to 0.0825. UnitPrice values range from 10 to 80. PaymentMethods are payment methods used for purchases with values such as Credit Card, Debit Card, PayPal, or Gift Card. ShippingAddress is related to locations and addresses.",
        "inventory": "Inventory table contains columns: ProductID, ProductName, ProductDescription, ProductPrice, Category, Supplier, InitialQuantity, CurrentQuantity, ReorderLevel, LastReplenishmentDate, NextReplenishmentDate. Primary key is likely to be ProductID. ProductID is an ID column serving as a good candidate for joins. ProductName are general strings including Gentle Hydrating for Dry Hair, Heat Protection Spray, Revitalizing Eye Cream, or Nourishing Oil for Split Ends. ProductDescription are general strings including Shampoo with aloe vera for revitalizing dry hair, Protect your hair from heat damage with our lightweight, non-greasy spray, Eye cream to reduce puffiness and dark circles, or A nourishing blend of oils that helps seal split ends and prevent future breakage. ProductPrice values range from 10 to 80. Categorys are categories of beauty products with values such as Skin Care, Hair Care, Makeup, Fragrance, or Beauty. Supplier are general strings including Hair Harmony Holistics, Eternal Youth Cosmetics, SkinLove Laboratories, or CrownGlow Creations. InitialQuantity values range from 100 to 700. CurrentQuantity values range from 0 to 534. ReorderLevel values range from 20 to 160. LastReplenishmentDate datetimes range from 2023-10-23 to 2024-05-25. NextReplenishmentDate datetimes range from 2024-07-02 to 2024-11-28."
    },
    "Customer Integration": {
        "goal": "integrate customer data from multiple sources",
        "Hubspot": "Hubspot table contains columns: LeadID, UserName, Source, VisitCounts, PageVisited, FirstVisitTime, CompanySize, DownloadedContent, FormSubmitted, FormSubmissionDateTime, LeadScore. Primary key is likely to be LeadID. LeadID is an ID column serving as a good candidate for joins. UserName are names such as Antonio Davies, Tanesha Edwards, Michelle Hall, or Victor Martinez. Sources are marketing channels/sources with values such as organic, referral, paid ad, social media, or direct. VisitCounts values range from 0 to 11. PageVisiteds are sections or categories of the company's website with values such as Helpdesk, FAQ, Pricing, Contact, or Features. FirstVisitTime datetimes range from 2023-11-21 15:29:42 to 2024-09-14 20:09:55. CompanySize are categories of company sizes with values including, but not limited to <10, 100 to 500, and 5000+. DownloadedContent value is either No or Yes. FormSubmitted value is either False or True. FormSubmissionDateTime datetimes range from 2023-11-21 15:29:42 to 2024-08-29 19:24:11. LeadScore values range from 20 to 100.",
        "Mailchimp": "Mailchimp table contains columns: Subscriber_ID, First_Name, Last_Name, Email, Campaign_ID, Campaign_Name, Campaign_Launch_Date, Opened, Clicked, Unsubscribed, List_Segment, Clicked_Link. Primary key is likely to be Subscriber_ID. Subscriber_ID is an ID column serving as a good candidate for joins. First_Name are names such as Ebony, Noah, Andrea, or Rashad. Last_Name are names such as Morales, Griffin, Washington, or Williams. Email are emails <NAME_EMAIL>, <EMAIL>, <EMAIL>, or <EMAIL>. Campaign_IDs are campaign identifiers or newsletter editions with values such as July-B, Aug-C, Aug-A, June-B, or July-A. Campaign_Names are marketing campaign themes or email newsletter titles with values such as Summer Special, Back to School, Anniversary Sale, Father's Day, or Independence Day. Campaign_Launch_Date datetimes range from 2024-06-14 00:00:00 to 2024-09-14 00:00:00. Opened value is either No or Yes. Clicked value is either No or Yes. Unsubscribed value is either N/A, No, or Yes. List_Segment are general strings including customers, prospects, VIPs, or opportunities. Clicked_Link are urls such as www.nexstream.com/faq, www.nexstream.com/features, www.nexstream.com/about, or www.nexstream.com/pricing.",
        "Salesforce": "Salesforce table contains columns: ContactID, FirstName, LastName, DateTimeJoined, EmailAddress, OpportunityID, Stage, DealSize, LastContactDate, NextStep, DecisionMaker, Location (city), Location (state), Location (country). Primary key is likely to be ContactID. ContactID is an ID column serving as a good candidate for joins. FirstName are names such as Hugo, Emma, Rashad, or Alessandro. LastName are names such as Mendez, Campbell, Gonzalez, or Thompson. DateTimeJoined datetimes range from 2023-11-11 02:42:07 to 2024-12-13 12:28:18. EmailAddress are emails such as missing, <EMAIL>, <EMAIL>, or <EMAIL>. OpportunityID are general strings including OPP-3402, OPP-5214, OPP-140, or OPP-3723. Stages are sales pipeline stages with values such as Purchased, Closed won, Closed lost, Negotiation, or Qualified. DealSize values range from 1.0 to 950.0. LastContactDate datetimes range from 2024-02-01 00:00:00 to 2024-12-22 00:00:00. NextStep are general strings including Purchased, Installation, Demo, or Follow-up. DecisionMaker value is either No or Yes. Location (city) are general strings including Phoenix, San Francisco, Tampa, or Garland. Location (state) is related to locations and addresses. Location (country) is related to locations and addresses.",
        "Zendesk": "Zendesk table contains columns: TicketID, Requester, IssueType, MessageHeader, MessageBody, OpenTimestamp, ClosedTimestamp, Status, SatisfactionRating, AssignedAgent, ResolutionTime. Primary key is likely to be TicketID. TicketID is an ID column serving as a good candidate for joins. Requester are general strings including GeoLeroy, ReneeF, LindaB, or BridgetM. IssueTypes are customer support categories with values such as Billing and Payments, Website Issues, or Product Inquiries. MessageHeader are general strings including Page Loading Issue, Membership Tier Benefits, Exchange Item, or Location Inquiry. MessageBody are general strings including 'I noticed the contact information on your website is outdated. Can you please update it or provide the correct contact details?', 'There are display issues on the website. Some elements are not visible properly. Can you please check?', or 'The website is taking too long to load pages. I’m unable to browse properly. Please look into this.'. OpenTimestamp datetimes range from 2023-11-11 00:00:29 to 2024-12-22 01:02:02. ClosedTimestamp datetimes range from 2023-11-11 00:46:54 to 2024-12-22 05:12:07. Status value is either Follow-up, Resolved, or Unresolved. SatisfactionRating values range from 1 to 5. AssignedAgents are customer service representatives' first names with values such as Jessica, David, Mike, Sarah, or John. ResolutionTime are general strings including 1 hour, 1.5 hours, 0.5 hours, or 2.0 hours."
    }
}