<script>
  import { Book_Call_URL } from '$lib/constants.js';
</script>


<div class="mx-auto prose max-w-screen-md xl:max-w-screen-lg items-center mt-20">
  <h1 class="text-4xl text-left font-bold mb-6 md:text-center">A Trustworthy AI Agent <br>to Jumpstart Self-serve Analytics</h1>

  <div class="text-center">
    <a href={Book_Call_URL} class="mx-auto" target="_blank">
      <button class="hover:underline text-gray-800 font-bold rounded-full 
        my-4 py-4 px-12 shadow-lg bg-[#4c788b] text-white
        focus:outline-none focus:shadow-outline transform transition hover:scale-105 duration-300 ease-in-out">
        Book a Demo
      </button>
    </a>
  </div>
  
  <h3 class="">Marketers Shouldn't do Data Cleaning</h3>
  <p class="mb-4">
    Marketing teams are drowning in oceans of data: whether it's coming from advertising channels, website analytics, or marketing automation tools, there's just more data to handle than ever before. Despite having direct business context and pressing questions, growth marketers are often blocked from the insights needed to run effective marketing initiatives. Instead, a big lift is spent each week manually plugging away in a spreadsheet to get answers. Even when pre-built dashboards exist, making changes such as creating new customer segments or adding new data sources relies on backlogged engineering teams that always seem to have higher priorities.
  </p>

  <h3 class="">What Does Soleda Do?</h3>
  <p class="mb-4"> 
    Soleda is building a platform of dependable and highly accurate AI agents. Our first agent (Dana) eliminates tedious and time-consuming data prep and data cleaning commonly experienced by marketing teams (and RevOps more generally). Rather than wasting time on data work that isn't part of your core job, you can instead focus on making optimal operational decisions leading to more efficient spend and happier customers.
  </p>
  <p class="mb-1">
    Our solution involves cutting-edge AI, but only leverages technology as it helps solve real world problems:
  </p>
    <ul>
    <li class="marker:text-slate-600"><span class="italic">Analytics is hard because real data is messy</span> so cleaning it is practically a full-time job. Spreadsheets are full of null values, typos, outliers, and anomalies. Column names are hard to decipher. That data someone tried to import from Salesforce just doesn't look right. Sometimes, you might not even have access to data due to permission issues. Dana tackles this head-on by carefully and intelligently navigating around these common pitfalls. In situations where the right action is unclear, the agent will ask for your feedback first. The agent pro-actively suggests fixes, but avoids automatic intervention so that you, as the human, maintain full control.</li>
    <li class="marker:text-slate-600"><span class="italic">Data doesn't come from just one place.</span> Bringing it all together is hard because not only is the data messy, individual data providers aren't built to play nicely with each other. Each vendor tracks user behavior slightly differently, defines key metrics slightly differently, and outputs their results in slightly different formats -- leading to noticeably different customer stories. There also lacks common keys that can be used to connect this information together, so figuring out how is as much a science as it is an art. With Dana's help, marketing leaders can tap into sophisticated ML algorithms to identify likely targets for joining. Dana then uses live feedback to learn on-the-fly about how to stitch it all together.</li> 
    <li class="marker:text-slate-600">The field moves fast with new trends popping up all the time.  Whether it's a new channel to reach users or shifting economic conditions, businesses have to <span class="italic">adjust quickly to meet ever-changing customer demands</span>. On the one hand, you want to keep abreast of industry best practices for measuring and achieving customer success. At the same time, your company’s modus operandi establishes a unique context to operate in. Soleda maintains flexibility since unlike traditional workflow automation or RPA, our agent is not built in a rigid hard-coded manner, but instead adapts to user preferences. Moreover, to the degree that processes are repeatable, our agent picks up those patterns to automate repetitive work without any extra effort on your part.</li>
    </ul>

  <h3 class="">What Makes Us Different</h3>
  <p class="mb-4">Rather than black-box AI voodoo, Soleda's agent is powered by cutting-edge, proprietary AI technology tailored specifically for marketing contexts. Unlike LLM-based chatbots, there is no need for magical prompt-based incantations to get the right answers. Users can talk naturally like they would to a collaborative teammate who understands both marketing priorities (multi-touch attribution, conversion optimization, etc.) as well as the complexities of data engineering. Rather than generating answers that merely sound correct, Dana's responses are always grounded to the actual data in the spreadsheet. Even better, Dana has a tangible thought process, meaning you can ask how a metric was calculated or what the reasoning was behind an action. All this results in accurate insights and actionable advice from an AI agent you can trust.
  </p>

  <div class="text-center">
    <a href="/payment" class="mx-auto">
      <button class="hover:underline text-gray-800 font-bold rounded-full 
        my-4 py-4 px-12 shadow-lg bg-[#4c788b] text-white
        focus:outline-none focus:shadow-outline transform transition hover:scale-105 duration-300 ease-in-out">
        Sign up Today
      </button>
    </a>
  </div>

</div>

<!--     With the growth in popularity of LLMs (aka. Foundation Models), a Cambrian explosion of
    start-ups that allow users to "chat to your data" have emerged in recent months. In fact, even
    many long established incumbents have integrated LLMs somewhere in their product. While the
    improvement in technology is undeniable, simply providing a wrapper around ChatGPT (or similar
    services) is insufficient due to their tendency to hallucinate and react in an over-confident
    manner. We approach the problem a little differently.
  <p class="underline">Conversations over Commands</p>
  <p class="mb-4">
    Most companies in this space have gone all-in on the trend of Generative AI. The dialogue
    research literature refers to this part of the pipeline as NLG (natural language generation)
    where the goal is to produce relevant responses. Instead, we focus on the other end of the
    spectrum: NLU (natural language understanding), where the goal is to deeply understand what the
    user wanted in the first place.
  </p>
  <p class="mb-4">
    We believe that being a great assistant is about building trust, and that the first step to
    establishing trust is listening and understanding. Rather than immediately respond to commands,
    our virtual agent instead engages with the user in multi-turn conversations to ensure answers
    are accurate and trustworthy.
  </p>
  <p class="underline">Custom Models over Off-the-Shelf APIs</p>
  <p class="mb-4">
    In order to develop a robust and reliable assistant, we train specialized models using
    proprietary datasets that have been collected specifically for the purposes of improving
    analytics capabilities. Our agent is significantly better at business intelligence because it
    has been explicitly taught to be better. Full control of the system means our agent also
    operates faster and with more openness of what is going on behind the scenes.
  </p>
  <p class="mb-4">
    To be clear, there is no free lunch here. While the agent improves in the data analytics domain,
    it loses the ability to converse about sports, history, music, or pop culture. We believe that's
    OK. You want your analyst to do their job quickly and effectively, not to browse YouTube.
  </p>
  <p class="underline">Commonsense Workflows over Complicated Algorithms</p>
  <p class="mb-4">
    Self-serve analytics should truly be self-serve. While the data team is bombarded with requests
    on a daily basis, the vast majority of these requests don't require complex statistical
    algorithms. We believe data scientists should spend their time focused on higher value
    activities instead.
  </p>
  <p class="mb-4">
    So who will answer all those requests? Leave that to us! Our assistant cheerfully and tirelessly
    responds to questions day or night, unblocking the marketing team from moving forward and
    allowing the data team to focus on what they do best. Whereas many AI-based companies assume
    folks want to run sophisticated ML models on Big Data, we instead believe in "Small Data, Big
    Insights".
  </p>
  <p class="mb-4">
    The future of useful virtual assistants is one that goes far beyond marketing analytics, and
    moves into creating slide decks, triaging emails, and much more. General advances in AI will
    certainly help, but a company must be willing to lay the foundation for specific use cases. If
    you're interested in coming along this journey, drop us a <NAME_EMAIL>!
  </p>
</div> -->
