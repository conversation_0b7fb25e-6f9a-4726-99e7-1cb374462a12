import { SvelteComponentTyped } from 'svelte';
declare const __propDef: {
  props: {
    showIndicators?: boolean | undefined;
    showCaptions?: boolean | undefined;
    showThumbs?: boolean | undefined;
    images: any[];
    slideControls?: boolean | undefined;
    loop?: boolean | undefined;
    duration?: number | undefined;
    divClass?: string | undefined;
    indicatorDivClass?: string | undefined;
    captionClass?: string | undefined;
    indicatorClass?: string | undefined;
    slideClass?: string | undefined;
  };
  events: {
    [evt: string]: CustomEvent<any>;
  };
  slots: {
    previous: {};
    next: {};
  };
};
export type CarouselProps = typeof __propDef.props;
export type CarouselEvents = typeof __propDef.events;
export type CarouselSlots = typeof __propDef.slots;
export default class Carousel extends SvelteComponentTyped<
  CarouselProps,
  CarouselEvents,
  CarouselSlots
> {}
export {};
//# sourceMappingURL=Carousel.svelte.d.ts.map
