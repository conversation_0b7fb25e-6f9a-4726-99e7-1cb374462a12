import os
import copy
import json
from collections import defaultdict, Counter
from tqdm import tqdm as progress_bar

from backend.components.world import World
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import seaborn as sns
import matplotlib.pyplot as plt

from backend.agent import Agent
from backend.prompts.for_experts import intent_prompt
from backend.components.engineer import PromptEngineer
from utils.help import *
from utils.arguments import solicit_params
from utils.model_tests.nlu_dacts import test_set
from utils.model_tests.nlu_beta_dacts import beta_test_set


def run_cli_interface(args, data_analyst):
    action = None
    keep_going = True
    while keep_going:
        user_actions = []
        # user_utt = "What headers are available in products?"
        # print(f"User: {user_utt}")
        user_utt = input("User: ")
        if user_utt.lower() in END_PHRASES:
            keep_going = False
            output_msg = "Goodbye!"
        else:
            output = data_analyst.converse(user_utt, user_actions)
            output_msg = output["message"]
        print(f"Agent: {output_msg}")

        if len(output["actions"]) > 0:
            print(f"Actions: {output['actions']}")
        if args.verbose:
            print("  ---------- New Turn -----------")


def run_test_suite(args):
    data = beta_test_set if args.run_beta else test_set
    test_results = []
    total_turn_count = 0
    turn_passed = 0

    for sample_id, sample in progress_bar(enumerate(data), total=len(data)):
        convo_result = {
            "convo_id": sample["convo_id"],
            "turns": [],
            "status": "CONV_SUCCEED",  # Will be set to FAILED if any turn fails
        }

        data_analyst = Agent(2, args)

        for i, turn in enumerate(sample["turns"]):
            if turn["speaker"] != "User":
                data_analyst.context.add_turn(
                    turn["speaker"], turn["text"], "utterance"
                )
                continue

            total_turn_count += 1
            utt_id = turn["utt_id"]
            data_analyst.context.add_turn(turn["speaker"], turn["text"], "utterance")

            # Ensure intent and dact are set
            if not turn["intent"]:
                turn["intent"] = dax2intent(turn["dax"])
            if not turn["dact"]:
                turn["dact"] = dax2dact(turn["dax"])

            gold_dax = turn["dax"]
            gold_intent = turn["intent"]

            # Get prediction based on test expert type
            if args.test_expert == "embed":
                pred_intent, pred_dax, pred_score = data_analyst.nlu.embed(
                    data_analyst.context
                )
            elif args.test_expert == "logreg":
                pred_intent, pred_dax, pred_score = data_analyst.nlu.logreg(
                    data_analyst.context
                )
            elif args.test_expert == "peft":
                pred_intent, pred_dax, pred_score = data_analyst.nlu.peft(
                    data_analyst.context
                )
            elif args.test_expert == "icl":
                # For ICL, we need to have default table
                # TODO test untils for more comprehensive world/table/context/state mocking
                data_analyst.world = World(args)
                data_analyst.world.default_table = 'test_table'

                current_turn_idx = i
                previous_intent = ''
                if current_turn_idx > 1: 
                    previous_intent = sample["turns"][current_turn_idx-2]["intent"] 
                pred_intent, pred_dax, pred_score = data_analyst.nlu.icl(
                    data_analyst.context, '', previous_intent
                )

            # Record turn result
            turn_result = {
                "utt_id": utt_id,
                "status": "SUCCEED" if pred_dax == gold_dax else "FAILED",
                "dax": {
                    "expected_dax": gold_dax,
                    "actual_dax": pred_dax,
                },
                "intent": {"expected": gold_intent, "actual": pred_intent},
                "confidence_score": pred_score,
                "text": turn["text"],
            }

            convo_result["turns"].append(turn_result)

            if pred_dax == gold_dax:
                turn_passed += 1
            else:
                convo_result["status"] = "CONV_FAILED"
                if args.verbose:
                    print(f"Failed test {sample['convo_id']} at turn {utt_id}")
                    print(f"Expected DAX: {gold_dax}, Got: {pred_dax}")
                    print(f"Text: {turn['text']}\n")

        test_results.append(convo_result)

    # Generate summary statistics
    summary = {
        "overall_statistics": {
            "total_conversations": len(data),
            "passed_conversations": sum(
                1 for r in test_results if r["status"] == "CONV_SUCCEED"
            ),
            "total_turns": total_turn_count,
            "passed_turns": turn_passed,
            "accuracy": {
                "per_conversation": f"{(sum(1 for r in test_results if r['status'] == 'CONV_SUCCEED') / len(data)):.2%}",
                "per_turn": f"{(turn_passed / total_turn_count):.2%}",
            },
        },
        "test_results": test_results,
    }

    output_dir = os.path.join("utils", "model_tests")
    output_path = os.path.join(output_dir, f"test_results_{args.test_expert}.json")
    with open(output_path, "w") as f:
        json.dump(summary, f, indent=2)

    # Print summary
    print("\nTest Suite Summary:")
    print(
        f"Conversations: {summary['overall_statistics']['passed_conversations']}/{len(data)} passed"
    )
    print(f"Turns: {turn_passed}/{total_turn_count} passed")

    return summary


def calculate_confusion_matrix(actual_dax, pred_dax, labels_sequence):
    cm = confusion_matrix(actual_dax, pred_dax, labels=labels_sequence)
    summary = {}
    for idx, value in enumerate(cm):
        if int(sum(value)) != 0 and int(value[idx]) / int(sum(value)) < 0.5:
            err_count = int(sum(value)) - int(value[idx])
            err_rate = 1 - value[idx] / sum(value)
            err = {
                labels_sequence[i]: round(v / err_count * err_rate, 2)
                for i, v in enumerate(value)
                if v > 0 and i != idx
            }
            err = dict(sorted(err.items(), key=lambda x: x[1], reverse=True))
            summary[labels_sequence[idx]] = {"err": err, "err_rate": err_rate}
    summary = dict(
        sorted(
            summary.items(), key=lambda x: list(x[1]["err"].values())[0], reverse=True
        )
    )
    print(json.dumps(summary))

    plt.figure(figsize=(4, 4))
    sns.heatmap(
        cm,
        annot=True,
        fmt=".0f",
        cmap="Blues",
        cbar=False,
        xticklabels=labels_sequence,
        yticklabels=labels_sequence,
        annot_kws={"size": 12},
    )
    plt.xticks(rotation=60)

    plt.title("Confusion Matrix (Percentages)")
    plt.show()

    return summary


def run_err_analysis(args):
    if args.test_expert == "all":
        actual_dax_all, pred_dax_all = [], []
        test_experts = ["embed", "logreg", "peft", "icl"]
        for expert in test_experts:
            if os.path.exists(f"actual_dax_{expert}.json"):
                actual_dax_all.extend(json.load(open(f"actual_dax_{expert}.json", "r")))
                pred_dax_all.extend(json.load(open(f"pred_dax_{expert}.json", "r")))
            else:
                args.test_expert = expert
                actual_dax, pred_dax = run_test_suite(args)
                actual_dax_all.extend(actual_dax)
                pred_dax_all.extend(pred_dax)
    else:
        if os.path.exists(f"actual_dax_{args.test_expert}.json"):
            actual_dax_all = json.load(open(f"actual_dax_{args.test_expert}.json", "r"))
            pred_dax_all = json.load(open(f"pred_dax_{args.test_expert}.json", "r"))
        else:
            actual_dax_all, pred_dax_all = run_test_suite(args)
    labels_sequence = list(set(actual_dax_all + pred_dax_all))
    summary = calculate_confusion_matrix(actual_dax_all, pred_dax_all, labels_sequence)
    return summary


def construct_aeo_representation(aeo_context, icl_ent_states, icl_ops_states):
    aeo_representation = ""
    user_counter = 0
    for idx in range(len(aeo_context)):
        aeo_representation += aeo_context[idx] + "\n"
        if aeo_context[idx].startswith("User"):
            aeo_representation += f"* Target: {icl_ent_states[user_counter]}\n"
            if user_counter < len(icl_ops_states):
                aeo_representation += f"* Operations: {icl_ops_states[user_counter]}\n"
            user_counter += 1
    return aeo_representation


def run_auto_label(args):
    data = json.load(open(args.input_file, "r"))
    output_filename = args.input_file.split("/")[-1].split(".")[0] + "_label.json"
    label_data = []
    for sample_id, sample in progress_bar(enumerate(data), total=len(data)):
        convo_id = sample["convo_id"]
        label_sample = copy.deepcopy(sample)
        data_analyst = Agent(-1, args)
        icl_res_states, icl_ops_states = [], []
        for turn_id, turn in enumerate(sample["turns"]):
            utt_id = turn["utt_id"]
            label_turn = label_sample["turns"][turn_id]
            data_analyst.context.add_turn(turn["speaker"], turn["text"], "utterance")
            if turn["speaker"] == "User":
                intent, dax, score = data_analyst.nlu.predict_intent_dact(
                    data_analyst.context
                )

                peft_ent, peft_ops, _ = data_analyst.nlu.peft(
                    data_analyst.context, "aeo", intent, dax2dact(dax)
                )
                peft_ent = "" if peft_ent[0] == ["abstain"] else peft_ent
                peft_ops = [
                    operation for operation in peft_ops if operation != "abstain"
                ]
                print(
                    f"{convo_id}_{utt_id}) Entities: {peft_ent}, Operations: {peft_ops}"
                )

                icl_res = data_analyst.nlu.icl.predict_operations(
                    data_analyst.context, valid_col_rep="", pred_dax=dax
                )
                icl_res_states.append(icl_res)
                pred_flow = data_analyst.nlu.dax2flow.get(dax, None)
                if pred_flow is not None:
                    aeo_context = data_analyst.context.aeo_history(False)
                    aeo_representation = construct_aeo_representation(
                        aeo_context, icl_res_states, icl_ops_states
                    )
                    icl_ops = data_analyst.nlu.icl.predict_ops(
                        aeo_representation=aeo_representation,
                        pred_flow=pred_flow,
                        ops_def=data_analyst.nlu.ops_def,
                    )
                else:
                    icl_ops = []
                icl_ops_states.append(icl_ops)
                print(
                    f"{convo_id}_{utt_id}) ICL Entities: {icl_res}, Operations: {icl_ops}"
                )

                label_turn["intent"] = intent
                label_turn["dax"] = dax
                label_turn["dact"] = dax2dact(dax)

                label_turn["entities"] = peft_ent
                if peft_res != icl_res:
                    label_turn["icl_res"] = icl_res
                label_turn["operations"] = peft_ops
                if peft_ops != icl_ops:
                    label_turn["icl_ops"] = icl_ops
            else:
                for key in turn.keys():
                    if key not in ["speaker", "text"]:
                        label_turn.pop(key)

        label_data.append(label_sample)
        # in case some error happens during the labeling process
        json.dump(
            label_data,
            open(os.path.join(args.output_dir, output_filename), "w"),
            indent=4,
        )
    json.dump(
        label_data, open(os.path.join(args.output_dir, output_filename), "w"), indent=4
    )


def run_user_simulator(args, data_analyst):
    user_simulator = User(args)

    agent_output = {}
    collected_samples = []
    for _ in range(args.timesteps):
        user_output = user_simulator.step(agent_output)
        if user_utt == "<end>":
            break
        else:
            agent_output = data_analyst.step(*user_output)
        collected_samples.append((user_output, agent_output))
    run_training(args, collected_samples, data_analyst)


if __name__ == "__main__":
    args = solicit_params()
    args = setup_gpus(args)
    set_seed(args)

    data_analyst = Agent(-1, args)
    if args.do_nlu:
        run_test_suite(args)
    elif args.quantify:
        summary = run_err_analysis(args)
    elif args.do_label:
        run_auto_label(args)
    else:
        run_cli_interface(args, data_analyst)

"""
# --------- Run the Webserver ----------
cd backend
uvicorn webserver:app --reload
cd ../frontend
npm run dev -- --port 1414

# ---------- Utility commands ----------
# Unit tests
# python3 main.py --verbose --debug --do-qa --temperature 0 --api-version gpt-4
# Inserting data into Postgres
# python3 main.py --verbose --checkpoint query
"""
