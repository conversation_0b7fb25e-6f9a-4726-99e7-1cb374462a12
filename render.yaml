services:
  - type: web
    name: production-backend
    runtime: python
    buildCommand: "pip install -r requirements.txt"
    # HACK: Shouldn't need to specify download_corpora here
    startCommand: "python -m textblob.download_corpora && uvicorn backend.webserver:app --host 0.0.0.0 --port $PORT"
    healthCheckPath: /api/v1/health
    plan: standard # Need at least 1.5GB of RAM to run the server
    disk:
      # TODO: This shouldn't be necessary! should ideally load from s3 or the like
      name: model-weights
      mountPath: /data/disk
      sizeGB: 2
    websocket: true
    envVars:
      - key: PYTHON_VERSION
        value: "3.11.5"
      - key: LOGREG_MODEL
        value: "v10_acc900_lr1e-04_ont3.5.2.pt"
      - key: SOLEDA_ENV
        value: production
      - key: DB_DSN
        fromDatabase:
          name: soleda-db
          property: connectionString
      - key: PORT
        value: 8000

  - type: web
    name: production-frontend-app
    runtime: node
    rootDir: frontend
    buildCommand: "npm install && npm run build"
    startCommand: "node server.js"
    envVars:
      - key: VITE_SERVER_HOST
        fromService:
          name: production-backend
          type: web
          property: hostport
      - key: PORT
        value: 3000

databases:
  - name: soleda-db
    postgresMajorVersion: 15
    plan: starter
