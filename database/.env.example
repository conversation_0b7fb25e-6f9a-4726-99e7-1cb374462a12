STAGING_DB_USER=
STAGING_DB_PASSWORD=
STAGING_DB_HOST=
STAGING_DB_NAME=
STAGING_DB_DSN="postgresql://${STAGING_DB_USER}:${STAGING_DB_PASSWORD}@${STAGING_DB_HOST}/${STAGING_DB_NAME}"

PRODUCTION_DB_USER=
PRODUCTION_DB_PASSWORD=
PRODUCTION_DB_HOST=
PRODUCTION_DB_NAME=
PRODUCTION_DB_DSN="postgresql://${PRODUCTION_DB_USER}:${PRODUCTION_DB_PASSWORD}@${PRODUCTION_DB_HOST}/${PRODUCTION_DB_NAME}"

JWT_SECRET=

PRED_SERVER_URL=
EVAL_URL=
RETRIEVER_URL=
TRAINING_URL=
