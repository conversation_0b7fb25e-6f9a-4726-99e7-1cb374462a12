import sys
import json
import time
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from collections import defaultdict
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.webserver import app
from backend.db import get_db, load_engine
from backend.auth.credentials import get_auth_user_email
from utils.dependencies import get_test_db, load_test_engine

TEST_TOKEN = "secret_acromi_token_for_testing_123"

def get_test_user_email():
  return "<EMAIL>"

@pytest.fixture(scope="module")
def test_client():
  app.dependency_overrides[get_db] = get_test_db
  app.dependency_overrides[load_engine] = load_test_engine
  app.dependency_overrides[get_auth_user_email] = get_test_user_email
  
  client = TestClient(app)
  yield client
  
  app.dependency_overrides.clear()

@pytest.fixture(scope="module")
def websocket(test_client: TestClient):
  with test_client.websocket_connect(f"/api/v1/ws?token={TEST_TOKEN}") as ws:
    yield ws

def test_select_sheet(test_client):
  spreadsheet_data = {
    "ssName": "Shoe Store Sales",
    "tabNames": ["orders", "customers", "products"],
  }
  response = test_client.post("/api/v1/sheets/select", json=spreadsheet_data)
  assert response.status_code == 200, f"Failed to select spreadsheet: {response.text}"

def decide_which_parts(parts):
  # determine which parts of the message to keep, returns 3 boolean values
  store_query = 'query' in parts
  store_code = 'code' in parts
  store_actions = 'actions' in parts
  return store_query, store_code, store_actions

def decide_part_availability(parsed):
  # determine which parts are available in the message, returns 3 boolean values
  has_query, has_code, has_actions = False, False, False
  
  if 'interaction' in parsed.keys():
    if parsed['interaction'] and 'content' in parsed['interaction']:
      if 'SQL Query' in parsed['interaction']['content']:
        has_query = True
      if 'Pandas Code' in parsed['interaction']['content']:
        has_code = True
  if 'actions' in parsed.keys():
    has_actions = True
  return has_query, has_code, has_actions

def send_message(websocket, message:str, gold_dax:str, parts:list=[]):
  websocket.send_json( { 'currentMessage': message, 'dialogueAct': gold_dax, 'lastAction': None}, mode='binary')
  store_query, store_code, store_actions = decide_which_parts(parts)
  results = defaultdict(str)

  while True:
    try:
      raw_output = websocket.receive_json()
      
      has_query, has_code, has_actions = decide_part_availability(raw_output)
      if store_query and has_query:
        results['query'] = raw_output['interaction']['content'][19:]
      if store_code and has_code:
        results['code'] = raw_output['interaction']['content'][21:]
      if store_actions and has_actions:
        results['actions'] = raw_output['actions']
      if 'message' in raw_output.keys():
        results['message'] = raw_output['message']
        return results

    except json.JSONDecodeError as e:
      return f"Failed to decode JSON: {e}"

  return 'Error'

@pytest.mark.timeout(30)
def test_remove_duplicate_user(websocket):
  turn_one = send_message(websocket, "Can you help me find duplicates in the customers table?", '7BD', ['actions'])
  print("Turn 1:", turn_one)
  assert 'INTERACT' in turn_one['actions'], 'Missing interaction to remove_duplicates'

@pytest.mark.timeout(30)
def test_column_picking(websocket, test_client):
  payload = {
    'flowType': 'Clean(dedupe)',
    'selected': [
      {'tab': 'customers', 'col': 'first', 'row': -1, 'ver': True},
      {'tab': 'customers', 'col': 'last', 'row': -1, 'ver': True},
      {'tab': 'customers', 'col': 'state', 'row': -1, 'ver': True}
    ],
    'stage': 'pick-tab-col'
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_two = response.json()
  print("Turn 2:", turn_two)
  assert 'determine which row to keep' in turn_two['message'], 'Failed to pick the columns'

@pytest.mark.timeout(60)
def test_merge_style(websocket, test_client):
  payload = {
    'flowType': 'Clean(dedupe)',
    'selected': [
      {'tab': 'customers', 'col': 'first', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'last', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'state', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'stage': 'merge-style',
    'style': {'name': 'question', 'setting': 'not_sure'}
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_three = response.json()
  print("Turn 3:", turn_three)
  assert 'duplicate rows' in turn_three['message'], 'Failed to select a merge style'

@pytest.mark.timeout(30)
def test_separate_cards(websocket, test_client):
  payload = {
    'flowType': 'Clean(dedupe)',
    'selected': [
      {'tab': 'customers', 'col': 'first', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'last', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'state', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'stage': 'combine-cards',
    'resolution': 'separate',
    'chosen': { 'retain': [89, 324], 'retire': [] }
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_four = response.json()
  print("Turn 4:", turn_four)
  assert 'CLEAN' in turn_four['actions'], 'Failed to separate the duplicate rows'

@pytest.mark.timeout(30)
def test_merge_cards(websocket, test_client):
  payload = {
    'flowType': 'Clean(dedupe)',
    'selected': [
      {'tab': 'customers', 'col': 'first', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'last', 'row': -1, 'ver': True, 'rel': ''},
      {'tab': 'customers', 'col': 'state', 'row': -1, 'ver': True, 'rel': ''}
    ],
    'stage': 'combine-cards',
    'resolution': 'merge',
    'chosen': { 'retain': [110], 'retire': [6] }
  }
  response = test_client.post('/api/v1/interactions/merge', json=payload)
  turn_five = response.json()
  print("Turn 5:", turn_five)
  assert 'successfully' in turn_five['message'], 'Failed to merge the duplicate rows'

