identify_issues_prompt = """Given the set of valid columns and the user history, decide which table and columns (if any) are being referenced.
Start by constructing a concise thought concerning what issue the user is trying to resolve. Possible issues include:
  * problems - mixed data types and non-standard formatting which will break a SQL query, such as mixing MM/YY with MM/DD/YYYY
  * concerns - textual anomalies and numeric outliers, these won't break a query, but may need to be addressed
  * typos - misspellings and inconsistent naming conventions, possibly caused by similar terms or abbreviations
  * blanks - empty cells or null values that need to be handled, especially in calculations

Next, choose what table might be relevant, and then what columns are being referenced from that table.
If no specific columns are mentioned, but data __is__ being requested from a table, return '*' to represent all columns.
Note the current table is '{curr_tab}'. When multiple tables are appropriate, just return the {curr_tab} table as your response.
Conversely, if no tables seem appropriate, using the current table is once again a safe default.
Most importantly, only choose from the set of valid columns. This is very important to my career, pay attention to casing and please do NOT return any invalid columns.

Your entire response should be in well-formatted JSON including keys for thought (string), table (string) and columns (list), with no further explanations after the JSON output.
We will go through three sample spreadsheets with a couple of examples each, and then tackle the final real scenario.

## Mobile-First Car Rental Scenario
For our first sample spreadsheet, suppose the valid options are:
* Tables: Bookings; Promotions; CustomerSupport
* Columns: BookingID, CustomerID, VehicleID, StartDate, EndDate, PickupLocation, DropoffLocation, BookingStatus, TotalAmount, PaymentStatus in Bookings;
PromotionID, DiscountAmount, ApplicableVehicleTypes, TermsConditions, RedemptionCount in Promotions;
TicketID, CustomerID, IssueDate, IssueType, IssueDescription, AssignedAgent, ResolutionStatus, ResolutionDate, Feedback, FollowUpRequired in CustomerSupport

Agent: I found 3 outliers in the DiscountAmount column. Would you like to investigate?
Agent: The average discount amount was $34. See table for more details.
User: Sure, what are the values?

_Output_
```json
{{
  "thought": "The user is concerned about outliers in the DiscountAmount column in the Promotions table.",
  "table": "Promotions",
  "columns": ["DiscountAmount"]
}}
```

User: Are there any typos to watch out for in our bookings data?

_Output_
```json
{{
  "thought": "The user wants to check for typos in the bookings data.",
  "table": "Bookings",
  "columns": ["*"]
}}
```

Agent: I found general text and dates mixed together in the StartDate and EndDate columns. Would you like to investigate?
Agent: The average booking period lasted 3.6 days in November.
User: Hmm, what do you mean by mixed?

_Output_
```json
{{
  "thought": "There are problems in the StartDate and EndDate columns in the Bookings table.",
  "table": "Bookings",
  "columns": ["StartDate", "EndDate"]
}}
```

User: I found a bunch of problems in the promtions table.

_Output_
```json
{{
  "thought": "The user is concerned about problems in the Promotions table, but the specific problems are unclear.",
  "table": "Promotions",
  "columns": ["*"]
}}
```

Agent: I found 2 anomalies and 3 location issues in the PickupLocation column. Would you like to investigate?
Agent: The most popular pickup locations were Toronto, Montreal, and Vancouver.
User: What are the anomalies?

_Output_
```json
{{
  "thought": "The user is concerned about textual anomalies and location issues in the PickupLocation column in the Bookings table.",
  "table": "Bookings",
  "columns": ["PickupLocation"]
}}
```

## Luxury Online Retailer Scenario
For our second sample spreadsheet, suppose the valid options are:
* Tables: customers; campaigns; inventory; promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, DateRegistered, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand in customers;
CampaignID, CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in campaigns;
ItemID, BrandName, Category, Price, StockQuantity, DateAdded, Supplier in inventory;
PromoID, PromoName, StartTime, EndTime, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in promotions

User: What are the total number of purchases made by each customer?
Agent: I found some null rows in the customer name field which prevents me from proceeding. Should we ignore those users?
User: Actually, I'd like to take a look.

_Output_
```json
{{
  "thought": "The user is concerned about blanks in the FirstName and LastName columns in the customers table.",
  "table": "customers",
  "columns": ["FirstName", "LastName"]
}}
```

User: Are there any misspelled brand names?
Agent: I found 5 groups of similar terms: [Versache, Versace, Versaci], [Givenchy, Givenchi, Givenche, Givency], [Dolce and Gabbanna, Dolce & Gabbana, Dolce & Gabanna]. See table for more.
User: Yes, let's merge those into one term each.

_Output_
```json
{{
  "thought": "The user wants to resolve typos in the BrandName column in the inventory table.",
  "table": "inventory",
  "columns": ["BrandName"]
}}
```

User: Please change them into the standard format.
Agent: Ok, I have converted them all into MM/DD/YYYY
User: Do we have any issues with dates on any of the other tables?

_Output_
```json
{{
  "thought": "The user is concerned about the date formatting, but did not specify a table. I will pick a table that clearly has date columns.",
  "table": "inventory",
  "columns": ["*"]
}}
```

User: Can I get the phone numbers and emails for all customers who made a purchase in the last month?
Agent: I found some whole numbers and phone numbers mixed together in the phone number field. Would you like to take a look?
User: Sure, I guess

_Output_
```json
{{
  "thought": "The user wants to check for problems about mixed data types in the PhoneNumber column in the customers table.",
  "table": "customers",
  "columns": ["PhoneNumber"]
}}
```

## SaaS Vendors Scenario
For our third sample spreadsheet, suppose the valid options are:
* Tables: Mailchimp; Hubspot; Salesforce; Zendesk
* Columns: subscriber_id, first_name, last_name, email, campaign_id, campaign_name, campaign_launch_date, opened, clicked, unsubscribed, list_segment, clicked_link in Mailchimp;
LeadID, UserName, Source, VisitCounts, PageVisited, FirstVisitTime, DownloadedContent, FormSubmitted, FormSubmissionDateTime, LeadScore in Hubspot;
ContactID, FullName, DateTimeJoined, EmailAddress, OpportunityID, Stage, DealSize, LastContactDate, NextStep, DecisionMaker, Location (city), Location (state), Location (country) in Salesforce;
ticket_id, requester, issue_type, message_header, message_body, open_timestamp, closed_timestamp, status, satisfaction_rating, assigned_agent, resolution_time in Zendesk

Agent: I found 4 missing rows and 3 null values in the 'LastContactDate' field. Would you like to investigate?
Agent: There are 10 opportunities in progress and 5 completed in June.
User: Show me the ones without a last contact date.

_Output_
```json
{{
  "thought": "The user is concerned about blanks in the LastContactDate column in the Salesforce table.",
  "table": "Salesforce",
  "columns": ["LastContactDate"]
}}
```

User: Who are the 5 agents with the highest satisfaction ratings?
Agent: The top agents are William, Angela-ON, Lisa, Jason-ON, and Angela.
User: I think the agent names need to be cleaned up, so for example Angela and Angela-ON are actually the same person.

_Output_
```json
{{
  "thought": "The user wants to resolve typos in the assigned_agent column in the Zendesk table.",
  "table": "Zendesk",
  "columns": ["assigned_agent"]
}}
```

User: I think the states should be standardized to their two-letter abbreviations.

_Output_
```json
{{
  "thought": "The user wants to resolve concerns about textual anomalies in the Location (state) column in the Salesforce table.",
  "table": "Salesforce",
  "columns": ["Location (state)"]
}}
```

User: Let's dive into the latest marketing data
Agent: Sure, what would you like to know?
User: Do we have any missing values to watch out for?

_Output_
```json
{{
  "thought": "The user is concerned about blanks, but did not specify a table. I will pick a table that most likely has missing values.",
  "table": "Hubspot",
  "columns": ["*"]
}}
```

User: Which users opened the most emails in the last week?
Agent: I am hitting an error since it seems some of the values in the email column are unsupported. We can take a look or just ignore it.
User: Yea, let's take a look.

_Output_
```json
{{
  "thought": "The user wants to check for problems in the email column in the Mailchimp table.",
  "table": "Mailchimp",
  "columns": ["email"]
}}
```

## Real Scenario
For our current case, recall that the valid set of tables and columns are:
{columns}

{history}

_Output_
"""

connect_flow_prompt = """Following the conversation history, the user has made an open-ended request to connect two pieces of information together.
To complete this task, we need to first ground the request to a specific location. Note that the user is currently on the '{current}' table, which serves as the default location.
Given the valid tables and columns, start by constructing a concise thought concerning what two data sources the user wants to connect.
Then, choosing only from valid options, output the most likely table, column, or API being referenced.

If the target table or column is unclear or ambiguous, then output 'unsure' rather than making a guess. When all of something is being considered, you can use '*' to represent the entire set.
Your entire response should be in well-formatted JSON, including keys for thought (string), table (string), columns (list), and API (string).
There should be no further explanations or text after the JSON output.

For example,
---
Suppose the tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
* Columns: CourseID, CourseTitle, InstructorID, CourseDescription, StartDate, EndDate, Duration, CourseFormat, Category, EnrollmentCount in BB_courses;
EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in BB_enrollments;
TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;

_Conversation History_
User: What are the top 5 courses with the highest enrollment?
Agent: The top 5 courses with the highest enrollment are: Python Flask, Python Django, JavaScript React, Java Spring, and Ruby on Rails.
User: Can you show me the testimonials for those courses?

_Output_
```json
{{
  "thought": "The user wants to connect the top 5 courses with the highest enrollment to their testimonials.",
  "table": "Testimonials",
  "columns": ["CourseID", "TestimonialText"]
}}
```
---
## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

resolve_flow_prompt = """Following the conversation history, the user has made an open-ended request to fix issues within a spreadsheet.
To complete this task, we need to first ground the request to a specific location. Note that the user is currently on the '{current}' table, which serves as the default location.
Given the valid tables and columns, start by constructing a concise thought concerning what area the user wants to fix.
Then, choosing only from valid options, output the most likely table, row, and column(s) being referenced.

If the target table or column is unclear or ambiguous, then output 'unsure' rather than making a guess. When all of something is being considered, you can use '*' to represent the entire set.
Referencing the entire column is quite common, which means putting '*' for the rows. Otherwise, write simple pandas code to filter the rows withing a dataframe called 'main_df'.
Your entire response should be in well-formatted JSON, including keys for thought (string), table (string), columns (list), and rows (code snippet).
There should be no further explanations or text after the JSON output.

For example,
#############
Suppose the tables and columns are:
* Tables: CustomerContact, CustomerOrders, MarketingOffers
* Columns: CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustomerContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustomerOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Conversation History_
User: How many deliveries are still in transit?
Agent: I found 53 orders with the 'In Transit' status. Would you like to investigate?
User: That doesn't seem right, are there any issues with the order statuses?

_Output_
```json
{{
  "thought": "The user is concerned with the OrderStatus column in CustomerOrders table.",
  "table": "CustomerOrders",
  "columns": ["OrderStatus"],
  "rows": "*"
}}
```

_Conversation History_
User: Sure, we can get rid of any that have a missing title.
Agent: Ok, I have removed 67 rows with missing titles from MarketingOffers.
User: Let's move onto the fixing the dates next.

_Output_
```json
{{
  "thought": "There are multiple date columns in the MarketingOffers table.",
  "table": "MarketingOffers",
  "columns": ["StartDate", "EndDate"],
  "rows": "*"
}}
```
#############
For this next scenario, suppose we have:
* Tables: mq_leads, data_sources, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in mq_leads;
launch_id, is_secure, provenance, version, features, documentation_link in data_sources;
subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity

_Conversation History_
User: Let's just drop the three columns at the end related to upsells.
Agent: Sure, I have removed the columns. How does this look?
User: Are there any problems with subscriptions that have a late renewal notice?

_Output_
```json
{{
  "thought": "The user is focused on rows associated with a late renewal notice in the subscriptions table.",
  "table": "subscriptions",
  "columns": ["*"],
  "rows": "main_df['renewal_notice'] == 'late'"
}}
```

_Conversation History_
User: And what about user activity?
Agent: There are 9815 rows and 8 columns in the user_activity table. What would you like to do next?
User: There's something wrong with this column.

_Output_
```json
{{
  "thought": "The user is concerned about a specific column in the user_activity table, but did not specify the name.",
  "table": "user_activity",
  "columns": ["unsure"],
  "rows": "*"
}}
```
#############
As a final scenario, suppose the tables and columns are:
* Tables: Customers, Campaigns, Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, PurchaseHistory, PurchaseAmount, PointAccumulated, Address, Registered in Customers;
CampaignID, CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Campaigns;
PromoID, PromoName, LaunchDate, PromoChannel, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in Promotions

_Conversation History_
User: So what sorts of things can you do?
Agent: I am able to help with data transformations, fixing data issues, and generating insights.
User: Sure, what can we fix in the data?

_Output_
```json
{{
  "thought": "There is a general request to fix issues in the data.",
  "table": "*",
  "columns": ["*"],
  "rows": "*"
}}
```

_Conversation History_
User: How many different campaigns are we running?
Agent: Based on the CampaignID, we have 20 unique campaigns without an end date. Does that sound right?
User: Yea, but the table looks messy, we definitely need to clean it up first.

_Output_
```json
{{
  "thought": "The 'table' in the final utterance refers back to the Campaigns table mentioned earlier.",
  "table": "Campaigns",
  "columns": ["*"],
  "rows": "*"
}}
```

_Conversation History_
User: We should rename it to PurchaseHistory to be consistent.
Agent: Sure, I have renamed the column to PurchaseHistory. What's next?
User: Let's try to deal with any column related to contact information.

_Output_
```json
{{
  "thought": "Based on PurchaseHistory, I know we are working with the Customers table. Contact information for customers includes Email, PhoneNumber, and Address.",
  "table": "Customers",
  "columns": ["Email", "PhoneNumber", "Address"],
  "rows": "*"
}}
```
#############
Now it's your turn! As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

swebench_prompt = """You will be tasked to fix an issue from an open-source repository.

Your thinking should be thorough and so it's fine if it's very long. You can think step by step before and after each action you decide to take.

You MUST iterate and keep going until the problem is solved.

You already have everything you need to solve this problem in the /testbed folder, even without internet connection. I want you to fully solve this autonomously before coming back to me.

Only terminate your turn when you are sure that the problem is solved. Go through the problem step by step, and make sure to verify that your changes are correct. NEVER end your turn without having solved the problem, and when you say you are going to make a tool call, make sure you ACTUALLY make the tool call, instead of ending your turn.

THE PROBLEM CAN DEFINITELY BE SOLVED WITHOUT THE INTERNET.

Take your time and think through every step - remember to check your solution rigorously and watch out for boundary cases, especially with the changes you made. Your solution must be perfect. If not, continue working on it. At the end, you must test your code rigorously using the tools provided, and do it many times, to catch all edge cases. If it is not robust, iterate more and make it perfect. Failing to test your code sufficiently rigorously is the NUMBER ONE failure mode on these types of tasks; make sure you handle all edge cases, and run existing tests if they are provided.

You MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.

# Workflow

## High-Level Problem Solving Strategy

1. Understand the problem deeply. Carefully read the issue and think critically about what is required.
2. Investigate the codebase. Explore relevant files, search for key functions, and gather context.
3. Develop a clear, step-by-step plan. Break down the fix into manageable, incremental steps.
4. Implement the fix incrementally. Make small, testable code changes.
5. Debug as needed. Use debugging techniques to isolate and resolve issues.
6. Test frequently. Run tests after each change to verify correctness.
7. Iterate until the root cause is fixed and all tests pass.
8. Reflect and validate comprehensively. After tests pass, think about the original intent, write additional tests to ensure correctness, and remember there are hidden tests that must also pass before the solution is truly complete.

Refer to the detailed sections below for more information on each step.

## 1. Deeply Understand the Problem
Carefully read the issue and think hard about a plan to solve it before coding.

## 2. Codebase Investigation
- Explore relevant files and directories.
- Search for key functions, classes, or variables related to the issue.
- Read and understand relevant code snippets.
- Identify the root cause of the problem.
- Validate and update your understanding continuously as you gather more context.

## 3. Develop a Detailed Plan
- Outline a specific, simple, and verifiable sequence of steps to fix the problem.
- Break down the fix into small, incremental changes.

## 4. Making Code Changes
- Before editing, always read the relevant file contents or section to ensure complete context.
- If a patch is not applied correctly, attempt to reapply it.
- Make small, testable, incremental changes that logically follow from your investigation and plan.

## 5. Debugging
- Make code changes only if you have high confidence they can solve the problem
- When debugging, try to determine the root cause rather than addressing symptoms
- Debug for as long as needed to identify the root cause and identify a fix
- Use print statements, logs, or temporary code to inspect program state, including descriptive statements or error messages to understand what's happening
- To test hypotheses, you can also add test statements or functions
- Revisit your assumptions if unexpected behavior occurs.

## 6. Testing
- Run tests frequently using `!python3 run_tests.py` (or equivalent).
- After each change, verify correctness by running relevant tests.
- If tests fail, analyze failures and revise your patch.
- Write additional tests if needed to capture important behaviors or edge cases.
- Ensure all tests pass before finalizing.

## 7. Final Verification
- Confirm the root cause is fixed.
- Review your solution for logic correctness and robustness.
- Iterate until you are extremely confident the fix is complete and all tests pass.

## 8. Final Reflection and Additional Testing
- Reflect carefully on the original intent of the user and the problem statement.
- Think about potential edge cases or scenarios that may not be covered by existing tests.
- Write additional tests that would need to pass to fully validate the correctness of your solution.
- Run these new tests and ensure they all pass.
- Be aware that there are additional hidden tests that must also pass for the solution to be successful.
- Do not assume the task is complete just because the visible tests pass; continue refining until you are confident the fix is robust and comprehensive.
"""

insight_flow_prompt = """As seen in the conversation history, the user has made an open-ended request to find insights or observations in the data.
To complete this task, we need to first ground the request to a specific location.
Given the valid tables and columns, start by constructing a concise thought concerning what area the user is interested in exploring.
Then, choosing only from valid options, output the most likely table and column(s) being referenced.
Note that the user is currently on the '{current}' table, so any references to 'this table' or 'the table' should be understood with this in mind.

When all or any of something is being considered, you can use '*' to represent the entire set. This includes the scenario when the user is asking to analyze all the data in a table.
Users occasionally make unclear requests where the target table or column is ambiguous. Such scenarios are characterized by vague statements unrelated to data analysis.
Instead, the user is asking general information regarding the agent or service. In such cases, output 'unsure' rather than guessing an inaccurate target.
Your entire response should be in well-formatted JSON including keys for thought (string), table (string) and columns (list), with no further explanations or text after the JSON output.

For example,
---
Suppose the tables and columns are:
* Tables: members, classes, packages
* Columns: member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact in members;
class_id, class_name, instructor_id, class_date, start_time, end_time, room_number, class_capacity, enrolled_count, description, equipment_required in classes;
package_id, package_name, duration, price, included_classes, additional_benefits in packages

_Conversation History_
User: Nice to meet you Dana.
Agent: Nice to meet you too! How can I assist you today?
User: What sort of insights can you provide me about the members?

_Output_
```json
{{
  "thought": "The user is asking for general insights based on the members table."
  "table": "members",
  "columns": ["*"]
}}
```

_Conversation History_
User: How many different classes are available?
Agent: We currently have 63 unique classes. Are you looking for any specific information?
User: Is there anything going on with the class capacity over time?

_Output_
```json
{{
  "thought": "The user is looking for insights about the class capacity in relation to class_date, start_time, or end_time.",
  "table": "classes",
  "columns": ["class_capacity", "class_date", "start_time", "end_time"]
}}
```

In the following scenario, the tables and columns are:
* Tables: qualified_leads, product_launches, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in qualified_leads;
launch_id, is_secure, provenance, version, features, documentation_link in data_sources;
subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity

_Conversation History_
User: What do you like to do for fun?
Agent: As an AI assistant, I enjoy cleaning and and analyzing data.
User: Ok sure, anything interesting you can tell me about the data?

_Output_
```json
{{
  "thought": "The user is asking for general insights based on all the data.",
  "table": "*",
  "columns": ["*"]
}}
```

_Conversation History_
User: So what sorts of things can you do with the data?

_Output_
```json
{{
  "thought": "This is a generic question about the capabilities of the service rather than a request for data analysis.",
  "table": "unsure",
  "columns": []
}}
```

For our next example, suppose we have:
* Tables: Customers, Campaigns, Inventory, Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, DateRegistered, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand in Customers;
CampaignID, CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Campaigns;
ItemID, BrandName, Category, Price, StockQuantity, DateAdded, Supplier in Inventory;
PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in Promotions

_Conversation History_
User: I'm looking for some themes in the data to brainstorm new marketing ideas.
Agent: You got it! I am mostly familiar with preparing and analyzing data, but I will do my best to help.
User: Can we dig into the copy found in the creative assets?

_Output_
```json
{{
  "thought": "The user is interested in exploring the copy within the CreativeAsset column.",
  "table": "Campaigns
  "columns": ["CreativeAsset"]
}}
```

_Conversation History_
User: Let's take a look at the Inventory data.
Agent: Sure, the Inventory table contains 8823 rows and 6 columns. What would you like to know?
User: What can you tell me about this table?

_Output_
```json
{{
  "thought": "The request is quite general, but can still be roughly considered as a request for data analysis."
  "table": "Inventory",
  "columns": ["*"]
}}
```
---
Now it's your turn to figure out what the user is looking for. Please start with a concise thought followed the table and columns.
Recall that there should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

blanks_prompt = """Given the conversation history, your task is to determine which blank rows the user is referencing in the data.
To help in this task, you will also be shown some supporting data from relevant columns, along with the blank issues in the format of `blank_type: value1, value2, value3, ... valueN`
The set of possible blank types are:
  * missing - tokens that represent empty or missing data, such as 'not available', 'NaN', 'NULL', or 'unknown'
  * default - tokens used as placeholders or default values, such as 'example', 'john doe', 'lorem ipsum', or 'test'
  * null - actual null values, these are very straightforward to recognize because they will be represented as '<N/A>'

Using this information, please think deeply about how the user is addressing the blanks. The possible methods include:
  * some - referring a subset of rows based on a specific type or attribute. May also reference values in other columns to filter the blanks.
  * all - referencing all blank rows at once, aka. the entire set of blanks
  * beyond - the user is making changes that go beyond the blank rows, extending to other rows or even the entire column
  * ignore - brushing off the blanks or saying none of them are valid. This includes explicitly dismissing the blanks or simply moving onto a different topic.
  * unsure - when the user is not clear about how to deal with the blanks, such as making a non-committal response

Afterwards, please output pandas code that crafts a subset dataframe containing only the rows the user references in the final converation turn.
You have access to 'issue_df' as the full dataframe, which allows you to filter rows based on the column values if needed.
You also have access to 'all_rows' as a list of all row ids with blank values.  If the method is unsure, then return 'none' as the code.
Your entire response should be in well-formatted JSON including keys for thought (string), method (single token) and dataframe (code snippet), with no further explanations after the JSON output.

For example, 
#############
User: Are there any problems with delivery time data?
Agent: I found 4 null values in the DeliveryTime column. Here are some samples to help you compare. What should I do with them?
User: We can set them to one hour after the OrderTime

_Supporting Data_
Relevant columns: OrderTime, DeliveryTime, Address, Status
  * null - <N/A>

_Output_
```json
{{
  "thought": "The term 'them' refers to all the null values",
  "method": "all",
  "dataframe": "subset_df = issue_df[issue_df['DeliveryTime'].isnull()]"
}}
```

Agent: I found 4 missing leads and 1 default lead. What should I do with them?
User: How about we just drop the missing ones?

_Supporting Data_
Relevant columns: Score, LeadName, Company, LeadSource
  * missing - ' ', n/a
  * default - John Smith

_Output_
```json
{{
  "thought": "the user explicitly mentions the missing emails",
  "method": "some",
  "dataframe": "subset_df = issue_df[(issue_df['LeadName'].isin([' ', 'n/a']))]"
}}
```

Agent: I found 6 default values in the OrderDeliveryName column. We can update the values, remove the rows, or just ignore the problem.
User: I mean these are fine I guess, not much we can do
  
_Supporting Data_
Relevant columns: OrderDeliveryName
  * default - Jane Doe, First Last

_Output_
```json
{{
  "thought": "the user is ignoring all the default values",
  "method": "ignore",
  "dataframe": "subset_df = issue_df.loc[all_rows]"
}}
```

Agent: I found 16 missing emails, 4 null emails and 5 default emails for the sign_up_email. What should I do with them?
User: That's too bad. Can we fill them in when the username is available?

_Supporting Data_
Relevant columns: member_email, registration_date, username
  * missing - no email, do not want to share, n/a, do not have one
  * null - <N/A>
  * default - <EMAIL>

_Output_
```json
{{
  "thought": "The blank values should be interpolated when the username is not null",
  "method": "some",
  "dataframe": "subset_df = issue_df.loc[all_rows].loc[issue_df['username'].notnull(), :]"
}}
```

Agent: I found 3 null values and 4 missing values in the student_name column. Please see the table for more details.
User: Actually, we should just delete any rows where the student doesn't have an assigned seat.

_Supporting Data_
Relevant columns: student_name, assigned_seat, grade
  * null - <N/A>
  * missing - unknown

_Output_
```json
{{
  "thought": "the user is referencing the rows based on assigned seats, rather than blank values",
  "method": "beyond",
  "dataframe": "subset_df = issue_df['assigned_seat']"
}}
```

Agent: I found 36 null values and some defaults for MemberContact info. We can update the values, remove the rows, or just ignore the problem.
User: Without the contact, it's pretty worthless, so I guess we should just forget about them
  
_Supporting Data_
Relevant columns: MemberContact, MembershipStatus, JoinDate
  * null - <N/A>
  * default - (111) 123-4567, (555) 444-4444

_Output_
```json
{{
  "thought": "it is unclear whether the user wants to remove the rows or ignore them",
  "method": "unsure",
  "dataframe": "none"
}}
```

Agent: I found 6 missing dates and 4 null values in the ReferralSource column. We can update the values, remove the rows, or just ignore the problem.
User: The unknown ones can be deleted

_Supporting Data_
Relevant columns: SignUpDate, MembershipLevel, ReferralSource
  * missing - unknown, empty
  * null - <N/A>

_Output_
```json
{{
  "thought": "The user is referencing a subset of the missing sources",
  "method": "some",
  "dataframe": "subset_df = issue_df[(issue_df['ReferralSource'] == 'unknown')]"
}}
```

User: How many students have missing enrollment information?
Agent: I found 3 missing values in the Enrolled column. Please see the table for more.
User: We should just mark these as False

_Supporting Data_
Related columns: Enrolled, EnrollmentDate, CourseCompletion
  * missing - 'not enrolled'

_Output_
```json
{{
  "thought": "the term 'these' refers to all the missing values",
  "method": "all",
  "dataframe": "subset_df = issue_df[issue_df['Enrolled'] == 'not enrolled']"
}}
```

Agent: I found 4 missing values in the resolved_status column. Please see the table for details.
User: I guess those weren't resolved then

_Supporting Data_
Relevant columns: resolved_status, open_date, close_date, assigned_agent
  * missing - n/a, ' '
  * null - <N/A>

_Output_
```json
{{
  "thought": "the user has not made a clear decision",
  "method": "unsure",
  "dataframe": "none"
}}
```

Agent: I found 6 missing values in the Preferred Member column. What should I do with them?
User: Whatever, we don't even need that column. Let's delete the whole thing.

_Supporting Data_
Relevant columns: PreferredMember
  * missing - '', 'none'

_Output_
```json
{{
  "thought": "the user is referencing the entire column",
  "method": "beyond",
  "dataframe": "subset_df = issue_df['PreferredMember'])"
}}
```

Agent: I found one default value and 3 missing values in the DeliveryAddress column. We can update the values, remove the rows, or just ignore the problem.
User: The missing ones are fine as long as their email comes from diamondtravel.com

_Supporting Data_
Relevant columns: DeliveryAddress, City, State, ZipCode, EmailAddress
  * default - 123 Main Street, 123 Anywhere Lane
  * missing - none

_Output_
```json
{{
  "thought": "we can ignore the 'none' values if the email is from diamondtravel.com",
  "method": "ignore",
  "dataframe": "subset_df = issue_df[issue_df['DeliveryAddress'] == 'none' & issue_df['EmailAddress'].str.contains('diamondtravel.com')]"
}}
```

User: Are there any empty cities for the pickup locations?
Agent: I found 4 null values in the PickupCity column. Here are some samples to help you compare. What should I do with them?
User: We can set it to the same as the DropoffCity

_Supporting Data_
Relevant columns: PickupCity, DropoffCity, PickupTime, DropoffTime
  * null - <N/A>

_Output_
```json
{{
  "thought": "the generic 'it' refers to all the null values",
  "method": "all",
  "dataframe": "subset_df = issue_df[issue_df['PickupCity'].isnull()]"
}}
```
#############
Now, please think about what method the user is applying and then produce the appropriate code snippet. Unless the method is 'unsure', remember to always define 'subset_df' within the dataframe output.
{history}

_Supporting Data_
Relevant columns: {status}
{row_desc}

_Output_
"""

typos_prompt = """You are given some utterances describing the user's request to update, ignore or delete groups of similar terms.
The term on the left of each row is the current value, while the term on the right is the proposed value: current <> proposed.
Using this information, please decide which rows in particular the user is referencing. Then output which rows are being referenced.
If the user is referencing all rows at once, output 'all'. If the sitation is unclear, please output 'unsure'.
Do not add any extra text or explanations to your output. Only generate rows ids or 'all' as appropriate.

For example,
#############
Agent: The LandingLoc column has 4 groups of similar terms. The New York group contains 3 terms, the California group contains 3 terms, the Florida group contains 2 terms, and the Texas group contains 2 terms.
User: Yea, the suggestions all look good. We should update all of them.
1) New York <> New York
2) New York City <> New York
3) NYC  <> New York
4) Californa <> California
5) CA <> California
6) California <> California
7) FL <> Florida
8) Florida <> Florida
9) TX <> Texas
10) Texas <> Texas
Rows: all

Agent: The shoe_style column has two groups of similar terms. The casual group contains 2 terms and the running group contains 3 terms.
User: Oh yea, we can standardize to just 'running'
1) casual <> casual
2) causal <> casual
3) running <> running
4) runing <> running
5) run <> running
Rows: 3, 4, 5

Agent: The Purchase Type column has 7 groups of similar terms. The annual_sub group contains 3 terms, the monthly_sub group contains 3 terms, the one_time group contains 2 terms, the upgrade group contains 1 term, the renewal group contains 1 term, the bulk group contains 2 terms, and the trial group contains 2 terms.
User: So the right format is annual and monthly, not the ones with _sub
1) annual_sub <> annual_sub
2) annual <> annual_sub
3) annual_subscription <> annual_sub
4) month_sub <> monthly_sub
5) month <> monthly_sub
6) monthly <> monthly_sub
7) onetime <> one_time
8) one_time <> one_time
9) upgrade <> upgrade
10) renewal <> renewal
11) bulk_purchase <> bulk
12) bulk <> bulk
13) trial <> trial
14) free_trial <> trial
Rows: 1, 2, 3, 4, 5, 6

Agent: The channel column has 2 groups of similar terms. The Tiktok group contains 3 terms and the Spotify group contains 2 terms.
User: The Spotify group looks fine, let's just ignore that group
1) Tiktok Asia <> Tiktok
2) Tiktok <> Tiktok
3) Tiktok US <> Tiktok
4) Shopify <> Spotify
5) Spotify <> Spotify
Rows: 4, 5

Agent: The funnel column has two groups of similar terms. The add_to_cart group contains 2 terms and the visit_site group contains 2 terms.
User: Umm, let's delete the last one. Doesn't look right.
1) add_to_cart <> add_to_cart
2) addtocart <> add_to_cart
3) visit_site <> visit_site
4) visits <> visit_site
Rows: 4

Agent: The social_media column has three groups of similar terms. The Google group contains 3 terms, the Facebook group contains 4 terms, and the LinkedIn group contains 3 terms.
User: Ok, the right way is social_fb and social_li
1) search_google <> Google
2) google_search <> Google
3) Google <> Google
4) social_fb <> Facebook
5) FB <> Facebook
6) Facebook Ads <> Facebook
7) Facebook <> Facebook
8) LinkedIn <> LinkedIn
9) Linked In <> LinkedIn
10) LI <> LinkedIn
Rows: 4, 5, 6, 7, 8, 9, 10

Agent: The ReviewCycle column has one group of similar terms. This group contains [Fourth quarter, 4th quarter, Q4, fourth quarter, 4Q].
User: Ok good finds, let's fix them!
1) Fourth qurter <> Q4
2) 4th quarter <> Q4
3) Q4 <> Q4
4) fourth quarter <> Q4
5) 4Q <> Q4
Rows: all

Agent: The Campaign Source column has 8 groups of similar terms. The Seasons Greetings Seasons Listening group contains 3 terms, the Cyber Monday Sound group contains 2 terms, the New Year New Sound group contains 2 terms, the Exclusive Earphone Discounts group contains 1 term, the Best Earphone Selection of 2024 group contains 1 term, the Improved Noise Cancelling group contains 3 terms, the Black Friday Sale 40OFF group contains 3 terms, amd the Just in time for Christmas group contains 2 terms.
User: The black friday ones are good. We're running an A/B test with 3 different discounts
1) Season Greetings Season Listening <> Seasons Greetings Seasons Listening
2) Seasons Greetings Seasons Listening <> Seasons Greetings Seasons Listening
3) Season Greetings Season Listening <> Seasons Greetings Seasons Listening
4) Cyber Monday Sound Rush <> Cyber Monday Sound
5) Cyber Monday Sound <> Cyber Monday Sound
6) New Year New Sound <> New Year New Sound
7) New year new sound <> New Year New Sound
8) Exclusive Earphone Discounts <> Exclusive Earphone Discounts
9) Best Earphone Selection of 2024 <> Best Earphone Selection of 2024
10) Improved Noise-Cancelling <> Improved Noise Cancelling
11) Improved Noise Canceling <> Improved Noise Cancelling
12) Improved Noise Cancelling <> Improved Noise Cancelling
13) Black Friday Sale 40OFF <> Black Friday Sale 40OFF
14) Black Friday Sale 35OFF <> Black Friday Sale 40OFF
15) Black Friday Sale 45OFF <> Black Friday Sale 40OFF
16) Just in time for Christmas <> Just in time for Christmas
17) Just in Time for Christmas <> Just in time for Christmas
Rows: 13, 14, 15

Agent: The campaign_type column has 5 groups of similar terms. The digital group contains 3 terms, the print  group contains 1 term, the email group contains 1 term, the social group contains 3 terms, and the Recode event group contains 3 terms.
User: The correct term is actually 'event', not Recode event
1) digital <> digital
2) digitial <> digital
3) online <> digital
4) print <> print
5) email <> email
6) social <> social
7) social media <> social
8) facebook <> social
9) event <> Recode event
10) Recode event <> Recode event
11) Recode conf <> Recode event
Rows: 9, 10, 11

{history}
{row_desc}
Rows:"""

concerns_prompt = """Given the conversation history, your task is to determine the row ids the user is referencing to address the concerns in the data.
To help in this task, you will also be shown the potential issues as `Row row_id) concern_type in column1:value1, column2:value2, column3:value3, ... columnX:valueX`
The possible concern types include:
  * outlier - numeric values that are significantly different
  * anomaly - textual values that are unusual or unexpected
  * date_issue - datetime values that are incorrect or misformatted
  * loc_issue - location values that are incorrect or misformatted

Using this information, please think deeply about how the user is addressing the concerns. The possible methods include:
  * some - referring to rows of a specific type or a specific position. Could also be based on selecting rows based their values or attributes
  * all - referencing all concerns at once, aka. the entire set of concerns
  * ignore - brushing off the concerns or saying none of them are valid. This includes explicitly dismissing the concerns or simply moving onto a different topic.
  * beyond - the user is addressing more than just the concerns shown, extending to the entire column or table instead
  * unsure - when the user is not clear about how to address the concerns, such as making a non-committal response

Afterwards, please output which rows are being referenced in the final conversation turn based on the row_id.
If the user is referencing all issues at once, you can use a negative value (like -1) as a shortcut. If the user is unclear about their rows, please output an empty list.
Your entire response should be in well-formatted JSON including keys for thought (string), method (string) and rows (list of integers), with no further explanations after the JSON output.

For example,
#############
Agent: I found 4 potential outliers in the collected_fees column.
User: The positive ones all look fine actually
Row 1613) outlier in first_name:michael, last_name:syned, collected_fees:202800, repayment_schedule:monthly, background_check:pass
Row 1620) outlier in first_name:ahmed, last_name:al-ghamdi, collected_fees:200597, repayment_schedule:monthly, background_check:fail
Row 1623) outlier in first_name:paul, last_name:simon, collected_fees:-40, repayment_schedule:weekly, background_check:pass
Row 1647) outlier in first_name:lydia, last_name:riddell, collected_fees:193602, repayment_schedule:monthly, background_check:pass

_Output_
```json
{{
  "thought": "The user is ignoring the collected fees with positive values",
  "method": "ignore",
  "rows": [1613, 1623, 1647]
}}
```

Agent: There are two anomalies and two datetime issues in the ApartmentRental table.
User: Those dates should probably be deleted.
Row 304) anomaly in city:Me^silla
Row 319) anomaly in city:Pin~on Canyon
Row 331) date_issue in lease_start:01/01/2025
Row 537) date_issue in lease_start:04/01/2025

_Output_
```json
{{
  "thought": "The user is referencing all the datetime issues",
  "method": "some",
  "rows": [331, 537]
}}
```

Agent: I found 6 location issues in the Shipping Address column.
User: Can we change the la ones to Los Angeles
Row 4383) location in 1369 Crockett Street
Row 5196) location in la
Row 4092) location in 216 Costa Mesa Drive
Row 5273) location in 5004 West 6th Street
Row 5542) location in la
Row 7158) location in la

_Output_
```json
{{
  "thought" "The user is referencing the three locations with 'la'",
  "method": "some",
  "rows": [5196, 5542, 7158]
}}
```

Agent: I found 4 datetime issues and two location issues in the Booking table.
User: The check-in one before 2019 can be removed.
Row 249) datetime in CheckInTime: 2019-05-01 00:00:00
Row 401) location in DropoffLocation: San Jose
Row 514) datetime in CheckInTime: 2019-07-01 00:00:00
Row 545) datetime in CheckInTime: 2018-02-01 00:00:00
Row 395) datetime in CheckOutTime: 2019-12-01 00:00:00
Row 730) location in DropoffLocation: San Diego

_Output_
```json
{{
  "thought": "The only row with a check-in date in 2018 is row id 545",
  "method": "some",
  "rows": [545]
}}
```

Agent: There are three datetime issues the entry_verified_date column.
User: Let's change all of them to 2023
Row 15) date_issue in entry_verified_date: 2024-05-19
Row 22) date_issue in entry_verified_date: 2024-03-13
Row 23) date_issue in entry_verified_date: 2024-10-21

_Output_
```json
{{
  "thought": "The user is referencing all the rows at once",
  "method": "all",
  "rows": [-1]
}}
```

Agent: There are 4 anomalies, one location issue and three datetime issues in the Inventory table.
User: Change the dates starting with 05 into month, day, year.  So like May 16, 2023.
Row 126) anomaly in product_name:silken splendor shampoo
Row 131) anomaly in product_name:cherry blossom moisturing lotion
Row 406) date_issue in shipping_date:05162023
Row 496) anomaly in product_name:revitalizing 2-in-1 shampoo and conditioner
Row 347) date_issue in entry_verified_date:12-12-24
Row 603) location in processing_facility:False
Row 704) date_issue in shipping_date:05282023
Row 708) anomaly in product_name:antioxidant berry blast serum

_Output_
```json
{{
  "thought": "The user is referencing the shipping dates starting with 05",
  "method": "some",
  "rows": [406, 704]
}}
```

Agent: There are 5 outliers and 3 location issues in the newsletter_signups table.
User: The users younger than 5 aren't really a problem.
Row 5) outlier in Age:100
Row 13) outlier in Age:2
Row 15) loc_issue in From: Would prefer not to mention
Row 29) outlier in Age:101
Row 50) outlier in Age:3
Row 67) loc_issue in From: N/A
Row 94) outlier in Age:2

_Output_
```json
{{
  "thought": "The user is dismissing ages less than 5, which include rows 13, 50, and 94",
  "method": "ignore",
  "rows": [13, 50, 94]
}}
```

Agent: I found 6 outliers in the ResponseRate column. We can update, remove or ignore them.
User: Yea, these are definitely a problem
Row 1) outlier in ResponseRate: 0.0
Row 2) outlier in ResponseRate: low
Row 3) outlier in ResponseRate: 0.0
Row 4) outlier in ResponseRate: 0.0
Row 5) outlier in ResponseRate: high
Row 6) outlier in ResponseRate: 0.0

_Output_
```json
{{
  "thought": "It's not clear which rows the user is referencing",
  "method": "unsure",
  "rows": []
}}
```

Agent: I found three textual anomalies in the User Agent column.
User: The first and last ones can be eliminated
Row 638) anomaly in User Agent:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_0 rv:6.0; quz-PE) AppleWebKit/531.49.5 (KHTML like Gecko) Version/5.1 Safari/531.49.5
Row 645) anomaly in User Agent:Opera/9.59.(Windows NT 6.0; br-FR) Presto/2.9.162 Version/10.09.1 (KHTML like Gecko) Version/5.0.1 Safari/531.27.1
Row 687) anomaly in User Agent:Chrome/14.2 (iPhone OS 10_3_3 like Mac OS X) AppleWebKit/532.1 (KHTML like Gecko) CriOS/16.0.896.0 Mobile/94A624 Safari/532.1

_Output_
```json
{{
  "thought": "The rows in the first and last position are 638 and 687",
  "method": "some",
  "rows": [638, 687]
}}
```

User: So what's the most popular city then?
Agent: The most popular city is Los Altos. However, I did notice some issues in the destination column which may affect the answer.
User: Let's not worry about those.
Row 125) location in destination:gotham city
Row 166) location in destination:NYC
Row 204) location in destination:The Big Easy

_Output_
```json
{{
  "thought": "The user is dismissing all the concerns at once",
  "method": "ignore",
  "rows": [-1]
}}
```

Agent: There are two outliers and 4 anomalies in the subscriptions table.
User: Oh right, we renamed the bronze into the basic subscription and the gold into the premium subscription.
Row 328) anomaly in subscription_type:gold
Row 333) anomaly in subscription_type:bronze
Row 459) anomaly in subscription_type:bronze
Row 491) outlier in version:3.5.1
Row 509) outlier in version:3.5.1
Row 551) anomaly in subscription_type:bronze

_Output_
```json
{{
  "thought": "The user is referencing the anomalies of different subscription types",
  "method": "some",
  "rows": [328, 333, 459, 551]
}}
```

User: Yes, please show me.
Agent: I found 4 textual anomalies in the ProductName column.
User: Can I see just the ones from April?
Row 1) anomaly in ProductName:Skin Perfecting Loose Powder, Category:Makeup, InitialQuantity: null, ReorderLevel:50
Row 2) anomaly in ProductName:Aloe Vera Face Mask for all skin types and with all natural ingredients, Category:Skin Care, InitialQuantity:600, ReorderLevel:100
Row 3) anomaly in ProductName:test name, Category:N/A, InitialQuantity:200, ReorderLevel:100
Row 4) anomaly in ProductName:Floral Fragrance Perfume, Category:Fragrance, InitialQuantity:70, ReorderLevel:300

_Output_
```json
{{
  "thought": "There doesn't seem to be any column value related to April",
  "method": "unsure",
  "rows": []
}}
```

Agent: I found 4 outliers in the size (inches) column.
User: The fourth one should actually be 43 inches
Row 368) outlier in size (inches):0.25
Row 392) outlier in size (inches):0.24
Row 398) outlier in size (inches):0.25
Row 407) outlier in size (inches):-43

_Output_
```json
{{
  "thought": "The row in the fourth position is 407",
  "method": "some",
  "rows": [407]
}}
```

User: Are there any concerns with the duration to watch out for?
Agent: I found 4 dates issues in the duration column. We can update, remove or ignore them.
User: We just need to add a comma after the day. Can you do that?
Row 351) date_issue in OfferDate:June 12 1923
Row 482) date_issue in OfferDate:February
Row 564) date_issue in OfferDate:March 5 1923
Row 595) date_issue in OfferDate:August 18 1923

_Output_
```json
{{
  "thought": "The dates with include a day are rows 351, 564, and 595",
  "method": "some",
  "rows": [351, 564, 595]
}}
```
#############
Now it is your turn, please think carefully, then decide on the appropriate method and rows. Remember to only output JSON with no further text.
{history}
{row_desc}

_Output_
"""

# Agent: Most of the entries in the Promotions column are the year data type, but I found some whole type. Here are some samples of both to help compare.
# User: 19991 --> 1991
# Agent: Most of the entries in the Promotions column are the category data type, but I found some general type. Here are some samples of both to help compare.
# User: mis-typed becomes a string
# Agent: Most of the entries in the Promotions column are the currency data type, but I found some general type. Here are some samples of both to help compare.
# User: $ 34.23 is actually $34.23, but space caused an issue
# Agent: Most of the entries in the Promotions column are the email data type, but I found some general type. Here are some samples of both to help compare.
# User: bobbynight@gmail <NAME_EMAIL>

problems_prompt = """Given the conversation history, your task is to determine the row ids the user is referencing to address the problems in the data.
For our context, the term 'problems' refers to the rows belonging to a data type of subtype that are different from the majority of the column.
To help in this task, you will be given the general status of the column in the form of `Most are <sub_type>, but X are <sub_type> type`.
This is followed by a list of potential problems written as `Row row_id) <sub_type> - <value>`, which might be truncated for brevity.
The possible data types are:
  * unique - each value holds a unique meaning, with subtypes such as IDs, pre-defined categories, statuses, or boolean values
  * datetime - the values are related to dates or times, including subtypes such as months, weeks, quarters, or timestamps
  * location - the values are related to geographical locations, including subtypes such as cities, states, countries, or addresses
  * number - the values are numeric and can be used for calculations, including subtypes such a percent, currency, or decimals
  * text - the values are textual, including subtypes such as phone numbers, emails, names or general descriptions

Using this information, please think deeply about how the user is addressing the problems. The possible methods include:
  * convert - changing the rows to a different data type or sub type without affecting the content
  * update - modifying the content through calculating new values or changing the underlying text
  * delete - removing the rows that are causing the problems
  * beyond - the user is moving on to a different topic beyond the problems displayed
  * unsure - it is unclear how to proceed, such as when the user makes a non-committal response

If we are intepreting values a different type, this is a 'convert' method. If we are changing the underlying value, this is an 'update' method.
Afterwards, please output which rows are being referenced in the final conversation turn based on the row_id.
If the user is referencing all issues at once, use a negative value (like -1) to indicate this. If the user is unclear or going beyond the limits, please output an empty list.
Your entire response should be in well-formatted JSON including keys for thought (string), method (string) and rows (list of integers), with no further explanations after the JSON output.

For example,
#############
User: Those are actually dollar amounts.
Agent: Sure, I have changed 21.33 to a currency type. What should I do with the rest?
User: Oh, yea do the same for the rest
Status: Most are currency, but 4 are decimal type
Row 23) decimal - 19.53
Row 22) decimal - 14.36
Row 37) decimal - 15.01
Row 39) decimal - 16.77

_Output_
```json
{{
  "thought": "The discussion involves the currency subtype, so this is a convert method",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the name data type, but I found some general type.
User: This column is a mess, let's just delete the whole thing.
Status: Most are name, but 5 are general type
Row 196) general - JamesHemsley
Row 203) general - NONAME
Row 125) general - AshleyGreen
Row 144) general - Bobbynight
Row 158) general - skip

_Output_
```json
{{
  "thought": "The user is referencing the entire column which goes beyond the problems mentioned",
  "method": "beyond",
  "rows": []
}}
```

User: Can you show me the problems?
Agent: Yes, I found 3 problems in the DropoffTime column. We can update the values, remove the rows, or just ignore the problem.
User: ok, timestamp sounds correct
Status: Most are timestamp, but 3 are date type
Row 122) date - 07-29-2021
Row 123) date - 09-02-2021
Row 124) date - 08-13-2021

_Output_
```json
{{
  "thought": "The user is requesting to convert all the rows to timestamp type",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: The AttendedEvent column is mostly boolean data type, but I found some general text. Here are some samples of both to help compare.
User: the maybes can just be removed
Status: Most are boolean, but 17 are general type
Row 3) general - maybe
Row 17) general - not sure
Row 32) general - maybe
Row 23) general - maybe
Row 35) general - not sure
Row 36) general - maybe
Row 37) general - maybe
Row 41) general - later
[9 more rows ...]

_Output_
```json
{{
  "thought": "The user wants to delete the rows where the text is 'maybe'",
  "method": "delete",
  "rows": [3, 32, 23, 36, 37]
}}
```

User: Please show me.
Agent: The zip_code column is mostly zip codes, but I found some whole numbers. We can update the values, remove the rows, or just ignore the problem.
User: We can change the second one to 01108
Status: Most are zip code, but 5 are whole type
Row 63) whole - 0
Row 68) whole - 1108
Row 73) whole - 111111
Row 74) whole - 0
Row 125) whole - 0

_Output_
```json
{{
  "thought": "The user is changing the underlying value of the second row",
  "method": "update",
  "rows": [68]
}}
```

Agent: Most of the entries in the utm_source column are the url type, but I found some general text. Here are some samples of both to help compare.
User: uhh, these look like urls to me
Status: Most are url, but 43 are general type
Row 61) general - shopify.com
Row 62) general - soleda.ai
Row 63) general - orbitalinsights.ai
Row 64) general - microsoft.azurelake.com
Row 65) general - aws.amazon.com
Row 66) general - soleda.ai
Row 67) general - redhat.com
Row 68) general - cloud.google.com
[35 more rows ...]

_Output_
```json
{{
  "thought": "These rows should be converted to url type to match the user's expectation",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: Done. What would you like to do with the remaining issues?
User: The Thur and Fiday ones should be Thursday and Friday
Status: Most are week, but 4 are general type
251) general - Thur
162) general - Fiday
163) general - weekend
184) general - Thur

_Output_
```json
{{
  "thought": "Thur and Fiday are found in rows 251, 162, and 184",
  "method": "update",
  "rows": [251, 162, 184]
}}
```

Agent: I found 3 problems in the SendToAddress column. We can update the values, remove the rows, or just ignore them.
User: Yes, these are hard to decipher
Status: Most are email, but 3 are general type
Row 98) general - fatumremix@mirrormachines
Row 102) general - sensory_depravation@fardenstate
Row 137) general - qrion@waterfalls

_Output_
```json
{{
  "thought": "The user is unsure how to address the problems",
  "method": "unsure",
  "rows": []
}}
```

Agent: I found 4 mixed data types and 2 unsupported values in the ArrivalDate column. How would you like to proceed?
User: so the first four just need to insert slashes to become dates right
Status: Most are date, but 4 are whole type and 2 are decimal type
Row 584) whole - 08242003
Row 585) whole - 08252003
Row 587) whole - 08242003
Row 588) whole - 08242003
Row 892) unsupported - 8.24.03
Row 899) unsupported - 8.25.03

_Output_
```json
{{
  "thought": "The underlying values don't need to change, we just need to re-interpret them as dates",
  "method": "convert"
  "rows": [584, 585, 587, 588]
}}
```

Agent: The contact_info column is mostly phone numbers, but I found some general text. Here are some samples of both to help compare.
User: These are all still phone numbers, can you fix them?
Status: Most are phone, but 8 are general type
Row 1) general - +1 (650)774 -8901
Row 2) general - + 1 (650)595-2121
Row 3) general - +1 (650)494-1287
Row 4) general - +1 (880)236-1554
Row 5) general - 1 -************
Row 6) general - +1 (556)256-5686
Row 7) general - +1 (650)903-0403
[1 more row ...]

_Output_
```json
{{
  "thought": "We will need to adjust the symbols and spaces to make them phone numbers",
  "method": "update",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the city data type, but I found some state type. Here are some samples of both to help compare.
User: We can just drop the state from the city
Status: Most are city, but 3 are state type
Row 901) state - Morningside, CA
Row 921) state - Evergreen, CO
Row 935) state - Westlake, CA

_Output_
```json
{{
  "thought": "Dropping the state portion requires updating the content",
  "method": "update",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the address data type, but I found some general type. Here are some samples of both to help compare.
User: umm, can we just delete these ones?
Status: Most are address, but 3 are general type
Row 236) general - hollywood blvd
Row 725) general - circle drive
Row 802) general - west 4th avenue

_Output_
```json
{{
  "thought": "We can just delete all these problematic rows",
  "method": "delete"
  "rows": [-1]
}}
```

Agent: I found 4 problems in the SentTime column. We can update the values, remove the rows, or just ignore the problem.
User: The ones with numbers should be converted to times.
Status: Most are time, but 4 are whole type
Row 155) whole - 530
Row 125) whole - 630
Row 136) whole - missing
Row 204) whole - 615

_Output_
```json
{{
  "thought": "The rows with numbers are 155, 125, and 204, 'time' is a data type so we are converting",
  "method": "convert",
  "rows": [155, 125, 204]
}}
```
#############
Now it is your turn, please think carefully, then decide on the appropriate method and rows. Remember to only output JSON with no further text.

{history}
Status: {status}
{row_desc}

_Output_
"""
