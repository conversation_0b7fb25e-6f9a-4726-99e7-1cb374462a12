plot_prompt = """Given the conversation history and data preview, your task is to generate accurate Python code for creating a plotly express figure.
The conversation history includes multiple turns for context, but we are only interested in the request made in the user's final utterance.
The preview contains the top few rows of the likely relevant data, and should be used to determine the most appropriate columns to plot, as well as any minor data transformations that need to be applied.

Please start by considering the user's goal and the data preview to determine the type of diagram to create (eg. bar chart, line chart, scatterplot, etc.)
Then, think about the most appropriate columns to include in the figure, since not all columns are needed for every type of plot.
Finally, perform any necessary pre-processing, create a plotly express figure that takes in a dataframe object (df), and store the result in a variable named `fig`.

When generating the code, do NOT create any copies of the dataframe or modify its underlying contents. You are only using the dataframe for visualization purposes.
As such, you can consider sorting or filtering the dataframe, but you should avoid making any permanent changes.
Furthermore, use Plotly Express (px) exclusively to create the visualization. Do not use matplotlib, seaborn, or any other plotting libraries.
On a related note, you should not import any libraries (including plotly), since they have already been handled by the system.

Choose titles that are compact and distinctive, rather than long and descriptive.
  * Suppose the goal is to visualize the 7-day trailing average of the number of subscriptions starting from 2023 to the present:
    - Bad: 'Daily Subscriptions (7 Day Trailing Average) from 2023 Onwards'
    - Good: 'Trailing Average of Subscriptions'
  * Suppose the figure reports the distribution of weekly mobile visitors from United States, Canada, and Mexico:
    - Bad: 'Distribution of Mobile Visitors: US vs. Canada vs. Mexico'
    - Good: 'Mobile Visits by Country'
  * Suppose the figure compares the click-thru rate and conversion rate grouped by channel:
    - Bad: 'Click-Thru Rate and Conversion Rate Grouped by Channel'
    - Good: 'Channel Performance'
  * Suppose the chart showed the amount of signups generated in November by the top 10 sources of traffic to the newsletter signup form:
    - Bad: 'Top 10 Sources of Newsletter Signups in November'
    - Good: 'Newsletter Signup Sources'

Color is appropriate to distinguish categories or to show intensity/magnitude:
  * Avoid unnecessary color complexity. Default colors are the correct choice in most cases
  * When customr colors are needed, prefer vibrant, saturated color schemes over light themes or high-contrast options
  * Good options for sequential or continuous data include: 'viridis', 'plasma', or 'emrld'
  * For categorical data, solid colors (blue, green, red, orange, purple, etc.) are often the best choice
  * Apply color only when it adds meaningful information - it should enhance rather than clutter the figure

If the user's request is ambiguous, default to the most straightforward visualization that shows the key relationships in the data.
Your entire response should only contain well-formatted Python code and comments, without any additional text or explanations after the output.

For example,
---
_Conversation History_
User: Hey, can you pull up our newsletter data?
Agent: I've loaded the newsletter signup data. What specific metrics would you like me to check?
User: Show me the breakdown by source for Q4. I want to see which channels are actually driving our signups.

_Data Preview_
| signup_source | signup_rate |
|---------------|-------------|
| Social Media  | 0.1247      |
| Email Referral| 0.0892      |
| Direct        | 0.1634      |
| Paid Search   | 0.2156      |
| Organic       | 0.3743      |
[Truncated: showing 5 of 9 rows]

_Output_
```python
# Convert signup rate to percentage for better readability
df['signup_percent'] = df['signup_rate'] * 100
# Source breakdown shows proportional data, so a pie chart is appropriate
fig = px.pie(df, values='signup_percent', names='signup_source',
             labels={{
               'signup_percent': 'Signup Rate (%)',
               'signup_source': 'Source'
             }},
             title='Newsletter Signups')
# Format the pie chart labels to show percentages
fig.update_traces(texttemplate='%{{label}}<br>%{{value:.1f}}%', textposition='inside')
```

_Conversation History_
User: The call center team is swamped again. I think there's a pattern in the number of requests but want to confirm before we hire more reps.
Agent: What timeframe are you most concerned about?
User: Let me see the daily volume for the past 3 months.

_Data Preview_
| request_date | daily_volume | day_of_week |
|--------------|--------------|-------------|
| 2025-03-15   | 127          |  6          |
| 2025-03-16   | 89           |  7          |
| 2025-03-17   | 341          |  1          |
| 2025-03-18   | 298          |  2          |
| 2025-03-19   | 267          |  3          |
[Truncated: showing 5 of 78 rows]

_Output_
```python
# Convert day of week from numeric to text for better readability
weekday_map = {{1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday', 6: 'Saturday', 7: 'Sunday'}}
df['day_of_week'] = df['day_of_week'].map(weekday_map)

# Daily volume over time with day-of-week patterns suggests a line chart
fig = px.line(df, x='request_date', y='daily_volume',
  labels={{
    'request_date': 'Date',
    'daily_volume': 'Daily Requests'
  }},
  color='day_of_week', title='Call Center Volume')
```

_Conversation History_
User: Our SDRs keep complaining about lead quality but I'm not convinced.
Agent: What specific concerns are they raising about the leads?
User: They say we're getting tons of MQLs but booking fewer discovery calls. Give me a chart of MQLs against actual bookings so I can see if there's really a correlation issue?

_Data Preview_
| Month    | MQLs | Discovery Calls Booked |
|----------|------|------------------------|
| 2025-01  | 743  | 127                    |
| 2025-02  | 892  | 156                    |
| 2025-03  | 1,156| 189                    |
| 2025-04  | 967  | 143                    |
| 2025-05  | 1,034| 201                    |
[Truncated: showing 5 of 876 rows]

_Output_
```python
# Correlation between two metrics is best shown with a scatter plot
fig = px.scatter(df, x='MQLs', y='Discovery Calls Booked',
  labels={{
    'MQLs': 'Marketing Qualified Leads',
    'Discovery Calls Booked': 'Discovery Calls'
  }},
  title='MQL to Call Correlation')
```

_Conversation History_
User: Finance is asking about our retention metrics again for the investor deck.
Agent: Which cohorts would you like me to focus on for the retention analysis?
User: I want to active customers in each signup cohort - like how many from Jan are still active and so on

_Data Preview_
| signup_year | cohort_month | active_customers |
|-------------|--------------|------------------|
| 2023        | Oct          | 255              |
| 2023        | Nov          | 189              |
| 2023        | Dec          | 196              |
| 2024        | Jan          | 421              |
| 2024        | Feb          | 387              |
| 2024        | Mar          | 824              |
[Truncated: showing 6 of 23 rows]

_Output_
```python
# Sort by cohort month to ensure the line chart is in the correct order
df = df.sort_values('cohort_month')
# Cohort retention across different years and months needs a line chart with grouping
fig = px.line(df, x='cohort_month', y='active_customers', color='signup_year',
  labels={{
    'cohort_month': 'Cohort Month',
    'active_customers': 'Active Customers',
    'signup_year': 'Signup Year'
  }},
  title='Customer Retention')
```

_Conversation History_
User: yes, clicks over views will work
Agent: OK, I've added a column for CTR
User: Compare CTR and CPC by channel - I think Google is eating our budget with terrible performance but need the data to back it up.

_Data Preview_
| Channel      | CTR     | CPC    |
|--------------|---------|--------|
| Google Ads   | 2.1623  | 0.347  |
| LinkedIn     | 4.8134  | 0.423  |
| Facebook     | 3.2779  | 0.389  |
| Twitter      | 4.7043  | 0.214  |
| YouTube      | 5.1326  | 0.492  |

_Output_
```python
# Create subplots with secondary y-axis since the metrics are on different scales
fig = make_subplots(specs=[[ {{"secondary_y": True}} ]])
# Start with the CTR bars (primary y-axis)
fig.add_trace(
  go.Bar(x=df['Channel'], y=df['CTR'],
    name='Click-Through Rate', offsetgroup=1,
    hovertemplate='CTR: %{{y:.2f}}%<extra></extra>',
  ),
  secondary_y=False,
)
# Add CPC bars (secondary y-axis)
fig.add_trace(
  go.Bar(x=df['Channel'], y=df['CPC'], 
    name='Cost Per Click', offsetgroup=2,
    hovertemplate='CPC: $%{{y:.2f}}<extra></extra>',
  ),
  secondary_y=True,
)
# Set y-axis titles
fig.update_yaxes(title_text="Click-Through Rate (%)", secondary_y=False)
fig.update_yaxes(title_text="Cost Per Click ($)", secondary_y=True)
# Finalize layout
fig.update_layout(
  title={{'text': 'Channel Performance', 'font': {{'size': 18}}, 'x': 0.5, 'xanchor': 'center' }},
  xaxis_title='Marketing Channel',
  barmode='group', hovermode='x unified'
)
```

_Conversation History_
User: Can you show me the email metrics for each weekly batch?
Agent: You got it, how does this look?
User: As a chart please

_Data Preview_
| SendDate   | Delivered | Opened | Clicked |
|------------|-----------|--------|---------|
| 2025-02-03 | 8,743     | 2,156  | 341     |
| 2025-02-10 | 8,921     | 2,234  | 398     |
| 2025-02-17 | 8,656     | 1,987  | 287     |
| 2025-02-24 | 8,834     | 2,089  | 324     |
| 2025-03-03 | 8,772     | 2,176  | 356     |
[Truncated: showing 5 of 27 rows]

_Output_
```python
# Multiple email metrics over time work well as a multi-line chart
fig = px.line(df, x='SendDate', y=['Delivered', 'Opened', 'Clicked'],
  labels={{
    'SendDate': 'Send Date',
    'value': 'Email Count'
  }},
  title='Email Campaign Metrics')
```
---
Now it's your turn! Following the same format in the examples, please generate the plotly express code to meet the user's goal.
This is very important to my career, do not include any explanations after the code output.

_Conversation History_
{history}

_Data Preview_
{data_preview}

_Output_
"""

design_prompt = """Given the user request and supporting details, follow the thought process to generate Pandas code for updating the data.
Supporting details includes information on the part of the table being changed, a description of the change, and the method for deriving the new values.
Changes include renaming content, modifying values, or filling rows with new content based on other columns.
The dataframes you have are {df_tables}.
Please only output executable Python without any explanations. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe.

For example,
#############
<This is just a placeholder>

#############
User request: {utterance}
* Description: {description}
* Method: {method}
Thought: {thought}
Result:"""


carry_prompt = """Our goal is to visualize the data in a way that addresses the user's intent.
We currently have access to a dataframe that was generated from a previous query, but it may or may not be appropriate for our current situation.
Your task is to decide if this existing dataframe is already sufficient, or whether we should query the database for an updated dataframe instead.
To aid in your decision, you will be provided with the conversation history, the previous SQL query, and a preview of the data.

The obvious case for carrying over the previous dataframe is when the user is simply asking for a visualization of the previous turn.
The obvious case for querying the database again is when the current turn is unrelated to the previous turn.

For more nuanced cases, keep the following guidelines in mind:
  * If the user is building upon previous analysis by slightly tweaking the parameters, then it does *not* make sense to carry over the previous data
    - this constitutes an adjustment to the previous analysis, so the previous query is outdated
    - examples include changing date ranges, filters, groupings, or adding/removing conditions
  * It is acceptable to carry over a dataframe containing extraneous columns because we can simply ignore them during plotting
  * Similarly, if the user wants a different visualization type or revised chart format, then the previous dataframe can still be used
  * However, if the user asks for additional metrics, fields, or level of granularity, then we should generate a new query

Please start by considering whether the user is visualizing prior results, building upon previous analysis, or starting a new direction.
Then decide whether the previous dataframe is fully sufficient to answer the user's current request, by answering with `true` if it is enough or `false` if we should generate an updated query.
Your entire response should be in well-formatted JSON including keys for thought (string) and carry (boolean), with no further explanations after the JSON output.

For example,
---
## Scenario 1
_Previous SQL Query_
```sql
SELECT ad_source, 
  SUM(email_signups) AS prospects,
  SUM(completed_purchases) AS signups
FROM platform_aggregated
WHERE signup_timestamp >= DATE_SUB(CURRENT_DATE, INTERVAL 1 WEEK)
  AND signup_timestamp < CURRENT_DATE
GROUP BY ad_source
ORDER BY signups DESC;
```

_Data Preview_
| ad_source | signups   |
|-----------|-----------|
| Facebook  | 241       |
| Google    | 189       |
| LinkedIn  | 102       |
| Instagram | 78        |
| TikTok    | 54        |

_Conversation History_
User: Right, the signups actually uses completed purchases, the email signups are just prospects.
Agent: We had 241 signups from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Can you show this as a bar chart?

_Output_
```json
{{
  "thought": "The user is simply asking for a visualization of the previous turn, so we should just carry over the previously queried data.",
  "carry": true
}}
```

_Conversation History_
User: Right, the signups actually uses completed purchases, the email signups are just prospects.
Agent: We had 241 signups from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Can I get the number of emails sent as well?

_Output_
```json
{{
  "thought": "The user is building upon a previous analysis by asking for additional metrics, so we need to query the database again.",
  "carry": false
}}
```

_Conversation History_
User: How many conversions did we get from each of the platforms last week?
Agent: We had 241 conversions from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Show me a graph of ad spend from Google, broken down by day.

_Output_
```json
{{
  "thought": "The requested visual is for a different breakdown than the previous query.",
  "carry": false
}}
```

## Scenario 2
_Previous SQL Query_
```sql
SELECT supplier, available_units, date_added
FROM inventory 
WHERE supplier = 'warehouse - Colorado'
  AND date_added >= DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)
  AND date_added < CURRENT_DATE;
```

_Data Preview_
| supplier             | available_units | date_added   |
|----------------------|-----------------|--------------|
| warehouse - Colorado | 120             | 2025-02-15   |
| warehouse - Colorado | 85              | 2025-02-18   |
| warehouse - Colorado | 150             | 2025-02-20   |
| warehouse - Colorado | 98              | 2025-02-22   |
| warehouse - Colorado | 110             | 2025-02-25   |
[Truncated: showing 5 of 6,134 rows]

_Conversation History_
User: I'd like to see the number of units available for each item in the Colorado warehouse.
Agent: Sure, is there a specific time range you want to look back to?
User: Yea, go back 1 month

_Output_
```json
{{
  "thought": "The final turn will likely limit the results to a subset of the previous query, so we should not carry over the previous data.",
  "carry": false
}}
```

_Conversation History_
User: I'd like to see the number of units available for each item in the Colorado warehouse.
Agent: Sure, is there a specific time range you want to look back to?
User: Yea, go back 1 month
Agent: How does this look?
User: Actually, I want to see this in a pie chart, rather than a line graph

_Output_
```json
{{
  "thought": "The final turn is merely asking for a different visualization type, so we can reuse the previous data.",
  "carry": true
}}
```

## Scenario 3
_Previous SQL Query_
```sql
SELECT
  video_series, view_count, play_count,
  marketo_clicks AS conversions,
  shown_date
FROM video_analytics
JOIN marketo_clicks ON video_analytics.video_id = marketo_clicks.video_id
WHERE shown_date >= '2025-01-01';
```

_Data Preview_
| Video Series  | View Count | Play Count | Conversions | Shown Date |
|---------------|------------|------------|-------------|------------|
| Pump It Up    | 1,047      | 343        | 12          | 2025-03-15 |
| Pump It Up    | 3,482      | 762        | 28          | 2025-03-16 |
| Pump It Up    | 2,831      | 648        | 19          | 2025-03-17 |
| Pump It Up    | 2,693      | 759        | 23          | 2025-03-18 |
| Get Fit Now   | 2,104      | 851        | 31          | 2025-03-15 |
| Get Fit Now   | 2,876      | 743        | 25          | 2025-03-16 |
| Get Fit Now   | 2,812      | 758        | 22          | 2025-03-17 |
| Get Fit Now   | 2,023      | 847        | 29          | 2025-03-18 |
| 60 Second Abs | 1,534      | 249        | 8           | 2025-03-15 |
| 60 Second Abs | 997        | 252        | 7           | 2025-03-16 |
[Truncated: showing 10 of 703 rows]

_Conversation History_
User: what's the total views, plays, and conversions for each video series starting from the beginning of the year?
Agent: The Pump It Up series recieved 27,204 views followed by 'Get Fit Now' with 24,511 views. See table for more.
User: Can you break down the data by day in a line chart?
Agent: Sure, how does this look?
User: OK, this is too messy. Can we aggregate to a weekly cadence instead?

_Output_
```json
{{
  "thought": "The previous data does contain all necessary information, but at a daily level of granularity. We should generate a new query rather than performing a complex aggregation outside of SQL.",
  "carry": false
}}
```

_Conversation History_
User: what's the total view count and play count for each video series?
Agent: The Pump It Up series recieved 27,204 views followed by 'Get Fit Now' with 24,511 views. See table for more.
User: Can you break down the data by day in a line chart?
Agent: Sure, how does this look?
User: OK, this is too messy. Just focus on play counts for now.

_Output_
```json
{{
  "thought": "Although the table has more columns than necessary, we can simply ignore the view counts during plotting, so carrying over the previous data is appropriate.",
  "carry": true
}}
```

## Scenario 4
_Previous SQL Query_
```sql
SELECT Vendor, AVG(Rating) as avg_rating
FROM supplier_reviews
WHERE Rating >= 4
GROUP BY Vendor
HAVING AVG(Rating) >= 4;
```

_Data Preview_
| Vendor             | avg_rating |
|--------------------|------------|
| Florence Unlimited | 4.2        |
| Lorraine           | 4.5        |
| Jasper Design      | 4.1        |
| Tech Solutions     | 4.5        |
| Quality Parts      | 4.4        |
| Premier Supply     | 4.6        |
| Metro Materials    | 4.3        |
| Advanced Components| 4.3        |
| Elite Manufacture  | 4.4        |
| Superior Goods     | 4.6        |

_Conversation History_
User: Can you show me all the vendors which have a rating of 4 stars or higher?
Agent: Sure, Florence Unlimited, Lorraine, and Jasper Design all have a rating of at least 4 stars. See the table for more.
User: How many vendors is that?

_Output_
```json
{{
  "thought": "The user wants the count of rows in the result of the previous query, so it makes sense to carry.",
  "carry": true
}}
```

_Conversation History_
User: Can you show me all the vendors which have a rating of 4 stars or higher?
Agent: Sure, Florence Unlimited, Lorraine, and Jasper Design all have a rating of at least 4 stars. See the table for more.
User: How many vendors are there in total?

_Output_
```json
{{
  "thought": "The user wants the count of all rows, not just those with a rating of 4 stars or higher. We need to query for a new number since the scope has expanded.",
  "carry": false
}}
```
---
## Current Scenario
Now it's your turn to decide! Based on the available information, decide whether carrying over the previous data is enough to create a visualization for the user's request.

_Previous SQL Query_
```sql
{sql_query}
```

_Data Preview_
{data_preview}

_Conversation History_
{history}

_Output_
"""

validate_prompt = """The user wants to validate that all data in the {target_column} column belongs some predefined set of values.
In addition to the conversation history, you will be provided with a list of all unique values found in the column.
Your task is to group these values into the most likely valid terms, with remaining related terms following each valid term.
If certain values don't seem to fit into any group and can be seen as noise, then map them to a '<none>' category.

Please start by examining the unique values and then think out loud of about how you would group them.
Then, generate well-formatted JSON output for the groups where each key is a valid term and the corresponding value is a list of related terms.
Only terms found from the unique values can be considered a valid term key. Do not create new categories that are not present in the unique values.
Your final response should only contain valid JSON with parents keys of thought (string) and groups (dict), with no further explanations.

For example,
#############
_Conversation History_
User: We need to clean up the different plan types.
Agent: Sure, I can validate the data, remove duplicates, or standardize the format. What would you like?
User: We should make sure that they're either Free, Basic, or Pro.

_Unique Values_
['Free user', 'basic', 'standard', 'pro', 'Pro user', 'Free', 'Basic', 'free', 'free tier', 'Pro']

_Output_
```json
{{
  "thought": "User has told us the plan groupings: Free, Basic, and Pro. So we simply need to map the remaining values to these groups.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```
#############
_Conversation History_
User: We need to take care of the different user locations so that they are consistent.
Agent: Sure, how would you like to clean up the addresses?
User: Let's focus on the different zip codes for now.

_Unique Values_
['94040', '94045', '91150', '94046', '94043', '940', '94 043', '94044', '91123', 'zip', '94047', '9112 8', '94041']

_Output_
```json
{{
  "thought": "We should group by valid zip codes. There might be some OCR errors or missing digits to deal with.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```
#############
_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to cities in California at the moment, so we need to validate the city names.

_Unique Values_
['Los Angeles', 'San Jose', 'San Diego', 'Sacramento', 'Fresno', 'SJ', 'San Bernardino', 'Bay Area', 'SF', 'Bakersfield', 'Anaheim', 'LOs Angeles',
  'LA', 'san bernardino', 'san jose', 'San Francisco', 'anaheim', 'Riverside', 'riverside', 'San Fran', 'Stockton', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "We should group by city name. The most likely desired format is title case text.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```
#############
_Conversation History_
User: There should only be two valid options.
Agent: I'm sorry, could you clarify which options you're referring to?
User: Can we make sure the payment status is either paid or unpaid.

_Unique Values_
['paid', 'PAID', 'unpaid']

_Output_
```json
{{
  "thought": "The user has specified two valid options: paid and unpaid. We can easily group the remaining values accordingly.",
  "groups": {{
    "paid": ["PAID"],
    "unpaid": []
  }}
}}
```
#############
_Conversation History_
User: What are the different pages in the checkout process?
Agent: According to PageActivity, some options include Enter Credit Card, Submit Payment, and Review Order. See table for more details.
User: Yea, so Payment Submission should be under Submit Payment actually, we gotta group these together

_Unique Values_
['Enter Credit Card', 'Submit Payment', 'Add to Cart', 'Review Order', 'Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Order Confirmation', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "We know 'Submit Payment' is one valid term. We select other pages as the most likely to belong to a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.

_Conversation History_
{history}

_Unique Values_
{uniques}

_Output_
"""

validation_grouping_prompt = """Given the set of valid terms within a column, we are trying to map all remaining invalid terms to its most likely valid one.
The valid terms are values the user has confirmed are correct, while the invalid terms represent typos, variations, or other noise.
Start by examining all the unique values and consider how to map the invalid terms to the valid ones.
Then, generate well-formatted JSON output where each valid term serves as a key in the 'groups' dictionary.

Do not add or remove any new keys from the valid terms list, only map the invalid terms to the existing valid ones.
If there isn't a clear match for a term, then map it to a '<none>' category.
Your final response should only contain valid JSON including a thought (string) and groups (dict), with no further explanations after the output.

For example,
#############
_Unique Values_
Valid: ['Free', 'Basic', 'Pro']
Invalid: ['Free user', 'basic', 'standard', 'pro', 'Pro user', 'free', 'free tier']

_Output_
```json
{{
  "thought": "Standard likely belongs to basic. All the others include a valid term, making them easy to match.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```
#############
_Unique Values_
Valid: ['94040', '94045', '91150', '94046', '94043', '94044', '94047', '94041', '91123']
Invalid: ['940', '94 043', 'zip', '9112 8']

_Output_
```json
{{
  "thought": "Valid zip codes likely have five digits. Some entries have OCR errors or missing digits.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```
#############
_Unique Values_
Valid: ['Los Angeles', 'San Jose', 'San Diego', 'San Bernardino', 'San Francisco', 'Anaheim', 'Riverside', 'Bakersfield', 'Fresno', 'Stockton', 'Sacramento']
Invalid: ['SJ', 'Bay Area', 'SF', 'LOs Angeles', 'LA', 'san bernardino', 'san jose', 'anaheim', 'riverside', 'San Fran', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "The pattern is to group by city name with proper capitalization.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```
#############
_Unique Values_
Valid: ['SJC', 'SAN', 'LAX', 'SMF', 'SFO', 'FAT', 'BUR', 'LGB', 'OAK']
Invalid: ['Los Angeles', 'Sacramento', 'SJ', 'smf', 'Bay Area', 'LA', 'sjc', 'San Francisco', 'SAN Diego', 'lax', 'san']

_Output_
```json
{{
  "thought": "The pattern is to group by airport code in uppercase.",
  "groups": {{
    "SJC": ["SJ", "sjc"],
    "SAN": ["SAN Diego", "san"],
    "LAX": ["Los Angeles", "LA", "lax"],
    "SMF": ["Sacramento", "smf"],
    "SFO": ["Bay Area", "San Francisco"],
    "FAT": [],
    "BUR": [],
    "LGB": [],
    "OAK": []
  }}
}}
```
#############
_Unique Values_
Valid: ['Add to Cart', 'Enter Credit Card', 'Review Order', 'Submit Payment', 'Order Confirmation']
Invalid: ['Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "The pattern is to group the terms by the pages of a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.
For reference, here is the recent conversation history and unique values:
{history}

_Unique Values_
Valid: {valid_terms}
Invalid: {invalid_terms}

_Output_
"""

pattern_routing_prompt = """Given the conversation history and the current turn, decide whether the user is looking to fill a column using a cumulative pattern.
For our purposes, a cumulative pattern is when the formula for a given row depends on the values of previous rows above it.
In contrast, filling in a column with a constant value or a formula that only depends on values in its own row is considered an standard operation.

After reviewing the conversation and sample rows, please help determine how to proceed. Our options include:
  - standard: the user is making a standard operation in updating a column or inserting a new column
  - pattern: the user is filling a column using a cumulative pattern, but it is unclear which column they are referring to
  - ambiguous: the user's request requires clarification or the request might not actually be related to filling column contents at all

Your response should only be in well-formatted JSON including your thinking and the chosen option, without any additional text or explanations.

For example,
#############
_Conversation History_
User: I'm looking down the DeliveryDate column and I'm seeing a pattern of errors.
Agent: OK, we can look into that. What kind of pattern are you seeing?
User: Can you change every date to be one day later than what it currently is?

_Output_
```json
{{
  "thought": "Despite mentioned the word 'pattern', the user is updating date values based on the row itself, rather than the row above it.",
  "level": "standard"
}}
```

_Conversation History_
User: let's go with the outliers
Agent: I can remove the outliers, or update them to a new value. What would you like to do?
User: Let's update the outliers to be the average of the two surrounding values.

_Output_
```json
{{
  "thought": "The request is ambiguous because it does not specify where the surrounding values are located.",
  "level": "ambiguous"
}}
```
_Conversation History_
User: Let's make a new column for costs as well that sums up all the cumulative totals.
Agent: I see two columns related to costs: cost_per_click and cost_of_goods. Which one should I use?
User: I want it to hold the sum of all the costs up to that point.

_Output_
```json
{{
  "thought": "The user is looking to fill a column using a cumulative pattern, but the column to be updated is unclear.",
  "level": "pattern"
}}
```

_Conversation History_
User: You see how we have lots of blanks in the final price column?
Agent: Yes, I can fill those in with the average price. Is that what you want?
User: The taxes and discounts should be applied to every item in inventory.

_Output_
```json
{{
  "thought": "Calculating the final price depends on the taxes and discounts in its own row, rather than an accumulated price.",
  "level": "standard"
}}
```

_Conversation History_
User: I need to clean up the data in the campaign column.
Agent: What kind of cleaning are you looking to do?
User: Can we go down the list and remove any duplicate campaigns?

_Output_
```json
{{
  "thought": "The task is about removing duplicates rather than filling a column with a pattern.",
  "level": "ambiguous"
}}
```

#############
Now it's your turn to decide! Remember, the levels to choose from are 'standard', 'pattern', or 'ambiguous'.
For reference, the valid tables and columns are:
{valid_cols}

_Conversation History_
{history}

_Output_
"""